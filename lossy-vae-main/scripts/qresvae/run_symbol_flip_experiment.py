import sys
from pathlib import Path
import os
import pickle
import random
import copy
import torch
import torchvision.transforms.functional as tvf
from PIL import Image
import matplotlib.pyplot as plt
import math

# --- Add project root to sys.path ---
try:
    script_dir = Path(__file__).resolve().parent
except NameError: # For interactive environments like IPython
    script_dir = Path(os.getcwd()).resolve()
project_root = script_dir.parent.parent # From scripts/qresvae to lossy-vae-main
sys.path.insert(0, str(project_root))

from lvae.models.qresvae.zoo import qres34m # Changed from qres34m_lossless
from lvae.models.qresvae.model import pad_divisible_by # For padding if needed

torch.set_grad_enabled(False)

# --- Helper Functions for Bit Flipping ---
def flip_random_bit_in_bytestring(bs: bytes) -> bytes:
    if not bs:
        print("Warning: Attempted to flip bit in an empty bytestring.")
        return bs
    byte_array = bytearray(bs)
    if not byte_array:
        print("Warning: byte_array is empty, cannot flip bit.")
        return bs
    byte_idx = random.randrange(len(byte_array))
    bit_idx = random.randrange(8)
    byte_array[byte_idx] ^= (1 << bit_idx)
    return bytes(byte_array)

def get_bit_streams_from_compressed_obj(comp_obj):
    """
    Extracts the 12 individual byte streams for the lossy qres34m model.
    Assumes comp_obj is a list of 13 elements where:
    - Elements 0-11 are lists, each containing a single byte string (coded data).
    - Element 12 is metadata (e.g., a shape tuple).
    Returns a flat list of these 12 byte strings.
    """
    actual_streams = []
    if not (isinstance(comp_obj, list) and len(comp_obj) == 13):
        print(f"Error get_bit_streams: comp_obj is not a list of 13 elements as expected. Found {len(comp_obj) if isinstance(comp_obj, list) else type(comp_obj)}.")
        # Return 12 empty placeholders to allow experiment to potentially identify this issue
        return [b''] * 12 

    num_data_streams = 12
    for i in range(num_data_streams):
        top_element = comp_obj[i]
        if isinstance(top_element, list) and len(top_element) == 1 and isinstance(top_element[0], bytes):
            actual_streams.append(top_element[0])
        else:
            print(f"Warning get_bit_streams: Element comp_obj[{i}] was not [bytes]. Type: {type(top_element)}. Appending placeholder.")
            actual_streams.append(b'') # Add empty bytes as placeholder
    
    # Element comp_obj[12] is metadata and is ignored for stream extraction.

    if len(actual_streams) != num_data_streams:
        # This should ideally not be reached if the loop and placeholder logic is correct
        print(f"Critical Warning get_bit_streams: Extracted {len(actual_streams)} streams, expected {num_data_streams}. This indicates a bug.")
        # Pad/truncate to ensure 12 streams are returned to avoid breaking downstream code immediately
        if len(actual_streams) < num_data_streams:
            actual_streams.extend([b''] * (num_data_streams - len(actual_streams)))
        else:
            actual_streams = actual_streams[:num_data_streams]
            
    return actual_streams

def insert_modified_streams_into_compressed_obj(original_comp_obj, flat_modified_streams):
    """
    Inserts a flat list of 12 modified byte streams back into the lossy model's compressed object structure.
    original_comp_obj is expected to be a list of 13 elements.
    Elements 0-11 (data) will be replaced with [modified_stream].
    Element 12 (metadata) will be preserved.
    """
    if not (isinstance(original_comp_obj, list) and len(original_comp_obj) == 13):
        print(f"Error insert_modified_streams: Original comp_obj is not a list of 13 elements. Found {len(original_comp_obj) if isinstance(original_comp_obj, list) else type(original_comp_obj)}.")
        # Fallback: try to create a structure, though it's likely to be incorrect
        new_obj_content = [[s] for s in flat_modified_streams[:12]]
        if len(original_comp_obj) > 12 : # try to preserve last element if possible
             return new_obj_content + [copy.deepcopy(original_comp_obj[12])]
        elif len(flat_modified_streams) > 11: # if original_comp_obj was too short, this is also problematic
             return new_obj_content # only 12 elements now
        return new_obj_content

    if len(flat_modified_streams) != 12:
        print(f"Warning insert_modified_streams: Expected 12 modified streams, got {len(flat_modified_streams)}. Will pad/truncate.")
        if len(flat_modified_streams) < 12:
            flat_modified_streams.extend([b''] * (12 - len(flat_modified_streams)))
        else:
            flat_modified_streams = flat_modified_streams[:12]

    new_comp_obj = [None] * 13 # Create a new list of 13 elements

    # Insert modified streams wrapped in lists for elements 0-11
    for i in range(12):
        new_comp_obj[i] = [flat_modified_streams[i]]

    # Deepcopy metadata from original_comp_obj[12] to new_comp_obj[12]
    new_comp_obj[12] = copy.deepcopy(original_comp_obj[12])
    
    return new_comp_obj

def main():
    # --- Configuration ---
    # Using a lossy model, lambda can be, e.g., 16, 32, 64, 128, 256, 512, 1024, 2048
    # Lower lambda = higher bpp, higher quality. Higher lambda = lower bpp, lower quality.
    compression_lambda = 1024 
    image_path_str = '../../images/collie128.png' 
    runs_dir_str = '../../runs/' 
    output_comparison_image_name = f'lossy_bit_flip_lmb{compression_lambda}.png'

    # Resolve paths based on the script's location
    image_path = script_dir / image_path_str
    runs_dir = script_dir / runs_dir_str
    os.makedirs(runs_dir, exist_ok=True) 
    original_bits_path = runs_dir / f"original_image_lossy_lmb{compression_lambda}.bits"
    comparison_output_path = runs_dir / output_comparison_image_name
    
    print(f"Using image: {image_path}")
    print(f"Saving/loading bits in: {runs_dir}")
    print(f"Using LOSSY model qres34m with lambda = {compression_lambda}")

    # --- 1. Load Model ---
    print(f"Loading qres34m (lossy) model with lambda={compression_lambda}...")
    model = qres34m(lmb=compression_lambda, pretrained=True) # Changed to lossy model
    model.eval()
    model.compress_mode(True) 
    print("Model loaded.")

    # --- 2. Load and Preprocess Image ---
    if not image_path.exists():
        print(f"ERROR: Image not found at {image_path}")
        return
        
    img_pil = Image.open(image_path).convert('RGB') 
    max_stride = 64 # from zoo.py for qres34m
    img_pil_padded = pad_divisible_by(img_pil, div=max_stride)
    im_tensor_unsqueeze = tvf.to_tensor(img_pil_padded).unsqueeze_(0)
    print(f"Image loaded and preprocessed. Original size: {img_pil.size}, Padded size for model: {img_pil_padded.size}")

    # --- 3. Compress Original Image ---
    print("Compressing original image with lossy model...")
    original_compressed_obj = model.compress(im_tensor_unsqueeze)
    with open(original_bits_path, 'wb') as f:
        pickle.dump(original_compressed_obj, file=f)
    print(f"Original lossy compressed object saved to {original_bits_path}")

    # --- Print detailed structure of compressed_obj (for lossy model) ---
    print("\\n--- Detailed Structure of original_compressed_obj (Lossy Model) ---")
    # No specific length check here anymore to allow printing of any length list for diagnosis
    if isinstance(original_compressed_obj, list):
        print(f"original_compressed_obj is a list with {len(original_compressed_obj)} elements.")
        for idx, top_element in enumerate(original_compressed_obj):
            print(f"  Element {idx}: Type: {type(top_element)}")
            if isinstance(top_element, list):
                print(f"    Element {idx} is a list with {len(top_element)} sub-elements.")
                for sub_idx, sub_element in enumerate(top_element):
                    sub_element_type = type(sub_element)
                    sub_element_len = len(sub_element) if isinstance(sub_element, (bytes, list, str, dict, tuple)) else 'N/A' # Added tuple for length
                    print(f"      Sub-element {sub_idx}: Type: {sub_element_type}, Length/Value: {sub_element_len if isinstance(sub_element, (bytes,list,tuple)) else str(sub_element)[:100]}")
                    if isinstance(sub_element, bytes):
                        print(f"        (Bytes content snippet: {sub_element[:10]}...{sub_element[-10:] if len(sub_element) > 20 else sub_element[10:]})")
            elif isinstance(top_element, bytes):
                print(f"    Element {idx} is bytes. Length: {len(top_element)}")
                print(f"      (Bytes content snippet: {top_element[:20]}...{top_element[-20:] if len(top_element) > 40 else top_element[20:]})")
            elif isinstance(top_element, tuple):
                 print(f"    Element {idx} is a tuple with {len(top_element)} items (likely metadata).")
                 for tuple_idx, tuple_item in enumerate(top_element):
                     print(f"      Tuple item {tuple_idx}: Type: {type(tuple_item)}, Value: {str(tuple_item)[:100]}")
            elif isinstance(top_element, (int, float, str, bool, dict)):
                 print(f"    Element {idx} value (first 200 chars): {str(top_element)[:200]}")
    else:
        print(f"original_compressed_obj (Lossy Model) is not a list: Type {type(original_compressed_obj)}")
    print("--- End of Detailed Structure (Lossy Model) ---")

    # --- 4. Decompress Original (for baseline) ---
    print("Decompressing original (lossy) image (no errors)...")
    im_hat_original = model.decompress(original_compressed_obj)
    im_hat_original_cropped = im_hat_original[:, :, :img_pil.height, :img_pil.width]
    im_hat_original_squeezed = im_hat_original_cropped.squeeze(0).cpu()
    print("Original (lossy) image decompressed.")
    print("NOTE: For lossy compression, the 'Reconstructed (No Error)' image will differ from the original.")

    # --- 5. Bit Flip Experiments ---
    all_byte_streams = get_bit_streams_from_compressed_obj(original_compressed_obj)
    num_target_streams = len(all_byte_streams) # Should be 12 for qres34m

    if num_target_streams != 12:
        print(f"Error: Expected 12 target byte streams for qres34m, but found {num_target_streams}. Aborting experiment.")
        # Check if placeholders were returned due to parsing issues
        if all(s == b'' for s in all_byte_streams) and len(all_byte_streams) == 12:
             print("  All extracted streams are placeholders. Check warnings from get_bit_streams_from_compressed_obj.")
        return
    
    print(f"Successfully extracted {num_target_streams} target byte streams for flipping.")

    # --- Modified Experiment: Flip 1 bit in each of the 12 streams individually ---
    flipped_images_list = []
    experiment_log_details = []
    experiment_titles = [] # To store titles for each flipped image

    if num_target_streams != 12:
        print(f"CRITICAL: num_target_streams is {num_target_streams}, but experiment logic expects 12. Aborting.")
        return

    for stream_idx_to_flip in range(num_target_streams): # Iterate 0 to 11
        exp_name = f"FlipStream{stream_idx_to_flip}"
        experiment_titles.append(f"Flipped Stream {stream_idx_to_flip}")

        print(f"\\nRunning Experiment: {exp_name} - Flipping bit in stream at index {stream_idx_to_flip}")

        # Important: Make a fresh deepcopy of the original streams for each experiment run
        # to ensure bit flips are isolated to that specific run.
        current_all_streams_copy = [copy.deepcopy(s) for s in all_byte_streams]
        
        original_selected_stream = current_all_streams_copy[stream_idx_to_flip]

        if not original_selected_stream:
            print(f"  Selected stream at index {stream_idx_to_flip} is empty. Placeholder issue or empty stream? Skipping flip for this stream.")
            # Add a placeholder image or handle as error
            from PIL import ImageDraw
            error_img_pil = Image.new("RGB", img_pil.size, (230, 230, 230))
            draw = ImageDraw.Draw(error_img_pil)
            draw.text((10,10), f"Stream {stream_idx_to_flip}\\nEmpty/Skipped", fill=(0,0,0))
            flipped_images_list.append(tvf.to_tensor(error_img_pil)) # Convert PIL to tensor for consistency if needed, or keep as PIL
            experiment_log_details.append(f"Experiment '{exp_name}': SKIPPED - Targeted stream {stream_idx_to_flip} was empty.")
            continue
            
        print(f"  Targeting stream for flip. Index: {stream_idx_to_flip}.")
        print(f"  Original length of this stream: {len(original_selected_stream)} bytes.")

        modified_selected_stream = flip_random_bit_in_bytestring(original_selected_stream)
        current_all_streams_copy[stream_idx_to_flip] = modified_selected_stream
        
        log_detail = (f"Experiment '{exp_name}': Flipped bit in stream at index {stream_idx_to_flip}. "
                      f"Original length {len(original_selected_stream)} bytes.")
        print(f"  {log_detail}")
        
        # Create the modified compressed object using the specifically altered stream list
        temp_compressed_obj = insert_modified_streams_into_compressed_obj(original_compressed_obj, current_all_streams_copy)
        
        temp_bits_path = runs_dir / f"temp_flipped_stream{stream_idx_to_flip}_lmb{compression_lambda}.bits"
        with open(temp_bits_path, 'wb') as f_temp:
            pickle.dump(temp_compressed_obj, file=f_temp)

        with open(temp_bits_path, 'rb') as f_temp:
            loaded_flipped_obj = pickle.load(file=f_temp)
        
        im_hat_flipped_tensor = None
        try:
            decompressed_flipped = model.decompress(loaded_flipped_obj)
            decompressed_flipped_cropped = decompressed_flipped[:, :, :img_pil.height, :img_pil.width]
            im_hat_flipped_tensor = decompressed_flipped_cropped.squeeze(0).cpu()
            print(f"  Successfully decompressed after bit flip in stream {stream_idx_to_flip}.")
            experiment_log_details.append(log_detail + " - Decompressed successfully.")
        except Exception as e:
            print(f"  ERROR decompressing after bit flip in stream {stream_idx_to_flip}: {e}")
            error_img_tensor = torch.zeros_like(im_hat_original_squeezed) 
            im_hat_flipped_tensor = error_img_tensor
            experiment_log_details.append(log_detail + f" - DECOMPRESSION FAILED: {e}")
        
        flipped_images_list.append(im_hat_flipped_tensor)

    # --- 6. Display and Save Results ---
    print("\\nDisplaying results...")
    print(f"NOTE: Using LOSSY model (lambda={compression_lambda}). Perfect reconstruction is NOT expected.")
    
    display_original_pil = img_pil

    # We have: Original, Reconstructed_No_Error, Flipped_Stream_0, ..., Flipped_Stream_11
    # Total 2 + 12 = 14 images
    titles = ["Original Image", f"Reconstructed (Lossy lmb{compression_lambda}, No Error)"] + experiment_titles
    images_to_display_pil = [display_original_pil, tvf.to_pil_image(im_hat_original_squeezed)]
    
    for flipped_tensor in flipped_images_list: # flipped_images_list now contains tensors or None
        if flipped_tensor is not None:
            images_to_display_pil.append(tvf.to_pil_image(flipped_tensor))
        # If a stream was skipped and a placeholder PIL was added, it would be handled here if we appended PILs directly.
        # Current logic appends tensors, so tvf.to_pil_image handles it.
        # If a None was appended due to an unforeseen issue, we might need a more robust placeholder here.
        # For now, assuming flipped_images_list contains 12 tensors (some might be black error images).

    num_images = len(images_to_display_pil)
    # Aim for a layout that shows all 14 images, e.g., 3 rows for ~5 images each, or 4 rows
    if num_images <= 5:   cols = num_images
    elif num_images <= 10:  cols = 5
    elif num_images <= 15: cols = 5 # For 14 images, 5 cols -> 3 rows (5,5,4)
    else: cols = 6 # Fallback for more images
    rows = math.ceil(num_images / cols)
    
    fig, axes = plt.subplots(rows, cols, figsize=(cols * 3, rows * 3)) # Adjusted figsize slightly
    if num_images == 1: axes = [axes] 
    axes = axes.flatten() 

    for i, img_pil_disp in enumerate(images_to_display_pil):
        if i < len(axes):
            axes[i].imshow(img_pil_disp)
            axes[i].set_title(titles[i] if i < len(titles) else f"Img {i}")
            axes[i].axis('off')
    
    for j in range(i + 1, len(axes)): 
        axes[j].axis('off')

    plt.tight_layout()
    plt.savefig(comparison_output_path)
    print(f"Comparison image saved to {comparison_output_path}")
    plt.show()

    print("\\n--- Experiment Log (Lossy Model) ---")
    for log_entry in experiment_log_details:
        print(log_entry)
    print("--- End of Experiment ---")

if __name__ == '__main__':
    main() 