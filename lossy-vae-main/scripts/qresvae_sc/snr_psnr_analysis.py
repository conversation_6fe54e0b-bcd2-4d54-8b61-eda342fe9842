#!/usr/bin/env python3
"""
SNR vs PSNR 分析脚本
用于生成论文图表：在相似BPP条件下比较不同压缩方法在不同SNR下的PSNR性能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any
import json

# 添加项目路径
sys.path.append('/home/<USER>/LOSSY_VAE/lossy-vae-main')

from compression_comparison import CompressionComparator


def run_snr_psnr_analysis(image_path: str, baseline_method: str = "VAE",
                         snr_range: List[float] = None, output_dir: str = "snr_analysis_results"):
    """运行SNR vs PSNR分析 - 以指定方法为基准进行比较

    Args:
        image_path: 测试图像路径
        baseline_method: 基准方法名称 (默认为VAE)
        snr_range: SNR测试范围
        output_dir: 输出目录
    """
    if snr_range is None:
        snr_range = list(range(0, 16, 2))  # 0到16，间隔为5
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print("="*80)
    print("SNR vs PSNR 分析 - 论文图表生成")
    print("="*80)
    print(f"测试图像: {image_path}")
    print(f"基准方法: {baseline_method}")
    print(f"SNR范围: {snr_range}")
    print(f"输出目录: {output_dir}")
    print("="*80)
    
    # 第一步：在SNR=20时找到接近目标BPP的配置
    print("\n第一步: 寻找最佳压缩配置...")
    # 初始化VAE模型
    try:
        # 强制重新加载模块以避免缓存问题
        import importlib
        import sys
        if 'lvae.models.qrvesvae_sc.zoo' in sys.modules:
            importlib.reload(sys.modules['lvae.models.qrvesvae_sc.zoo'])

        from lvae.models.qrvesvae_sc.zoo import qres34m_sc
        model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc_292/best.pt"
        vae_model = qres34m_sc(pretrained=model_path, snr_db=20.0)
        vae_model.eval()
        vae_model.compress_mode(True)
        print("VAE模型加载成功")
        baseline_comparator = CompressionComparator(model_path=model_path, snr_db=20.0)
        baseline_comparator.model = vae_model
    except Exception as e:
        print(f"VAE模型加载失败: {e}")
        # 尝试不加载预训练权重
        try:
            from lvae.models.qrvesvae_sc.zoo import qres34m_sc
            vae_model = qres34m_sc(pretrained=False, snr_db=20.0)
            vae_model.eval()
            vae_model.compress_mode(True)
            print("VAE模型（无预训练权重）加载成功")
            baseline_comparator = CompressionComparator(model_path=model_path, snr_db=20.0)
            baseline_comparator.model = vae_model
        except Exception as e2:
            print(f"VAE模型（无预训练权重）加载也失败: {e2}")
            baseline_comparator = CompressionComparator(snr_db=20.0)

    baseline_results = baseline_comparator.compare_all_methods(image_path)
    
    # 分析并选择以基准方法BPP为目标的配置
    selected_configs = find_best_configs_for_baseline(baseline_results, baseline_method)

    # 获取实际的基准BPP值
    baseline_bpp = get_baseline_bpp(selected_configs, baseline_method)

    print(f"\n选定的压缩配置 (以{baseline_method}为基准, BPP≈{baseline_bpp:.2f}):")
    for method, config in selected_configs.items():
        print(f"  {method}: BPP={config['bpp']:.3f}, 参数={config.get('params', {})}")
    
    # 第二步：对每个SNR进行测试
    print(f"\n第二步: 在不同SNR下测试性能...")
    all_results = {}
    
    for snr_db in snr_range:
        print(f"\n测试 SNR = {snr_db} dB...")
        
        # 创建新的比较器
        try:
            # 强制重新加载模块以避免缓存问题
            import importlib
            import sys
            if 'lvae.models.qrvesvae_sc.zoo' in sys.modules:
                importlib.reload(sys.modules['lvae.models.qrvesvae_sc.zoo'])

            from lvae.models.qrvesvae_sc.zoo import qres34m_sc
            model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc_292/best.pt"
            vae_model = qres34m_sc(pretrained=model_path, snr_db=snr_db)
            vae_model.eval()
            vae_model.compress_mode(True)

            comparator = CompressionComparator(model_path=model_path, snr_db=snr_db)
            comparator.model = vae_model
        except Exception as e:
            print(f"    VAE模型加载失败: {e}")
            # 尝试不加载预训练权重
            try:
                from lvae.models.qrvesvae_sc.zoo import qres34m_sc
                model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc_292/best.pt"
                vae_model = qres34m_sc(pretrained=model_path, snr_db=snr_db)
                vae_model.eval()
                vae_model.compress_mode(True)
                comparator = CompressionComparator(model_path=model_path, snr_db=snr_db)
                comparator.model = vae_model
                print(f"    VAE模型（无预训练权重）加载成功")
            except Exception as e2:
                print(f"    VAE模型（无预训练权重）加载也失败: {e2}")
                comparator = CompressionComparator(snr_db=snr_db)

        snr_results = {}
        
        for method_name, config in selected_configs.items():
            try:
                result = test_method_at_snr(comparator, method_name, config, image_path, snr_db)
                
                if 'error' not in result:
                    snr_results[method_name] = {
                        'psnr': result.get('psnr', 0),
                        'bpp': result.get('bpp', 0),
                        'snr_db': snr_db,
                        'method': method_name
                    }
                    print(f"  ✓ {method_name}: PSNR={result.get('psnr', 0):.2f}dB, BPP={result.get('bpp', 0):.3f}")
                else:
                    print(f"  ✗ {method_name}: {result['error']}")
                    
            except Exception as e:
                print(f"  ✗ {method_name}: 异常 - {str(e)}")
        
        all_results[snr_db] = snr_results
    
    # 第三步：生成图表
    print(f"\n第三步: 生成论文图表...")
    
    # 绘制主要的SNR vs PSNR图
    plot_snr_vs_psnr_paper_style(all_results, baseline_bpp, baseline_method,
                                 os.path.join(output_dir, f"snr_vs_psnr_{baseline_method.lower()}_baseline.png"))

    # 保存详细数据
    save_analysis_results(all_results, selected_configs, baseline_bpp, baseline_method,
                         os.path.join(output_dir, f"snr_analysis_data_{baseline_method.lower()}_baseline.json"))

    # 生成性能总结
    generate_performance_summary(all_results, baseline_bpp, baseline_method,
                               os.path.join(output_dir, f"performance_summary_{baseline_method.lower()}_baseline.txt"))
    
    print(f"\n分析完成！结果保存在: {output_dir}")
    return all_results


def find_best_configs_for_baseline(baseline_results: Dict[str, Any], baseline_method: str) -> Dict[str, Dict]:
    """以指定方法的BPP为基准，找到其他方法最接近基准BPP的配置"""
    configs = {}

    # 收集所有方法的BPP信息
    method_bpps = {}
    for method_name, result in baseline_results.items():
        if 'error' in result:
            continue
        method_bpps[method_name] = result.get('bpp', 0)

    # 找到基准方法的BPP
    baseline_methods = [name for name in method_bpps.keys() if baseline_method.upper() in name.upper()]
    if not baseline_methods:
        print(f"警告: 未找到{baseline_method}方法，使用第一个方法作为基准")
        baseline_bpp = list(method_bpps.values())[0] if method_bpps else 1.0
        baseline_method_name = list(method_bpps.keys())[0] if method_bpps else "Unknown"
    else:
        # 选择基准方法（通常只有一个）
        baseline_method_name = baseline_methods[0]
        baseline_bpp = method_bpps[baseline_method_name]
        print(f"以{baseline_method}为基准: {baseline_method_name}, BPP={baseline_bpp:.3f}")

        # 添加基准方法配置
        configs[f'{baseline_method}+QAM'] = {
            'bpp': baseline_bpp,
            'params': {},
            'original_name': baseline_method_name
        }

    # 为其他方法类型选择最接近基准BPP的配置
    method_types = {
        'JPEG': [name for name in method_bpps.keys() if 'JPEG' in name],
        'WebP': [name for name in method_bpps.keys() if 'WebP' in name],
        'BPG': [name for name in method_bpps.keys() if 'BPG' in name],
        'AVIF': [name for name in method_bpps.keys() if 'AVIF' in name]
    }

    for method_type, method_names in method_types.items():
        if not method_names:
            continue

        # 找到最接近基准BPP的方法
        best_method = min(method_names, key=lambda x: abs(method_bpps[x] - baseline_bpp))
        best_bpp = method_bpps[best_method]

        # 提取参数
        params = {}
        if '-' in best_method:
            try:
                quality_str = best_method.split('-')[1].split('+')[0]
                params['quality'] = int(quality_str)
            except:
                pass

        configs[f"{method_type}+LDPC+16QAM"] = {
            'bpp': best_bpp,
            'params': params,
            'original_name': best_method
        }

        print(f"  {method_type}: 选择 {best_method}, BPP={best_bpp:.3f} (与{baseline_method}差值: {abs(best_bpp - baseline_bpp):.3f})")

    return configs


def get_baseline_bpp(selected_configs: Dict[str, Dict], baseline_method: str) -> float:
    """从选定配置中获取基准方法的BPP值"""
    for method_name, config in selected_configs.items():
        if baseline_method.upper() in method_name.upper():
            return config['bpp']

    # 如果没找到，返回第一个方法的BPP
    if selected_configs:
        return list(selected_configs.values())[0]['bpp']

    return 1.0  # 默认值


def test_method_at_snr(comparator, method_name: str, config: Dict, image_path: str, snr_db: float) -> Dict[str, Any]:
    """在指定SNR下测试特定方法"""
    try:
        if 'VAE' in method_name:
            # VAE需要重新加载模型以使用新的SNR
            from lvae.models.qrvesvae_sc.zoo import qres34m_sc
            print(f"    加载VAE模型 (SNR={snr_db}dB)...")
            model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc_292/best.pt"
            model = qres34m_sc(pretrained=model_path, snr_db=snr_db)
            model.eval()
            model.compress_mode(True)

            old_model = comparator.model
            comparator.model = model
            result = comparator.compress_with_vae_qam(image_path)
            comparator.model = old_model
            
        elif 'JPEG' in method_name:
            quality = config['params'].get('quality', 75)
            result = comparator.compress_with_jpeg_ldpc_qam(image_path, quality)
            
        elif 'WebP' in method_name:
            result = comparator.compress_with_webp_ldpc_qam(image_path)
            
        elif 'BPG' in method_name:
            quality = config['params'].get('quality', 30)
            result = comparator.compress_with_bpg_ldpc_qam(image_path, quality)
            
        elif 'AVIF' in method_name:
            quality = config['params'].get('quality', 30)
            result = comparator.compress_with_avif_ldpc_qam(image_path, quality)
            
        else:
            return {'error': f'未知方法: {method_name}'}
            
        return result
        
    except Exception as e:
        return {'error': f'测试失败: {str(e)}'}


def plot_snr_vs_psnr_paper_style(all_results: Dict[float, Dict], baseline_bpp: float, baseline_method: str, save_path: str):
    """绘制论文风格的SNR vs PSNR图"""
    plt.figure(figsize=(10, 7))
    
    # 设置论文风格
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'serif',
        'axes.linewidth': 1.2,
        'grid.alpha': 0.3
    })
    
    # 准备数据
    methods_data = {}
    for snr_db, snr_results in all_results.items():
        for method_name, result in snr_results.items():
            if method_name not in methods_data:
                methods_data[method_name] = {'snr': [], 'psnr': []}
            methods_data[method_name]['snr'].append(snr_db)
            methods_data[method_name]['psnr'].append(result['psnr'])
    
    # 定义颜色和标记
    style_config = {
        'VAE+QAM': {'color': 'red', 'marker': 'o', 'linestyle': '-', 'linewidth': 2.5},
        'JPEG+LDPC+16QAM': {'color': 'blue', 'marker': 's', 'linestyle': '--', 'linewidth': 2},
        'WebP+LDPC+16QAM': {'color': 'green', 'marker': '^', 'linestyle': '-.', 'linewidth': 2},
        'BPG+LDPC+16QAM': {'color': 'orange', 'marker': 'D', 'linestyle': ':', 'linewidth': 2},
        'AVIF+LDPC+16QAM': {'color': 'purple', 'marker': 'v', 'linestyle': '-', 'linewidth': 2}
    }
    
    # 绘制曲线
    for method_name, data in methods_data.items():
        if len(data['snr']) > 1:  # 至少需要2个点才能画线
            style = style_config.get(method_name, {'color': 'black', 'marker': 'o', 'linestyle': '-', 'linewidth': 2})
            
            plt.plot(data['snr'], data['psnr'], 
                    color=style['color'], 
                    marker=style['marker'],
                    linestyle=style['linestyle'],
                    linewidth=style['linewidth'],
                    markersize=8,
                    label=method_name,
                    markerfacecolor='white',
                    markeredgewidth=2)
    
    plt.xlabel('SNR (dB)', fontsize=14, fontweight='bold')
    plt.ylabel('PSNR (dB)', fontsize=14, fontweight='bold')
    plt.title(f'Performance Comparison (Baseline: {baseline_method}, BPP ≈ {baseline_bpp:.2f})', fontsize=16, fontweight='bold')
    plt.legend(fontsize=11, loc='lower right')
    plt.grid(True, alpha=0.3)

    # 🔥 修复：动态设置X轴范围，显示所有测试的SNR值
    snr_values = list(all_results.keys())
    if snr_values:
        min_snr = min(snr_values)
        max_snr = max(snr_values)
        plt.xlim(min_snr - 1, max_snr + 1)
        print(f"图表SNR范围: {min_snr} 到 {max_snr} dB")
    else:
        plt.xlim(-1, 51)  # 默认范围

    # 添加性能信息框
    textstr = f'Baseline: {baseline_method}\nBPP: {baseline_bpp:.2f}\nTest Image: Kodim09'
    props = dict(boxstyle='round', facecolor='lightgray', alpha=0.8)
    plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
            verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"论文图表已保存: {save_path}")
    plt.show()


def save_analysis_results(all_results: Dict, configs: Dict, baseline_bpp: float, baseline_method: str, save_path: str):
    """保存分析结果到JSON文件"""
    output_data = {
        'baseline_method': baseline_method,
        'baseline_bpp': baseline_bpp,
        'selected_configs': configs,
        'snr_results': all_results,
        'analysis_info': {
            'description': f'SNR vs PSNR analysis for paper (baseline: {baseline_method})',
            'test_image': 'kodim09.png',
            'snr_range': list(all_results.keys())
        }
    }
    
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"详细数据已保存: {save_path}")


def generate_performance_summary(all_results: Dict, baseline_bpp: float, baseline_method: str, save_path: str):
    """生成性能总结报告"""
    with open(save_path, 'w', encoding='utf-8') as f:
        f.write(f"SNR vs PSNR 性能分析总结\n")
        f.write(f"基准方法: {baseline_method}\n")
        f.write(f"基准BPP: {baseline_bpp:.2f}\n")
        f.write("="*50 + "\n\n")
        
        # 计算每种方法的平均性能
        method_stats = {}
        for snr_db, snr_results in all_results.items():
            for method_name, result in snr_results.items():
                if method_name not in method_stats:
                    method_stats[method_name] = {'psnr_values': [], 'snr_values': []}
                method_stats[method_name]['psnr_values'].append(result['psnr'])
                method_stats[method_name]['snr_values'].append(snr_db)
        
        f.write("方法性能统计:\n")
        for method_name, stats in method_stats.items():
            avg_psnr = np.mean(stats['psnr_values'])
            max_psnr = np.max(stats['psnr_values'])
            min_psnr = np.min(stats['psnr_values'])
            f.write(f"{method_name}:\n")
            f.write(f"  平均PSNR: {avg_psnr:.2f} dB\n")
            f.write(f"  最高PSNR: {max_psnr:.2f} dB\n")
            f.write(f"  最低PSNR: {min_psnr:.2f} dB\n")
            f.write(f"  PSNR范围: {max_psnr - min_psnr:.2f} dB\n\n")
    
    print(f"性能总结已保存: {save_path}")


if __name__ == "__main__":
    # 配置参数
    image_path = "images/kodim09.png"
    baseline_method = "VAE"  # 以VAE为基准进行比较
    snr_range = list(range(0, 16, 2))  # 用户要求的SNR范围：0到50，间隔为5
    
    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        # 尝试其他路径
        alternative_paths = [
            "../../images/kodim09.png",
            "../images/kodim09.png"
        ]
        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                image_path = alt_path
                print(f"使用替代图像: {image_path}")
                break
        else:
            print("未找到测试图像，退出")
            sys.exit(1)
    
    # 运行分析
    print(f"\n{'='*60}")
    print(f"开始分析 (基准方法: {baseline_method})")
    print(f"{'='*60}")

    results = run_snr_psnr_analysis(
        image_path=image_path,
        baseline_method=baseline_method,
        snr_range=snr_range,
        output_dir=f"snr_analysis_{baseline_method.lower()}_baseline"
    )

    print(f"基准方法 {baseline_method} 分析完成")
    
    print(f"\n{'='*60}")
    print("所有分析完成！")
    print(f"{'='*60}")
