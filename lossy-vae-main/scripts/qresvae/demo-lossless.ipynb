{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/LOSSY_VAE/lossy-vae-main/scripts/qresvae\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/lwm_env/lib/python3.12/site-packages/compressai/models/video/google.py:353: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.\n", "  @amp.autocast(enabled=False)\n"]}], "source": ["import sys\n", "from pathlib import Path # 确保 Path 也被导入了\n", "import os\n", "# 下面的代码会把项目根目录 (lossy-vae-main) 添加到 Python 的搜索路径中\n", "# os.getcwd() 在 notebook 中运行时，通常是 notebook 文件所在的目录\n", "notebook_dir = Path(os.getcwd()).resolve()\n", "project_root = notebook_dir.parent.parent # 从 scripts/qresvae 退两级到 lossy-vae-main\n", "sys.path.insert(0, str(project_root))\n", "\n", "print(os.getcwd())\n", "import pickle\n", "from pathlib import Path\n", "from PIL import Image\n", "import torch\n", "import torchvision.transforms.functional as tvf\n", "\n", "torch.set_grad_enabled(False)\n", "\n", "from lvae.models.qresvae.zoo import qres34m_lossless"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Initialize model and load pre-trained weights"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/lwm_env/lib/python3.12/site-packages/torch/cuda/__init__.py:716: UserWarning: Can't initialize NVML\n", "  warnings.warn(\"Can't initialize NVML\")\n"]}], "source": ["model = qres34m_lossless(pretrained=True)\n", "\n", "model.eval()\n", "model.compress_mode(True)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Compress an RGB image"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["img_path = '../../images/horse3.png'\n", "\n", "im = tvf.to_tensor(Image.open(img_path)).unsqueeze_(0)\n", "compressed_obj = model.compress(im)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Save to file, compute bit rate"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Compressed file size: 2425912 bits = 9.254120 bpp\n"]}], "source": ["save_path = '../../runs/image.bits'\n", "with open(save_path, 'wb') as f:\n", "    pickle.dump(compressed_obj, file=f)\n", "\n", "total_bits = Path(save_path).stat().st_size * 8\n", "bpp = total_bits / (im.shape[2] * im.shape[3])\n", "print(f'Compressed file size: {total_bits} bits = {bpp:.6f} bpp')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Decompress and reconstruct the image"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["with open(save_path,'rb') as f:\n", "    compressed_obj = pickle.load(file=f)\n", "\n", "im_hat = model.decompress(compressed_obj).squeeze(0).cpu()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Check if the compression is lossless"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["real = tvf.pil_to_tensor(Image.open(img_path)) # uint8\n", "fake = torch.round_(im_hat * 255.0).to(dtype=torch.uint8)\n", "\n", "torch.equal(real, fake)"]}], "metadata": {"kernelspec": {"display_name": "lwm_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}