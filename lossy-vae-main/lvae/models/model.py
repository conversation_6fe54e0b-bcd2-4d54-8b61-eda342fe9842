import pickle
from collections import OrderedDict, defaultdict
from PIL import Image
import math
import numpy as np  # 确保引入numpy
import torch
import torch.nn as nn
import torch.nn.functional as tnf
import torch.distributions as td
import torchvision.transforms.functional as tvf
from compressai.entropy_models import GaussianConditional
from scipy.optimize import minimize_scalar # 引入 minimize_scalar

import lvae.models.common as common
from lvae.models.entropy_coding import gaussian_log_prob_mass


class MaxwellBoltzmannPrior(nn.Module):
    def __init__(self, lambda_init=0.5, learnable=True):
        super().__init__()
        # lambda参数控制分布形状
        self.lambda_param = nn.Parameter(torch.tensor(lambda_init)) if learnable else lambda_init
        
    def energy(self, z):
        # 计算z的能量（平方和）
        return torch.sum(z**2, dim=(1,2,3))
        
    def log_prob(self, z):
        # 计算<PERSON>-<PERSON>mann分布的对数概率
        energy = self.energy(z)
        log_prob = -self.lambda_param * energy
        
        # 计算归一化常数(近似)
        # Z = ∫exp(-λ|x|²)dx = (π/λ)^(d/2)
        # log(Z) = (d/2)log(π/λ)
        dims = np.prod(z.shape[1:])
        log_normalizer = (dims/2) * torch.log(torch.tensor(math.pi) / self.lambda_param)
        
        return log_prob - log_normalizer


class AdaptiveMaxwellBoltzmannPrior(nn.Module):
    def __init__(self, min_lambda=0.0, max_lambda=5.0, fixed_lambda=0.7, layer_name="unknown"):
        super().__init__()
        self.min_lambda = min_lambda
        self.max_lambda = max_lambda
        self.fixed_lambda = fixed_lambda
        self.layer_name = layer_name
        
        # 用于记录历史lambda值的缓冲，每个元素是(step, lambda_value)对
        self.register_buffer('lambda_history', torch.zeros(10000, 2))
        self.register_buffer('history_index', torch.tensor(0))
        self.register_buffer('global_step', torch.tensor(0)) # Will be incremented by HierarchicalVAE
        
    def energy(self, z):
        return torch.sum(z**2, dim=(1,2,3)) # Sum over C, H, W to get shape [B]
    
    def calculate_entropy(self, lambda_param, dims=None):
        """
        计算Maxwell-Boltzmann分布的熵
        H = d/2 * (1 + log(π/λ))
        
        Args:
            lambda_param: lambda参数 (Tensor)
            dims: 分布的维度数 (Python float or int)
        
        Returns:
            熵值（单位：nats） (Tensor, same shape as lambda_param or broadcastable)
        """
        if dims is None:
            # This should ideally not happen if called correctly
            print(f"Warning ({self.layer_name}): calculate_entropy called with dims=None. Using fallback.")
            dims = 1000 # Fallback, should be provided by caller based on z's actual dimensions
        
        # Ensure dims is a Python scalar for the formula
        if isinstance(dims, torch.Tensor):
            dims_val = dims.item()
        else:
            dims_val = float(dims) # Ensure it's float for division
            
        eps = 1e-9
        # lambda_param should already be a tensor. Clamp it.
        # Ensure lambda_param has the correct device and dtype if it was just created
        if not isinstance(lambda_param, torch.Tensor):
            # Try to infer device and dtype from registered buffers or parameters
            ref_tensor = self.history_index if hasattr(self, 'history_index') else next(self.parameters(), torch.tensor(0.0))
            lambda_param = torch.tensor(lambda_param, device=ref_tensor.device, dtype=ref_tensor.dtype)

        lambda_safe = torch.clamp(lambda_param, min=eps)
        
        pi_tensor = torch.tensor(math.pi, device=lambda_safe.device, dtype=lambda_safe.dtype)
        
        log_term = torch.log(pi_tensor / lambda_safe)
        # Clamp log_term to prevent inf/nan issues if pi_tensor / lambda_safe is too small or too large
        log_term = torch.clamp(log_term, min=-100.0, max=100.0) 
        
        entropy_nats = (dims_val / 2.0) * (1.0 + log_term)
        return entropy_nats
        
    def log_prob(self, z, current_snr=None, lambda_override=None):
        """
        Args:
            z (Tensor): The latent variable samples.
            current_snr (float, optional): Current SNR in dB.
            lambda_override (Tensor, optional): If provided, this lambda value overrides the fixed lambda.
        Returns:
            log_p (Tensor): Log probability of z.
            effective_lambda (Tensor): The lambda value actually used.
            entropy_nats (Tensor): The entropy of the distribution with effective_lambda.
        """
        effective_lambda = None
        if lambda_override is not None:
            # 确保lambda_override是浮点类型
            if not isinstance(lambda_override, torch.Tensor):
                effective_lambda = torch.tensor(lambda_override, device=z.device, dtype=torch.float32)
            else:
                effective_lambda = lambda_override.to(dtype=torch.float32)
                
            if self.training: # Record if override is used during training
                idx = self.history_index.item() % 10000 # Use self.history_index.item()
                self.lambda_history[idx, 0] = self.global_step.item() # Use self.global_step.item()
                # 确保存储到history之前是浮点类型
                self.lambda_history[idx, 1] = effective_lambda.detach().float().mean()
                self.history_index.fill_( (self.history_index.item() + 1) % 10000 )
        else:
            # Ensure fixed_lambda is a tensor with correct device/dtype
            if not isinstance(self.fixed_lambda, torch.Tensor):
                ref_device = z.device
                effective_lambda = torch.tensor(self.fixed_lambda, device=ref_device, dtype=torch.float32)
            else:
                effective_lambda = self.fixed_lambda.to(z.device, dtype=torch.float32) # 确保是浮点类型

        # Ensure effective_lambda is at least 0D (scalar) if it became a Python number
        if not isinstance(effective_lambda, torch.Tensor):
             effective_lambda = torch.tensor(effective_lambda, device=z.device, dtype=torch.float32)
        if effective_lambda.ndim == 0: # Ensure it can be broadcasted
             effective_lambda = effective_lambda.view(1)
             
        # 确保effective_lambda是浮点类型
        if effective_lambda.dtype != torch.float32 and effective_lambda.dtype != torch.float64:
            effective_lambda = effective_lambda.float()

        energy = self.energy(z) # Shape [B]
        
        # Ensure lambda can broadcast with energy: lambda (scalar or [B]) * energy ([B])
        if effective_lambda.numel() == 1: # Scalar lambda
            log_prob_unnormalized = -effective_lambda * energy
        elif effective_lambda.shape == energy.shape: # Batch-wise lambda
            log_prob_unnormalized = -effective_lambda * energy
        elif effective_lambda.ndim == 1 and energy.ndim == 1 and effective_lambda.shape[0] == energy.shape[0]:
             log_prob_unnormalized = -effective_lambda * energy # Should be fine
        elif effective_lambda.ndim > 1 and effective_lambda.shape[0] == energy.shape[0] and effective_lambda.numel() == energy.shape[0]:
             effective_lambda_squeezed = effective_lambda.squeeze()
             if effective_lambda_squeezed.shape == energy.shape:
                 log_prob_unnormalized = -effective_lambda_squeezed * energy
             else:
                 print(f"Warning ({self.layer_name}): Lambda shape {effective_lambda.shape} not directly broadcastable with energy shape {energy.shape}. Using mean lambda.")
                 log_prob_unnormalized = -effective_lambda.mean() * energy # Fallback
        else:
            print(f"Warning ({self.layer_name}): Lambda shape {effective_lambda.shape} not directly broadcastable with energy shape {energy.shape}. Using mean lambda.")
            log_prob_unnormalized = -effective_lambda.mean() * energy # Fallback to mean lambda

        dims = np.prod(z.shape[1:]) # C*H*W
        
        pi_tensor = torch.tensor(math.pi, device=effective_lambda.device, dtype=effective_lambda.dtype)
        # Clamp effective_lambda before division/log
        clamped_lambda = torch.clamp(effective_lambda, min=1e-9)
        log_pi_lambda = torch.log(pi_tensor / clamped_lambda)
        log_normalizer = (dims / 2.0) * log_pi_lambda
        
        current_entropy_nats = self.calculate_entropy(effective_lambda, dims) # Use the actually applied lambda
        
        return log_prob_unnormalized - log_normalizer, effective_lambda, current_entropy_nats


class GaussianNLLOutputNet(nn.Module):
    def __init__(self, conv_mean, conv_scale, bin_size=1/127.5):
        super().__init__()
        self.conv_mean  = conv_mean
        self.conv_scale = conv_scale
        self.bin_size = bin_size
        self.loss_name = 'nll'

    def forward_loss(self, feature, x_tgt):
        """ compute negative log-likelihood loss

        Args:
            feature (torch.Tensor): feature given by the top-down decoder
            x_tgt (torch.Tensor): original image
        """
        feature = feature.float()
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_logscale = tnf.softplus(p_logscale + 16) - 16 # logscale lowerbound
        log_prob = gaussian_log_prob_mass(p_mean, torch.exp(p_logscale), x_tgt, bin_size=self.bin_size)
        assert log_prob.shape == x_tgt.shape
        nll = -log_prob.mean(dim=(1,2,3)) # BCHW -> (B,)
        return nll, p_mean

    def mean(self, feature):
        p_mean = self.conv_mean(feature)
        return p_mean

    def sample(self, feature, mode='continuous', temprature=None):
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_scale = torch.exp(p_logscale)
        if temprature is not None:
            p_scale = p_scale * temprature

        if mode == 'continuous':
            samples = p_mean + p_scale * torch.randn_like(p_mean)
        elif mode == 'discrete':
            raise NotImplementedError()
        else:
            raise ValueError()
        return samples

    def update(self):
        self.discrete_gaussian = GaussianConditional(None, scale_bound=0.11)
        device = next(self.parameters()).device
        self.discrete_gaussian = self.discrete_gaussian.to(device=device)
        lower = self.discrete_gaussian.lower_bound_scale.bound.item()
        max_scale = 20
        scale_table = torch.exp(torch.linspace(math.log(lower), math.log(max_scale), steps=128))
        updated = self.discrete_gaussian.update_scale_table(scale_table)
        self.discrete_gaussian.update()

    def _preapre_codec(self, feature, x=None):
        assert not feature.requires_grad
        pm = self.conv_mean(feature)
        pm = torch.round(pm * 127.5 + 127.5) / 127.5 - 1 # workaround to make sure lossless
        plogv = self.conv_scale(feature)
        # scale (-1,1) range to (-127.5, 127.5) range
        pm = pm / self.bin_size
        plogv = plogv - math.log(self.bin_size)
        if x is not None:
            x = x / self.bin_size
        return pm, plogv, x

    def compress(self, feature, x):
        pm, plogv, x = self._preapre_codec(feature, x)
        # compress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        strings = self.discrete_gaussian.compress(x, indexes, means=pm)
        return strings

    def decompress(self, feature, strings):
        pm, plogv, _ = self._preapre_codec(feature)
        # decompress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        x_hat = self.discrete_gaussian.decompress(strings, indexes, means=pm)
        x_hat = x_hat * self.bin_size
        return x_hat


class MSEOutputNet(nn.Module):
    def __init__(self, mse_lmb):
        super().__init__()
        self.mse_lmb = float(mse_lmb)
        self.loss_name = 'mse'

    def forward_loss(self, x_hat, x_tgt):
        """ compute MSE loss

        Args:
            x_hat (torch.Tensor): reconstructed image
            x_tgt (torch.Tensor): original image
        """
        assert x_hat.shape == x_tgt.shape
        mse = tnf.mse_loss(x_hat, x_tgt, reduction='none').mean(dim=(1,2,3)) # (B,3,H,W) -> (B,)
        loss = mse * self.mse_lmb
        return loss, x_hat

    def mean(self, x_hat, temprature=None):
        return x_hat
    sample = mean


class VDBlock(nn.Module):
    """ Adapted from VDVAE (https://github.com/openai/vdvae)
    - Paper: Very Deep VAEs Generalize Autoregressive Models and Can Outperform Them on Images
    - arxiv: https://arxiv.org/abs/2011.10650
    """
    def __init__(self, in_ch, hidden_ch=None, out_ch=None, residual=True,
                 use_3x3=True, zero_last=False):
        super().__init__()
        out_ch = out_ch or in_ch
        hidden_ch = hidden_ch or round(in_ch * 0.25)
        self.in_channels = in_ch
        self.out_channels = out_ch
        self.residual = residual
        self.c1 = common.conv_k1s1(in_ch, hidden_ch)
        self.c2 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c3 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c4 = common.conv_k1s1(hidden_ch, out_ch, zero_weights=zero_last)

    def residual_scaling(self, N):
        # This residual scaling improves stability and performance with many layers
        # https://arxiv.org/pdf/2011.10650.pdf, Appendix Table 3
        self.c4.weight.data.mul_(math.sqrt(1 / N))

    def forward(self, x):
        xhat = self.c1(tnf.gelu(x))
        xhat = self.c2(tnf.gelu(xhat))
        xhat = self.c3(tnf.gelu(xhat))
        xhat = self.c4(tnf.gelu(xhat))
        out = (x + xhat) if self.residual else xhat
        return out

class VDBlockPatchDown(VDBlock):
    def __init__(self, in_ch, out_ch, down_rate=2):
        super().__init__(in_ch, residual=True)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


from timm.models.convnext import ConvNeXtBlock
class MyConvNeXtBlock(ConvNeXtBlock):
    def __init__(self, dim, mlp_ratio=2, **kwargs):
        super().__init__(dim, mlp_ratio=mlp_ratio, **kwargs)
        self.norm.affine = True # this variable is useless. just a workaround for flops computation

    def forward(self, x):
        shortcut = x
        x = self.conv_dw(x)
        if self.use_conv_mlp:
            x = self.norm(x)
            x = self.mlp(x)
        else:
            x = x.permute(0, 2, 3, 1).contiguous()
            x = self.norm(x)
            x = self.mlp(x)
            x = x.permute(0, 3, 1, 2).contiguous()
        if self.gamma is not None:
            x = x.mul(self.gamma.reshape(1, -1, 1, 1))
        x = self.drop_path(x) + shortcut
        return x

class MyConvNeXtPatchDown(MyConvNeXtBlock):
    def __init__(self, in_ch, out_ch, down_rate=2, mlp_ratio=2, kernel_size=7):
        super().__init__(in_ch, mlp_ratio=mlp_ratio, kernel_size=kernel_size)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


class STEFunction(torch.autograd.Function):
    @staticmethod
    def forward(ctx, input_for_grad, discrete_output):
        # input_for_grad: e.g., noisy_symbols (N,), complex
        # discrete_output: e.g., recovered_one_hot_iq (N, 2, 4), real
        ctx.input_shape = input_for_grad.shape
        ctx.input_dtype = input_for_grad.dtype # Save dtype
        return discrete_output

    @staticmethod
    def backward(ctx, grad_output):
        # grad_output: gradient w.r.t. discrete_output, shape (N, 2, 4), real
        # Expected gradient: for input_for_grad, shape (N,), complex

        # Heuristic to match shape and type:
        # Sum gradients from the one-hot representation.
        reduced_grad_real_part = torch.sum(grad_output, dim=(1, 2)) # Shape (N,), real

        # Construct gradient compatible with input_for_grad
        if ctx.input_dtype.is_complex:
            # Create a complex gradient with the summed real part and zero imaginary part.
            # This is a strong simplification.
            final_grad = torch.complex(reduced_grad_real_part, torch.zeros_like(reduced_grad_real_part))
        elif ctx.input_dtype == reduced_grad_real_part.dtype: # If input was real and compatible
             final_grad = reduced_grad_real_part
        else: # Fallback if types are mismatched in an unexpected way (e.g. input real float64, reduced_grad float32)
            final_grad = reduced_grad_real_part.to(ctx.input_dtype)


        # Ensure the shape is absolutely correct.
        if final_grad.shape != ctx.input_shape:
            # This case should ideally not be hit if input_shape is (N,) and reduced_grad is (N,)
            # but as a safeguard:
            print(f"Warning: STEFunction gradient shape mismatch. Got {final_grad.shape}, expected {ctx.input_shape}. Returning zeros.")
            return torch.zeros(ctx.input_shape, dtype=ctx.input_dtype, device=grad_output.device), None
            
        return final_grad, None




class BottomUpEncoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.enc_blocks = nn.ModuleList(blocks)

    def forward(self, x):
        feature = x
        enc_features = dict()
        for i, block in enumerate(self.enc_blocks):
            feature = block(feature)
            res = int(feature.shape[2])
            enc_features[res] = feature
        return enc_features


class QLatentBlockX(nn.Module):
    """ Latent block as described in the paper.
    """
    def __init__(self, width, zdim, enc_width=None, kernel_size=7, snr_db=20.0, layer_id=None, init_lambda=0.7):
        """
        Args:
            width       (int): number of feature channels
            zdim        (int): number of latent variable channels for this specific layer.
            enc_width   (int, optional): number of encoder feature channels. \\
                Defaults to `width` if not provided.
            kernel_size (int, optional): convolution kernel size. Defaults to 7.
            snr_db      (float, optional): SNR in dB for QAM channel simulation. Defaults to 20.0.
            layer_id    (str, optional): layer的唯一标识符，用于区分不同层的参数。
            init_lambda (float, optional): 初始lambda值。默认为0.7。
        """
        super().__init__()
        self.in_channels  = width
        self.out_channels = width
        self.layer_id = f"layer_{layer_id}" if layer_id is not None else f"layer_w{width}_z{zdim}"

        enc_width = enc_width or width
        hidden = int(max(width, enc_width) * 0.25)
        concat_ch = (width * 2) if enc_width is None else (width + enc_width)
        use_3x3 = (kernel_size >= 3)
        self.resnet_front = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.resnet_end   = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.posterior = VDBlock(concat_ch, hidden, zdim, residual=False, use_3x3=use_3x3) 
        self.prior     = VDBlock(width, hidden, zdim * 2, residual=False, use_3x3=use_3x3,
                                 zero_last=True)
        self.z_proj = nn.Sequential(
            common.conv_k3s1(zdim, hidden//2) if use_3x3 else common.conv_k1s1(zdim, hidden//2),
            nn.GELU(),
            common.conv_k1s1(hidden//2, width),
        )
        self.discrete_gaussian = GaussianConditional(None) 
        
        # 储存SNR参数
        self.snr_db = snr_db # This is a Python float initially
        
        # QAM星座参数
        self._qam_map_vals = torch.tensor([-15, -13, -11, -9, -7, -5, -3, -1, 1, 3, 5, 7, 9, 11, 13, 15], dtype=torch.float32)
        self._qam_normalization = torch.sqrt(torch.tensor(10.0, dtype=torch.float32))
        
        # 添加Maxwell-Boltzmann先验
        self.mb_prior = AdaptiveMaxwellBoltzmannPrior(
            min_lambda=0.0,  # 允许的最小lambda值
            max_lambda=5.0,  # 允许的最大lambda值
            fixed_lambda=init_lambda,  # 使用相同的初始值
            layer_name=self.layer_id
        )
        
        # 用于记录当前使用的lambda值
        self.register_buffer('current_lambda', torch.tensor(init_lambda))

    def residual_scaling(self, N):
        self.z_proj[2].weight.data.mul_(math.sqrt(1 / 3*N))

    def transform_prior(self, feature):
        """ prior p(z_i | z_<i) """
        feature = self.resnet_front(feature)
        pm, plogv = self.prior(feature).chunk(2, dim=1)
        # 确保plogv不包含inf或nan
        plogv = torch.clamp(plogv, min=-20.0, max=20.0)  # 防止极端值
        plogv = tnf.softplus(plogv + 2.3) - 2.3 
        # 再次检查并清理可能的nan或inf
        plogv = torch.nan_to_num(plogv, nan=0.0, posinf=20.0, neginf=-20.0)
        return feature, pm, plogv
    
    def _calculate_layer_entropy_for_lambda(self, lambda_val, z_shape_for_dims):
        """
        Helper to calculate layer entropy for a given lambda, using MB prior's logic.
        lambda_val can be a Python float or a Tensor.
        Returns entropy in BITS.
        """
        if z_shape_for_dims is None or len(z_shape_for_dims) != 4:
            print(f"Warning ({self.layer_id}): _calculate_layer_entropy_for_lambda invalid z_shape {z_shape_for_dims}")
            # Fallback, this is risky. Try to get zdim.
            zdim_fallback = self.posterior.c4.out_channels # Assuming posterior's output is zdim
            dims = zdim_fallback * 16 * 16 # Educated guess for H, W if not available
        else:
            dims = np.prod(z_shape_for_dims[1:]) # C*H*W

        # Ensure lambda_val is a tensor for AdaptiveMaxwellBoltzmannPrior.calculate_entropy
        ref_tensor = self.current_lambda # Use a registered buffer for device/dtype
        if not isinstance(lambda_val, torch.Tensor):
            lambda_tensor = torch.tensor(lambda_val, device=ref_tensor.device, dtype=ref_tensor.dtype)
        else:
            lambda_tensor = lambda_val.to(ref_tensor.device, ref_tensor.dtype)

        entropy_nats = self.mb_prior.calculate_entropy(lambda_tensor, dims)
        return entropy_nats / math.log(2) # Convert to bits

    def _soft_qam_channel(self, z_input):
        """训练时使用的软量化QAM信道近似"""
        B, C, H, W = z_input.shape
        
        # 使用当前自适应lambda，让软量化更符合Maxwell-Boltzmann分布
        # 获取当前lambda值 - 现在log_prob返回3个值，需要正确解包
        _, current_lambda, _ = self.mb_prior.log_prob(z_input, current_snr=self.snr_db)
        current_lambda_value = current_lambda.detach().mean()
        
        # 1. 重塑以便于处理
        # 假设每两个通道对应一个复数
        z_pairs = z_input.reshape(B, C//2, 2, H, W)
        
        # 2. 软量化 - 我们使用可微分的近似
        # 构建星座点
        pam_levels = self._qam_map_vals.to(z_input.device) / self._qam_normalization.to(z_input.device)
        
        # 对每个维度单独软量化
        soft_quantized = []
        for dim in range(2):  # I和Q分量
            # 提取当前维度
            z_dim = z_pairs[:, :, dim]  # [B, C//2, H, W]
            
            # 展开以便于处理
            z_flat = z_dim.reshape(-1, 1)  # [B*C//2*H*W, 1]
            
            # 计算到每个PAM电平的距离
            distances = torch.abs(z_flat - pam_levels.view(1, -1))  # [B*C//2*H*W, 4]
            
            # 使用softmax得到软权重 - 温度参数控制软硬程度
            # 将temperature与lambda关联，高lambda使量化更硬（更集中）
            temperature = 0.1 / (1 + current_lambda_value.item())  # lambda越大，温度越小，量化越硬
            weights = torch.softmax(-distances/temperature, dim=1)  # [B*C//2*H*W, 4]
            
            # 加权求和得到软量化值
            soft_level = torch.sum(weights * pam_levels.view(1, -1), dim=1)  # [B*C//2*H*W]
            
            # 重塑回原始维度
            soft_level = soft_level.reshape(B, C//2, H, W)
            soft_quantized.append(soft_level)
        
        # 构建软量化后的复数表示
        soft_real = soft_quantized[0]
        soft_imag = soft_quantized[1]
        soft_complex = torch.complex(soft_real, soft_imag)
        
        # 3. 使用通用函数添加AWGN噪声
        noisy_complex, channel_stats = self._add_awgn_noise(soft_complex, self.snr_db)
        
        # 4. 软解调 - 类似于软量化
        demod_soft_real = []
        demod_soft_imag = []
        
        for component, values in [("real", noisy_complex.real), ("imag", noisy_complex.imag)]:
            values_flat = values.reshape(-1, 1)
            distances = torch.abs(values_flat - pam_levels.view(1, -1))
            weights = torch.softmax(-distances/temperature, dim=1)
            soft_demod = torch.sum(weights * pam_levels.view(1, -1), dim=1)
            soft_demod = soft_demod.reshape(B, C//2, H, W)
            
            if component == "real":
                demod_soft_real = soft_demod
            else:
                demod_soft_imag = soft_demod
        
        # 5. 重塑回原始格式
        recovered_z = torch.cat([demod_soft_real.unsqueeze(2), demod_soft_imag.unsqueeze(2)], dim=2)
        recovered_z = recovered_z.reshape(B, C, H, W)
        
        # 计算信道统计量
        channel_metrics = {
            'snr': self.snr_db,
            'error': torch.mean(torch.abs(recovered_z - z_input)),
            'lambda': current_lambda_value,
            **channel_stats  # 添加从_add_awgn_noise获取的统计信息
        }
        
        return recovered_z, channel_metrics
    # Define QAM constellation
    def create_qam_constellation(self, M):
        n = int(np.sqrt(M))
        x_coord = np.linspace(-n+1, n-1, n)
        constellation = np.array([(x, y) for y in x_coord for x in x_coord])
        return constellation
    def _hard_quantize_to_qam(self, z_input, qam_order=256, preserve_shape=True):
        """
        硬量化到QAM星座点，确保输出形状与输入形状匹配
        
        Args:
            z_input: 输入信号，形状为[B, C, H, W]
            qam_order: QAM阶数，默认为256
            preserve_shape: 是否保持输出形状与输入形状一致，默认为True
            
        Returns:
            tuple: (量化后的符号, 符号索引, PAM电平数)
        """
        B, C, H, W = z_input.shape
        original_shape = z_input.shape
        
        # 确保通道数是偶数
        if C % 2 != 0:
            raise ValueError(f"Channel dimension must be even, got {C}")
        
        # 重塑为便于处理的形式 - 每两个通道视为一个I/Q对
        z_pairs = z_input.reshape(B, C//2, 2, H, W)
        
        # 使用缓存的PAM电平
        pam_levels_count = int(np.sqrt(qam_order))
        device = z_input.device
        
        # 缓存不同QAM阶数的PAM电平
        if not hasattr(self, '_pam_levels_cache'):
            self._pam_levels_cache = {}
        
        cache_key = f"{pam_levels_count}_{device}"
        if cache_key not in self._pam_levels_cache:
            if pam_levels_count == 16:
                pam_levels = self._qam_map_vals.to(device) / self._qam_normalization.to(device)
            else:
                max_val = 15.0 / self._qam_normalization.item()
                pam_levels = torch.linspace(-max_val, max_val, pam_levels_count, device=device)
            self._pam_levels_cache[cache_key] = pam_levels
        else:
            pam_levels = self._pam_levels_cache[cache_key]
        
        # 量化每个维度
        quantized_indices = []
        quantized_values = []
        
        for dim in range(2):  # I和Q分量
            # 提取当前维度
            z_dim = z_pairs[:, :, dim]  # [B, C//2, H, W]
            
            # 展开
            z_flat = z_dim.reshape(-1, 1)  # [B*C//2*H*W, 1]
            
            # 计算距离
            distances = torch.abs(z_flat - pam_levels.view(1, -1))  # [B*C//2*H*W, pam_levels_count]
            
            # 找到最近的PAM电平索引
            indices = torch.argmin(distances, dim=1)  # [B*C//2*H*W]
            
            # 获取量化值
            values = pam_levels[indices]
            
            # 重塑回原始维度
            indices = indices.reshape(B, C//2, H, W)
            values = values.reshape(B, C//2, H, W)
            
            quantized_indices.append(indices)
            quantized_values.append(values)
        
        # 组合I和Q分量
        i_indices = quantized_indices[0]
        q_indices = quantized_indices[1]
        i_values = quantized_values[0]
        q_values = quantized_values[1]
        
        # 创建复数表示
        complex_values = torch.complex(i_values, q_values)
        
        # 组合索引（以便存储）- 编码为单个索引
        combined_indices = i_indices * pam_levels_count + q_indices
        
        # 如果需要保持形状一致，将输出重塑为与输入相同的形状
        if preserve_shape:
            # 将复数值转换为实数对表示，与输入形状匹配
            real_part = complex_values.real  # [B, C//2, H, W]
            imag_part = complex_values.imag  # [B, C//2, H, W]
            
            # 交错排列实部和虚部，恢复原始形状
            reshaped_values = torch.zeros(original_shape, device=device)
            for i in range(C//2):
                reshaped_values[:, 2*i, :, :] = real_part[:, i, :, :]
                reshaped_values[:, 2*i+1, :, :] = imag_part[:, i, :, :]
            
            # 同样处理索引
            # 为了保持索引的一致性，我们需要将combined_indices复制到原始形状
            # 这里我们简单地将每个索引复制两次（对应I和Q）
            reshaped_indices = torch.zeros(original_shape, dtype=combined_indices.dtype, device=device)
            for i in range(C//2):
                reshaped_indices[:, 2*i, :, :] = combined_indices[:, i, :, :]
                reshaped_indices[:, 2*i+1, :, :] = combined_indices[:, i, :, :]
            
            # print(f"_hard_quantize_to_qam: input={z_input.shape}, output={reshaped_values.shape}, indices={reshaped_indices.shape}")
            return reshaped_values, reshaped_indices, pam_levels_count
        else:
            # 返回原始复数表示和索引
            print(f"_hard_quantize_to_qam: input={z_input.shape}, output={complex_values.shape}, indices={combined_indices.shape}")
            return complex_values, combined_indices, pam_levels_count

    def _demodulate_from_indices(self, symbol_indices, original_shape, pam_levels_count=16):
        """从存储的索引重建信号
        
        Args:
            symbol_indices: 符号索引
            original_shape: 原始形状 [B,C,H,W]
            pam_levels_count: PAM电平数量，默认16（对应256-QAM）
        """
        B, C, H, W = original_shape
        
        # 解析索引
        i_indices = symbol_indices // pam_levels_count
        q_indices = symbol_indices % pam_levels_count
        
        # 构建PAM电平
        if pam_levels_count == 16:
            # 使用预设的16个PAM电平（256-QAM）
            pam_levels = self._qam_map_vals.to(symbol_indices.device) / self._qam_normalization.to(symbol_indices.device)
        else:
            # 动态生成PAM电平
            max_val = 15.0 / self._qam_normalization.item()
            pam_levels = torch.linspace(-max_val, max_val, pam_levels_count, device=symbol_indices.device)
        
        # 获取量化值
        i_values = pam_levels[i_indices]
        q_values = pam_levels[q_indices]
        
        # 创建复数表示
        complex_values = torch.complex(i_values, q_values)
        
        # 转换回实数表示
        real_part = complex_values.real
        imag_part = complex_values.imag
        
        # 重塑为原始形状
        recovered = torch.cat([real_part.unsqueeze(2), imag_part.unsqueeze(2)], dim=2)
        recovered = recovered.reshape(B, C, H, W)
        
        return recovered

    def calculate_bit_size(self, z):
        """计算Z经过信道处理后的比特大小和信息熵"""
        B, C, H, W = z.shape
        num_symbols = B * (C//2) * H * W
        
        # 计算当前lambda下的熵
        lambda_val = self.current_lambda.item()
        total_dims = np.prod(z.shape[1:])  # 不包括batch维度
        entropy_bits = self._calculate_layer_entropy_for_lambda(lambda_val, z.shape)
        
        # 每个符号的平均比特数（每个符号对应C//2个维度）
        bits_per_symbol = entropy_bits / (total_dims / (C//2))
        bits_per_symbol_val = bits_per_symbol.item()
        
        # 根据每符号熵值自适应选择QAM阶数
        if bits_per_symbol_val <= 2.5:  # 熵很低，用QPSK (4-QAM)
            qam_order = 4
            bits_per_qam = 2
        elif bits_per_symbol_val <= 4.5:  # 用16-QAM
            qam_order = 16
            bits_per_qam = 4
        elif bits_per_symbol_val <= 6.5:  # 用64-QAM
            qam_order = 64
            bits_per_qam = 6
        else:  # 默认用256-QAM
            qam_order = 256
            bits_per_qam = 8
            
        # 计算使用自适应QAM时的最大比特数
        max_bits = num_symbols * bits_per_qam
        
        # 计算有效比特数 - 这个仍然是基于实际熵而非调制阶数
        effective_bits = entropy_bits * (total_dims / z.shape[1])
        
        # 如果有效比特数超过了QAM阶数允许的最大值，则截断
        if effective_bits.item() > max_bits:
            effective_bits = torch.tensor(max_bits, device=entropy_bits.device, dtype=entropy_bits.dtype)
        
        return {
            "num_symbols": num_symbols,
            "qam_order": qam_order,
            "bits_per_qam": bits_per_qam,
            "max_bits": max_bits,
            "entropy_bits_per_symbol": bits_per_symbol_val,
            "effective_bits": effective_bits.item(),
            "compression_ratio": effective_bits.item() / max_bits
        }

    def forward_train(self, feature, enc_feature, get_latents=False):
        """ Training mode. Forward pass and compute KL. """
        feature_transformed_prior, pm, plogv = self.transform_prior(feature)
        
        # 确保pv不包含nan或inf
        pv = torch.exp(plogv)
        pv = torch.clamp(pv, min=1e-6, max=1e6)  # 限制在合理范围内
        pv = torch.nan_to_num(pv, nan=1e-6, posinf=1e6, neginf=1e-6)  # 替换任何剩余的nan或inf
        
        qm = self.posterior(torch.cat([feature_transformed_prior, enc_feature], dim=1)) 
        z_sample_continuous_for_kl = qm + torch.empty_like(qm).uniform_(-0.5, 0.5) # This is z_q
        
        # --- SNR to Target Ratio to Lambda (参考解调.py) ---
        # 确保SNR是一个标量值
        if isinstance(self.snr_db, (float, int)):
            snr_db_val = self.snr_db
        else:  # 假设是tensor
            snr_db_val = self.snr_db.item()
            
        # 1. 构建256-QAM星座点
        constellation = self._create_qam_constellation()
        
        # 2. 计算目标功率比例
        target_ratio = self._calculate_target_power_ratio_for_snr(snr_db_val)
        
        # 3. 根据目标功率比例求解lambda值
        lambda_target = self.find_lambda_for_target_ratio(target_ratio)
        
        # 4. 转换为tensor用于后续计算
        lambda_from_ratio = torch.tensor(lambda_target, device=z_sample_continuous_for_kl.device, dtype=torch.float32)
        
        # 5. 计算对应的熵（用于记录）
        with torch.no_grad():
            target_total_entropy_bits = self._calculate_layer_entropy_for_lambda(
                lambda_target, 
                z_sample_continuous_for_kl.shape
            ).item()
            
        # --- SNR to Lambda转换结束 ---
        
        # 确保pm和pv不包含nan或inf
        pm = torch.nan_to_num(pm, nan=0.0, posinf=100.0, neginf=-100.0)
        
        # 在调用gaussian_log_prob_mass之前再次检查pv
        if torch.any(torch.isnan(pv)) or torch.any(torch.isinf(pv)) or torch.any(pv <= 0):
            print(f"Warning ({self.layer_id}): pv contains invalid values before gaussian_log_prob_mass. Fixing...")
            pv = torch.clamp(pv, min=1e-6, max=1e6)
            pv = torch.nan_to_num(pv, nan=1e-6, posinf=1e6, neginf=1e-6)
            
        try:
            log_prob_gaussian = gaussian_log_prob_mass(pm, pv, x=z_sample_continuous_for_kl, bin_size=1.0, prob_clamp=1e-6)
            kl_gaussian = -1.0 * log_prob_gaussian
        except AssertionError as e:
            print(f"Error in gaussian_log_prob_mass: {e}")
            print(f"pv stats: min={pv.min().item()}, max={pv.max().item()}, mean={pv.mean().item()}, has_nan={torch.isnan(pv).any().item()}")
            # 紧急修复: 使用一个安全的pv值
            safe_pv = torch.ones_like(pv) * 1.0
            log_prob_gaussian = gaussian_log_prob_mass(pm, safe_pv, x=z_sample_continuous_for_kl, bin_size=1.0, prob_clamp=1e-6)
            kl_gaussian = -1.0 * log_prob_gaussian
            
        # 计算MB prior的log_prob
        # Pass the dynamically calculated lambda_from_ratio as lambda_override
        log_prob_mb, effective_lambda_in_prior, mb_entropy_nats = self.mb_prior.log_prob(
            z_sample_continuous_for_kl,
            current_snr=snr_db_val, # Pass snr_db_val
            lambda_override=lambda_from_ratio
        )
        
        # 确保effective_lambda_in_prior是有效值
        if torch.any(torch.isnan(effective_lambda_in_prior)) or torch.any(torch.isinf(effective_lambda_in_prior)):
            print(f"Warning ({self.layer_id}): effective_lambda_in_prior contains invalid values. Using default.")
            effective_lambda_in_prior = torch.tensor(0.7, device=self.current_lambda.device, dtype=torch.float32)
            
        self.current_lambda.copy_(effective_lambda_in_prior.detach().mean()) # Record the lambda actually used
        
        # Posterior entropy for KL: E_q[log q(z|x)]. For q(z|x) = Uniform(qm-0.5, qm+0.5) over each dim,
        # its entropy is sum over dims of log(1.0) = 0 Nats, if each dim is independent.
        # Or, if q is a delta function after sampling (z_sample_continuous_for_kl), entropy is -inf.
        # The typical VAE KL divergence E_q[log q/p] assumes q is Gaussian.
        # Here, z_sample_continuous_for_kl is a single sample used for Monte Carlo estimate of E_q[-log p(z)].
        # So -log p(z_sample_continuous_for_kl) is the term.
        # The "posterior_entropy" term in KL = E_q[log q] - E_q[log p] is often handled differently.
        # If q is qm + U(-0.5,0.5), its differential entropy is D * log(1) = 0.
        # So KL for this specific q becomes - E_q[log p(z)] approx -log p(z_sample_continuous_for_kl)
        # This is what log_prob_mb represents (the -log p(z_q) part for MB prior).
        # So kl_mb = posterior_entropy - log_prob_mb
        # Let's assume posterior_entropy = 0 for U(-0.5, 0.5) per dim, as log(1^D)=0.
        posterior_entropy_nats = 0.0 
        kl_mb = posterior_entropy_nats - log_prob_mb # kl_mb is per batch item if log_prob_mb is. Shape [B]

        # 在with torch.no_grad()块中:
        with torch.no_grad():
            # 使用硬信道模拟计算真实误码率
            hard_channel_stats = self._simulate_hard_channel(z_sample_continuous_for_kl)
            symbol_errors = hard_channel_stats['symbol_error_rate']
            bit_errors = hard_channel_stats['bit_error_rate']
            
            # 仍然使用软QAM信道进行训练，因为它是可微分的
            z_channel_output, channel_metrics = self._soft_qam_channel(z_sample_continuous_for_kl)
            
            # 更新信道指标
            channel_metrics.update({
                'true_symbol_error_rate': symbol_errors,
                'true_bit_error_rate': bit_errors,
                'measured_snr_db': hard_channel_stats['measured_snr_db']
            })
        
        feature_out = feature_transformed_prior + self.z_proj(z_channel_output)
        feature_out = self.resnet_end(feature_out)
        
        # 计算比特大小信息
        bit_size_info = self.calculate_bit_size(z_sample_continuous_for_kl)
        
        # 确保所有值都是标量，避免.item()错误
        symbol_error_value = symbol_errors.item() if isinstance(symbol_errors, torch.Tensor) else float(symbol_errors)
        bit_error_value = bit_errors.item() if isinstance(bit_errors, torch.Tensor) else float(bit_errors)
        lambda_value = self.current_lambda.item() if isinstance(self.current_lambda, torch.Tensor) else float(self.current_lambda)
        effective_lambda_value = effective_lambda_in_prior.detach().mean().item() if isinstance(effective_lambda_in_prior, torch.Tensor) else float(effective_lambda_in_prior)
        lambda_from_ratio_value = lambda_from_ratio.item() if isinstance(lambda_from_ratio, torch.Tensor) else float(lambda_from_ratio)
        
        kl_dict = {
            'kl_vae': kl_gaussian,      
            'kl_mb': kl_mb,             
            'channel_error': channel_metrics['error'],
            'current_lambda': lambda_value,
            'mb_entropy': mb_entropy_nats / math.log(2), # Convert nats to bits for logging
            'target_total_entropy_bits': target_total_entropy_bits, # Log the target
            'lambda_from_ratio': lambda_from_ratio_value,
            'effective_lambda_in_prior': effective_lambda_value,
            'layer_stats': {
                'layer_id': self.layer_id,
                'zdim': z_sample_continuous_for_kl.shape[1],
                'symbol_error_rate': symbol_error_value,
                'bit_error_rate': bit_error_value,
                'lambda': lambda_value,
                # 添加比特大小信息
                'num_symbols': bit_size_info['num_symbols'],
                'qam_order': bit_size_info['qam_order'],
                'bits_per_qam': bit_size_info['bits_per_qam'],
                'max_bits': bit_size_info['max_bits'],
                'entropy_bits_per_symbol': bit_size_info['entropy_bits_per_symbol'],
                'effective_bits': bit_size_info['effective_bits'],
                'compression_ratio': bit_size_info['compression_ratio']
            }
        }
        
        if get_latents:
            return feature_out, dict(z=z_channel_output.detach(), kl_dict=kl_dict)
        return feature_out, dict(kl_dict=kl_dict)

    def forward_uncond(self, feature, t=1.0, latent=None, paint_box=None):
        """ Sampling mode. """
        feature, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
        pv = pv * t 
        if latent is None: 
            z_prior_sample = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
        elif paint_box is not None: 
            nB, zC, zH, zW = latent.shape
            if min(zH, zW) == 1: 
                z_prior_sample = latent
            else:
                x1, y1, x2, y2 = paint_box
                h_slice = slice(round(y1*zH), round(y2*zH))
                w_slice = slice(round(x1*zW), round(x2*zW))
                z_sample_patch_from_prior = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
                z_patch = z_sample_patch_from_prior[:, :, h_slice, w_slice]
                z_prior_sample = torch.clone(latent)
                z_prior_sample[:, :, h_slice, w_slice] = z_patch
        else: 
            assert pm.shape == latent.shape
            z_prior_sample = latent
        
        # For unconditional sampling, if channel effects are desired on generated samples:
        # Option 1: Pass through QAM channel simulation
        # final_z, _, _, _ = self.qam_channel(z_prior_sample)
        # Option 2: Use prior sample directly (no channel effect for pure generation)
        final_z = z_prior_sample

        feature = feature + self.z_proj(final_z)
        feature = self.resnet_end(feature)
        return feature

    def update(self):
        """ Prepare for entropy coding. """
        min_scale = 0.1
        max_scale = 20
        log_scales = torch.linspace(math.log(min_scale), math.log(max_scale), steps=64, device=self.prior.c1.weight.device)
        scale_table = torch.exp(log_scales)
        self.discrete_gaussian.update_scale_table(scale_table) 
        self.discrete_gaussian.update()

    def compress(self, feature, enc_feature):
        """ Forward pass, compression (encoding) mode. """
        feature_transformed, pm, plogv = self.transform_prior(feature) 
        qm = self.posterior(torch.cat([feature_transformed, enc_feature], dim=1)) 
        
        # 计算当前层的比特信息以确定适合的QAM阶数
        with torch.no_grad():
            bit_size_info = self.calculate_bit_size(qm)
            qam_order = bit_size_info['qam_order']
        
        # 添加噪声处理 - 使用与训练相同的SNR噪声
        B, C, H, W = qm.shape
        
        # 1. 重塑为便于处理的形式
        z_pairs = qm.reshape(B, C//2, 2, H, W)
        
        # 2. 软量化 - 使用自适应QAM阶数的PAM电平
        pam_levels_count = int(np.sqrt(qam_order))
        if pam_levels_count == 16:
            pam_levels = self._qam_map_vals.to(qm.device) / self._qam_normalization.to(qm.device)
        else:
            max_val = 15.0 / self._qam_normalization.item()
            pam_levels = torch.linspace(-max_val, max_val, pam_levels_count, device=qm.device)
            
        # 3. 对每个维度进行处理
        soft_quantized = []
        for dim in range(2):  # I和Q分量
            z_dim = z_pairs[:, :, dim]
            z_flat = z_dim.reshape(-1, 1)
            distances = torch.abs(z_flat - pam_levels.view(1, -1))
            weights = torch.softmax(-distances/0.1, dim=1)
            soft_level = torch.sum(weights * pam_levels.view(1, -1), dim=1)
            soft_level = soft_level.reshape(B, C//2, H, W)
            soft_quantized.append(soft_level)
        
        # 构建复数表示
        soft_real = soft_quantized[0]
        soft_imag = soft_quantized[1]
        soft_complex = torch.complex(soft_real, soft_imag)
        
        # 添加AWGN噪声
        snr_linear = 10**(self.snr_db / 10.0)
        signal_power = torch.mean(torch.abs(soft_complex)**2)
        noise_power = signal_power / snr_linear
        noise_std = torch.sqrt(noise_power / 2.0)
        
        noise_real = torch.randn_like(soft_real) * noise_std
        noise_imag = torch.randn_like(soft_imag) * noise_std
        noisy_complex = torch.complex(soft_real + noise_real, soft_imag + noise_imag)
        
        # 对加噪后的信号进行硬量化
        noisy_real = noisy_complex.real
        noisy_imag = noisy_complex.imag
        noisy_z = torch.cat([noisy_real.unsqueeze(2), noisy_imag.unsqueeze(2)], dim=2).reshape(B, C, H, W)
        
        # 最后进行硬量化以获得索引，使用自适应QAM阶数
        _, combined_indices, pam_levels_count = self._hard_quantize_to_qam(noisy_z, qam_order=qam_order)
        
        # 解调以获取重建信号
        z_hat_quantized = self._demodulate_from_indices(combined_indices, qm.shape, pam_levels_count)
        
        feature_out = feature_transformed + self.z_proj(z_hat_quantized)
        feature_out = self.resnet_end(feature_out)
        
        compressed_data = {
            "symbol_indices": combined_indices.cpu(),
            "original_z_shape": qm.shape,
            "applied_snr": self.snr_db,  # 记录使用的SNR值
            "qam_order": qam_order,      # 记录使用的QAM阶数
            "pam_levels_count": pam_levels_count  # 记录PAM电平数
        }
        return feature_out, compressed_data

    def decompress(self, feature, strings):
        """ Forward pass, decompression (decoding) mode. """
        feature_transformed, pm, plogv = self.transform_prior(feature)
        
        # 获取压缩数据
        symbol_indices = strings["symbol_indices"].to(feature.device)
        original_qm_shape = strings["original_z_shape"]
        pam_levels_count = strings.get("pam_levels_count", 16)  # 默认16个PAM电平（256-QAM）
        
        # 从索引重建信号
        z_hat = self._demodulate_from_indices(symbol_indices, original_qm_shape, pam_levels_count)
        
        feature_out = feature_transformed + self.z_proj(z_hat)
        feature_out = self.resnet_end(feature_out)
        return feature_out

    def update_snr(self, new_snr_db):
        """
        更新SNR参数，允许在训练或测试过程中动态改变信道条件
        
        Args:
            new_snr_db: 新的SNR值(dB)
        
        Returns:
            float: 根据新SNR计算的λ值
        """
        old_snr = self.snr_db
        self.snr_db = float(new_snr_db) # Store as float
        
        # 使用新方法：先计算目标功率比例，再求解lambda
        target_ratio = self._calculate_target_power_ratio_for_snr(self.snr_db)
        lambda_target = self.find_lambda_for_target_ratio(target_ratio)
        
        # 更新当前lambda值
        self.current_lambda.fill_(lambda_target)
        
        # print(f"层 {self.layer_id}: SNR从{old_snr}dB更新为{self.snr_db}dB, 目标功率比例={target_ratio:.4f}, λ值={lambda_target:.4f}")
        
        return lambda_target

    def calculate_energy(self, constellation):
        """Calculate energy of constellation points"""
        return torch.sum(constellation**2, axis=1)
    
    # 添加两个辅助方法到QLatentBlockX类中，在update_snr方法前
    def calculate_target_power_for_snr(self, constellation, snr_db):
        """
        根据SNR计算目标功率比例
        
        Args:
            snr_db: 信噪比(dB)
            
        Returns:
            目标功率比例
        """
        # 均匀分布下的平均功率
        uniform_avg_power = np.mean(self.calculate_energy(constellation))
        # 计算目标功率
        target_power = uniform_avg_power * (10 ** (snr_db / 10))

        # 计算目标功率比例
        ratio = target_power / uniform_avg_power

        # 从解调.py中迁移的方法
        snr_db_val = snr_db if isinstance(snr_db, (float, int)) else snr_db.item()
        
        # 根据SNR确定合适的功率比例
        if snr_db_val < -15:
            # 极限情况，几乎退化为BPSK/QPSK
            ratio = 0.005 + 0.002 * (snr_db_val + 20)  # -20dB时为0.005，-15dB时为0.015
        elif snr_db_val < -10:
            # 超强整形区域
            ratio = 0.015 + 0.003 * (snr_db_val + 15)  # -15dB时为0.015，-10dB时为0.03
        elif snr_db_val < -5:
            # 强整形区域
            ratio = 0.03 + 0.004 * (snr_db_val + 10)  # -10dB时为0.03，-5dB时为0.05
        elif snr_db_val < 0:
            # 中强整形区域
            ratio = 0.05 + 0.02 * (snr_db_val + 5)  # -5dB时为0.05，0dB时为0.15
        elif snr_db_val < 10:
            # 中等整形区域
            ratio = 0.15 + 0.025 * snr_db  # 0dB时为0.15，10dB时约为0.4
        elif snr_db_val < 20:
            # 轻度整形区域
            ratio = 0.4 + 0.03 * (snr_db - 10)  # 10dB时为0.4，20dB时为0.7
        else:
            # 高SNR区域，接近均匀分布
            ratio = 0.7 + min(0.2, 0.01 * (snr_db - 20))  # 20dB时为0.7，30dB时为0.8
        
        # 确保ratio在有效范围内
        ratio = min(0.9, max(0.005, ratio))
        
        return uniform_avg_power * ratio
    
    def find_lambda_for_target_ratio(self, target_ratio, lambda_range=(0.0, 5.0), tol=1e-6):
        """
        根据目标功率比例求解λ值
        
        Args:
            target_ratio: 目标功率与均匀分布功率的比例
            
        Returns:
            满足功率比例的λ值
        """
        # 构建星座点
        pam_levels = self._qam_map_vals.to(self.current_lambda.device) / self._qam_normalization.to(self.current_lambda.device)
        # 构建QAM星座点 (256-QAM)
        constellation = []
        for i in range(16):  # PAM的16个电平
            for q in range(16):  # PAM的16个电平
                constellation.append([pam_levels[i].item(), pam_levels[q].item()])
        
        # 转换为Tensor
        constellation = torch.tensor(constellation, device=self.current_lambda.device)
        
        # 计算星座点能量
        energy = torch.sum(constellation**2, dim=1)
        
        # 计算均匀分布下的平均功率
        uniform_power = torch.mean(energy)
        
        # 计算目标功率
        target_power = target_ratio * uniform_power
        
        
        def power_difference(lambda_param):
            """计算当前λ下的平均功率与目标功率的差值"""
            # 当λ≤0时返回大值
            if lambda_param <= 0:
                return 1e10  
            
            # 计算概率分布
            probs = torch.exp(-lambda_param * energy)
            probs = probs / torch.sum(probs)
            
            # 计算平均功率
            avg_power = torch.sum(energy * probs)
            
            # 返回差值
            return (avg_power - target_power).item()
        
        # 使用二分搜索方法（与解调.py中相同）
        left, right = lambda_range
        iterations = 0
        max_iterations = 100
        
        # 确保搜索范围是有效的
        if power_difference(left) * power_difference(right) >= 0:
            # 如果范围端点的函数值同号，需要调整搜索范围
            if abs(power_difference(left)) < abs(power_difference(right)):
                return left
            else:
                return right
        
        # 二分搜索
        while iterations < max_iterations and right - left > tol:
            mid = (left + right) / 2
            if power_difference(mid) * power_difference(left) < 0:
                right = mid
            else:
                left = mid
            iterations += 1
        
        optimal_lambda = (left + right) / 2
        return optimal_lambda

    def _create_qam_constellation(self):
        """创建256-QAM星座点，使用torch实现，参考解调.py的create_qam_constellation"""
        # 获取PAM电平
        pam_levels = self._qam_map_vals.to(self.current_lambda.device) / self._qam_normalization.to(self.current_lambda.device)
        
        # 构建星座点
        constellation = []
        for i in range(16):  # 16个PAM电平
            for q in range(16):  # 16个PAM电平
                constellation.append([pam_levels[i].item(), pam_levels[q].item()])
                
        return torch.tensor(constellation, device=self.current_lambda.device)
    
    def _calculate_target_power_ratio_for_snr(self, snr_db):
        """
        根据SNR计算目标功率比例，参考解调.py的calculate_target_power_for_snr函数
        
        Args:
            snr_db: 信噪比(dB)，标量值
        
        Returns:
            目标功率比例
        """
        # 根据SNR确定合适的功率比例
        if snr_db < -18:
            # 极限情况，几乎完全退化为BPSK
            # 极大幅度降低基础值，使lambda值达到200-300范围
            ratio = 0.0001 + 0.00005 * (snr_db + 20)  # -20dB时为0.0001，-18dB时为0.0002
        elif snr_db < -15:
            # 超极限情况，接近QPSK
            # 调整过渡，确保平滑
            ratio = 0.0002 + 0.0003 * (snr_db + 18) / 3  # -18dB时为0.0002，-15dB时为0.0005
        elif snr_db < -10:
            # 超强整形区域
            # 调整过渡，确保平滑
            ratio = 0.0005 + 0.0015 * (snr_db + 15) / 5  # -15dB时为0.0005，-10dB时为0.002
        elif snr_db < -5:
            # 强整形区域
            ratio = 0.002 + 0.008 * (snr_db + 10) / 5  # -10dB时为0.002，-5dB时为0.01
        elif snr_db < 0:
            # 中强整形区域
            ratio = 0.01 + 0.04 * (snr_db + 5) / 5  # -5dB时为0.01，0dB时为0.05
        elif snr_db < 10:
            # 中等整形区域
            ratio = 0.05 + 0.25 * snr_db / 10  # 0dB时为0.05，10dB时为0.3
        elif snr_db < 20:
            # 轻度整形区域
            ratio = 0.3 + 0.4 * (snr_db - 10) / 10  # 10dB时为0.3，20dB时为0.7
        else:
            # 高SNR区域，接近均匀分布
            ratio = 0.7 + min(0.2, 0.01 * (snr_db - 20))  # 20dB时为0.7，40+dB时为0.9
        
        # 确保ratio在有效范围内 - 降低最小值以支持更大的lambda
        ratio = min(0.9, max(0.0001, ratio))
        
        return ratio

    def _simulate_hard_channel(self, z_input, snr_db=None):
        """
        模拟硬判决信道，用于准确计算误符号率和误比特率
        
        Args:
            z_input: 输入连续值
            snr_db: 可选，指定SNR值，默认使用self.snr_db
            
        Returns:
            dict: 包含误符号率、误比特率和其他指标的字典
        """
        try:
            if snr_db is None:
                snr_db = self.snr_db if isinstance(self.snr_db, (float, int)) else self.snr_db.item()
            
            # 1. 调制 - 硬量化到QAM星座点，保持形状一致
            transmitted_symbols, symbol_indices, pam_levels_count = self._hard_quantize_to_qam(z_input, preserve_shape=True)
            
            # 2. 使用独立函数添加AWGN噪声
            received_symbols, channel_stats = self._add_awgn_noise(transmitted_symbols, snr_db)
            
            # 3. 解调 - 硬判决，同样保持形状一致
            decoded_symbols, received_indices, _ = self._hard_quantize_to_qam(received_symbols, preserve_shape=True)
            
            # 4. 计算误符号率 - 现在形状应该匹配
            symbol_errors = (symbol_indices != received_indices).float().mean()
            
            # 5. 计算误比特率
            bits_per_symbol = math.log2(pam_levels_count**2) / 2  # 每个实数对应半个符号
            total_bits = symbol_indices.numel() * bits_per_symbol
            
            # 高效向量化计算位差异
            bit_errors_count = 0
            for i_bit in range(int(bits_per_symbol)):
                bit_mask = 1 << i_bit
                input_bits = (symbol_indices.view(-1) & bit_mask) > 0
                output_bits = (received_indices.view(-1) & bit_mask) > 0
                bit_errors_count += (input_bits != output_bits).sum().item()
            
            bit_error_rate = torch.tensor(bit_errors_count / total_bits, device=z_input.device)
            
            # 返回完整信道统计信息
            return {
                'transmitted_symbols': transmitted_symbols,
                'received_symbols': received_symbols,
                'decoded_symbols': decoded_symbols,
                'symbol_error_rate': symbol_errors,  # 保持为张量，不要调用.item()
                'bit_error_rate': bit_error_rate,
                **channel_stats  # 包含所有信道统计信息
            }
        except Exception as e:
            print(f"_simulate_hard_channel中出错: {e}")
            # 返回合理的默认值
            snr_linear = 10**(snr_db / 10.0) if snr_db is not None else 0.01
            symbol_error_rate = 0.5 * math.exp(-snr_linear/20)
            return {
                'symbol_error_rate': torch.tensor(symbol_error_rate, device=z_input.device),
                'bit_error_rate': torch.tensor(symbol_error_rate / 2, device=z_input.device),
                'snr_db': snr_db if snr_db is not None else -20.0,
                'measured_snr_db': snr_db if snr_db is not None else -20.0,
                'signal_power': 1.0,
                'noise_power': 1.0 / snr_linear,
                'error': str(e)
            }

    def _add_awgn_noise(self, symbols, snr_db=None):
        """
        为信号添加加性高斯白噪声(AWGN)
        
        Args:
            symbols: 输入信号，可以是复数张量或实数张量
            snr_db: 信噪比(dB)，如果为None则使用self.snr_db
            
        Returns:
            tuple: (接收信号, 信道统计信息)
        """
        if snr_db is None:
            snr_db = self.snr_db if isinstance(self.snr_db, (float, int)) else self.snr_db.item()
        
        # 转换为线性比例
        snr_linear = 10**(snr_db / 10.0)
        
        # 计算信号功率
        if symbols.is_complex():
            # 复数信号
            signal_power = torch.mean(torch.abs(symbols)**2)
            
            # 计算噪声功率和标准差
            noise_power = signal_power / snr_linear
            noise_std = torch.sqrt(noise_power / 2.0)
            
            # 添加复高斯噪声
            noise_real = torch.randn_like(symbols.real) * noise_std
            noise_imag = torch.randn_like(symbols.imag) * noise_std
            received_symbols = torch.complex(symbols.real + noise_real, symbols.imag + noise_imag)
            
        else:
            # 实数信号 - 检查是否为I/Q对表示
            if symbols.dim() >= 3 and symbols.shape[-3] % 2 == 0:
                # 可能是I/Q对表示，尝试重塑
                orig_shape = symbols.shape
                
                if symbols.dim() == 4:  # BCHW格式
                    B, C, H, W = symbols.shape
                    if C % 2 == 0:
                        # 将其视为I/Q对
                        symbols_reshaped = symbols.reshape(B, C//2, 2, H, W)
                        signal_i = symbols_reshaped[:,:,0,:,:]
                        signal_q = symbols_reshaped[:,:,1,:,:]
                        
                        # 计算功率
                        signal_power = torch.mean(signal_i**2 + signal_q**2)
                        
                        # 计算噪声参数
                        noise_power = signal_power / snr_linear
                        noise_std = torch.sqrt(noise_power / 2.0)
                        
                        # 添加噪声
                        noise_i = torch.randn_like(signal_i) * noise_std
                        noise_q = torch.randn_like(signal_q) * noise_std
                        received_i = signal_i + noise_i
                        received_q = signal_q + noise_q
                        
                        # 重新组合
                        received_reshaped = torch.cat([
                            received_i.unsqueeze(2),
                            received_q.unsqueeze(2)
                        ], dim=2)
                        received_symbols = received_reshaped.reshape(orig_shape)
                    else:
                        # 不是I/Q对，作为普通实数信号处理
                        signal_power = torch.mean(symbols**2)
                        noise_power = signal_power / snr_linear
                        noise_std = torch.sqrt(noise_power)
                        received_symbols = symbols + torch.randn_like(symbols) * noise_std
                else:
                    # 其他维度格式，作为普通实数信号处理
                    signal_power = torch.mean(symbols**2)
                    noise_power = signal_power / snr_linear
                    noise_std = torch.sqrt(noise_power)
                    received_symbols = symbols + torch.randn_like(symbols) * noise_std
            else:
                # 普通实数信号
                signal_power = torch.mean(symbols**2)
                noise_power = signal_power / snr_linear
                noise_std = torch.sqrt(noise_power)
                received_symbols = symbols + torch.randn_like(symbols) * noise_std
        
        # 计算实际测量的SNR
        if received_symbols.is_complex():
            noise_power_measured = torch.mean(torch.abs(received_symbols - symbols)**2)
        else:
            noise_power_measured = torch.mean((received_symbols - symbols)**2)
        
        measured_snr = signal_power / noise_power_measured if noise_power_measured > 0 else torch.tensor(float('inf'))
        measured_snr_db = 10 * torch.log10(measured_snr)
        
        # 返回接收信号和信道统计信息
        channel_stats = {
            'snr_db': snr_db,
            'snr_linear': snr_linear,
            'signal_power': signal_power.item(),
            'noise_power': noise_power.item(),
            'noise_std': noise_std.item(),
            'measured_snr': measured_snr.item(),
            'measured_snr_db': measured_snr_db.item()
        }
        
        return received_symbols, channel_stats


class TopDownDecoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.dec_blocks = nn.ModuleList(blocks)

        width = self.dec_blocks[0].in_channels
        self.bias = nn.Parameter(torch.zeros(1, width, 1, 1))

        self._init_weights()
        
    def _init_weights(self):
        total_blocks = len([1 for b in self.dec_blocks if hasattr(b, 'residual_scaling')])
        for block in self.dec_blocks:
            if hasattr(block, 'residual_scaling'):
                block.residual_scaling(total_blocks)

    def forward(self, enc_features, get_latents=False):
        stats = []
        min_res = min(enc_features.keys())
        enc_min_res_feature = enc_features[min_res]
        B, _, H_min, W_min = enc_min_res_feature.shape
        feature = self.bias.expand(B, -1, H_min, W_min) 

        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_train'):
                res = int(feature.shape[2]) 
                if res not in enc_features:
                     raise KeyError(f"Encoder feature for resolution {res} not found. Available: {list(enc_features.keys())}")
                f_enc = enc_features[res]
                feature, block_stats = block.forward_train(feature, f_enc, get_latents=get_latents)
                stats.append(block_stats)
            else: 
                feature = block(feature)
        return feature, stats

    def forward_uncond(self, nhw_repeat=(1, 1, 1), t=1.0):
        nB, nH, nW = nhw_repeat
        feature = self.bias.expand(nB, -1, nH, nW)
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'):
                feature = block.forward_uncond(feature, t)
            else:
                feature = block(feature)
        return feature

    def forward_with_latents(self, latents, nhw_repeat=None, t=1.0, paint_box=None):
        if nhw_repeat is None:
            if latents and latents[0] is not None:
                 nB, _, nH, nW = latents[0].shape 
            else: 
                nB, nH, nW = (1,1,1) if nhw_repeat is None else nhw_repeat
            feature = self.bias.expand(nB, -1, nH, nW)
        else: 
            nB, nH, nW = nhw_repeat
            feature = self.bias.expand(nB, -1, nH, nW)

        idx = 0
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'): 
                current_latent = latents[idx] if latents and idx < len(latents) else None
                feature = block.forward_uncond(feature, t, latent=current_latent, paint_box=paint_box)
                idx += 1
            else: 
                feature = block(feature)
        return feature

    def update(self):
        for block in self.dec_blocks:
            if hasattr(block, 'update'):
                block.update()

    def compress(self, enc_features):
        min_res = min(enc_features.keys())
        enc_min_res_feature = enc_features[min_res]
        B, _, H_min, W_min = enc_min_res_feature.shape
        feature = self.bias.expand(B, -1, H_min, W_min) 
        
        compressed_data_all_layers = [] 
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'compress'): 
                res = feature.shape[2]
                f_enc = enc_features[res]
                feature, compressed_data_dict_from_block = block.compress(feature, f_enc)
                compressed_data_all_layers.append(compressed_data_dict_from_block) 
            else: 
                feature = block(feature)
        return compressed_data_all_layers, feature

    def decompress(self, compressed_object: list):
        smallest_shape_info = compressed_object[-1] 
        B_comp, _, H_comp, W_comp = smallest_shape_info 
        feature = self.bias.expand(B_comp, -1, H_comp, W_comp)
        
        num_latent_layers_compressed_data = len(compressed_object) - 1 
        data_idx = 0 

        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'decompress'): 
                if data_idx < num_latent_layers_compressed_data:
                    compressed_data_dict_for_block = compressed_object[data_idx]
                    data_idx += 1
                    feature = block.decompress(feature, compressed_data_dict_for_block)
                else:
                    raise ValueError("Mismatch: More decompressible blocks than available compressed data items.")
            else: 
                feature = block(feature)
        
        if data_idx != num_latent_layers_compressed_data:
            raise ValueError(f"Mismatch in consumed compressed data for decoder: consumed items = {data_idx}, available items = {num_latent_layers_compressed_data}")
            
        return feature

    def update_all_snr(self, new_snr_db):
        """
        更新所有层的SNR参数
        
        Args:
            new_snr_db: 新的SNR值(dB)
        """
        lambda_values = []
        for block in self.dec_blocks:
            if hasattr(block, 'update_snr'):
                lambda_val = block.update_snr(new_snr_db)
                lambda_values.append(lambda_val)
        
        if lambda_values:
            avg_lambda = sum(lambda_values) / len(lambda_values)
            print(f"所有层平均lambda值: {avg_lambda:.4f}")
        
        return lambda_values


class HierarchicalVAE(nn.Module):
    """ Class of general hierarchical VAEs
    """
    log2_e = math.log2(math.e)

    def __init__(self, config: dict):
        """ Initialize model """
        super().__init__()
        self.encoder = BottomUpEncoder(blocks=config.pop('enc_blocks'))
        self.decoder = TopDownDecoder(blocks=config.pop('dec_blocks'))
        self.out_net = config.pop('out_net')

        self.im_shift = float(config['im_shift'])
        self.im_scale = float(config['im_scale'])
        self.max_stride = config['max_stride']

        self.register_buffer('_dummy', torch.zeros(1), persistent=False)
        self._dummy: torch.Tensor
        
        # 添加熵正则项权重调度
        self.register_buffer('entropy_weight_base', torch.tensor(0.01))  # 基础权重
        self.register_buffer('entropy_weight_max', torch.tensor(0.03))   # 最大权重
        self.register_buffer('entropy_warmup_iters', torch.tensor(10000)) # 预热迭代次数
        self.register_buffer('global_iter', torch.tensor(0))  # 全局迭代计数 <--- Used by HierarchicalVAE

        self._stats_log = dict()
        self._flops_mode = False
        self.compressing = False
        
    def update_channel_snr(self, new_snr_db):
        """
        更新模型中所有QAM信道的SNR参数
        
        Args:
            new_snr_db: 新的SNR值(dB)
        
        Returns:
            lambda_values: 各层在新SNR下的lambda值列表
        """
        print(f"更新模型中所有QAM信道的SNR到 {new_snr_db} dB")
        lambda_values = self.decoder.update_all_snr(new_snr_db)
        
        # 记录到日志
        mode = 'train' if self.training else 'eval'
        self._stats_log[f'{mode}_updated_snr'] = new_snr_db
        if lambda_values:
            self._stats_log[f'{mode}_lambda_values'] = lambda_values
            
        return lambda_values

    def preprocess_input(self, im: torch.Tensor):
        """ Shift and scale the input image """
        assert (im.shape[2] % self.max_stride == 0) and (im.shape[3] % self.max_stride == 0)
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im + self.im_shift) * self.im_scale
        return x

    def process_output(self, x: torch.Tensor):
        """ scale the decoder output from range (-1, 1) to (0, 1) """
        assert not x.requires_grad
        im_hat = x.clone().clamp_(min=-1.0, max=1.0).mul_(0.5).add_(0.5)
        return im_hat

    def preprocess_target(self, im: torch.Tensor):
        """ Shift and scale the image to make it reconstruction target """
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im - 0.5) * 2.0
        return x

    def forward(self, im, return_rec=False):
        """ Forward pass for training """
        im = im.to(self._dummy.device)
        x = self.preprocess_input(im)
        x_target = self.preprocess_target(im)

        enc_features = self.encoder(x)
        
        # Increment global_step for priors that use it
        if self.training:
            self.global_iter +=1 # Model's own global iteration counter
            for module in self.decoder.modules():
                if isinstance(module, AdaptiveMaxwellBoltzmannPrior):
                    # The prior uses its own self.global_step, which should be updated
                    # based on the model's training progression.
                    # Let's ensure prior's global_step is synchronized or set from model's global_iter
                    module.global_step.fill_(self.global_iter.item())

        feature, stats_all = self.decoder(enc_features, get_latents=self._flops_mode) 
        out_loss, x_hat = self.out_net.forward_loss(feature, x_target)

        if self._flops_mode: 
            return x_hat

        nB, imC, imH, imW = im.shape 
        ndims = float(imC * imH * imW) 

        # 收集新的KL散度
        kl_vae_all_layers = []     # 与高斯先验的KL
        kl_mb_all_layers = []      # 与Maxwell-Boltzmann先验的KL
        mb_entropy_all_layers = [] # Maxwell-Boltzmann分布的熵
        channel_error_all_layers = []  # 信道错误率
        current_lambda_values = {} # 当前各层的lambda值
        target_entropy_log = {}    # Log target entropies
        lambda_from_target_log = {} # Log lambdas derived from target entropies
        
        for i, stat_block_output in enumerate(stats_all): 
            kl_d = stat_block_output['kl_dict'] 
            if 'kl_vae' in kl_d:
                # kl_vae有空间维度，需要对空间维度求和
                kl_vae_all_layers.append(kl_d['kl_vae'].sum(dim=(1, 2, 3)))
            if 'kl_mb' in kl_d:
                # kl_mb可能已经是[B]形状，检查维度数并正确处理
                kl_mb_tensor = kl_d['kl_mb']
                if kl_mb_tensor.dim() > 1:
                    # 对所有额外维度求和
                    dims_to_sum = tuple(range(1, kl_mb_tensor.dim()))
                    kl_mb_all_layers.append(kl_mb_tensor.sum(dim=dims_to_sum))
                else:
                    # 已经是[B]形状，直接添加
                    kl_mb_all_layers.append(kl_mb_tensor)
            if 'mb_entropy' in kl_d:
                # 收集Maxwell-Boltzmann熵
                mb_entropy_all_layers.append(kl_d['mb_entropy'])
            if 'channel_error' in kl_d:
                channel_error_all_layers.append(kl_d['channel_error'])
            if 'current_lambda' in kl_d:
                # 收集当前lambda值
                # 找到对应的QLatentBlockX层以获取层名称
                for name, module in self.decoder.named_modules():
                    if isinstance(module, QLatentBlockX) and hasattr(module, 'current_lambda'):
                        # 通过比较tensor确定是否是同一层
                        if hasattr(kl_d['current_lambda'], 'device') and module.current_lambda.device == kl_d['current_lambda'].device and torch.allclose(module.current_lambda, kl_d['current_lambda']):
                            current_lambda_values[module.layer_id] = kl_d['current_lambda'] # Keep as tensor for now
                            if 'target_total_entropy_bits' in kl_d:
                                target_entropy_log[module.layer_id] = kl_d['target_total_entropy_bits']
                            if 'lambda_from_ratio' in kl_d:
                                lambda_from_target_log[module.layer_id] = kl_d['lambda_from_ratio']
                            break
            
        # 计算总KL散度
        kl_vae_total_nats = sum(kl_vae_all_layers) if kl_vae_all_layers else torch.tensor(0.0, device=im.device)
        kl_vae_nats_per_dim = kl_vae_total_nats / ndims
        
        kl_mb_total_nats = sum(kl_mb_all_layers) if kl_mb_all_layers else torch.tensor(0.0, device=im.device)
        kl_mb_nats_per_dim = kl_mb_total_nats / ndims

        # 计算熵正则项 - 我们是最小化损失，所以使用负号来最大化熵
        # 计算所有层的平均熵
        if mb_entropy_all_layers:
            mb_entropy_avg = sum(mb_entropy_all_layers) / len(mb_entropy_all_layers)
            entropy_per_dim = mb_entropy_avg / ndims
        else:
            entropy_per_dim = torch.tensor(0.0, device=im.device)

        # 计算信道错误
        num_latent_layers = len(channel_error_all_layers)
        channel_error_avg = sum(channel_error_all_layers) / num_latent_layers if num_latent_layers > 0 else torch.tensor(0.0, device=im.device)
        
        # 设置KL权重
        kl_vae_weight = 0.1    # 高斯KL权重
        kl_mb_weight = 0.5     # Maxwell-Boltzmann KL权重
        
        # 使用固定的熵正则项权重，而不是渐进式增加
        entropy_weight = 50  # 使用固定熵权重
        
        # 更新全局迭代计数 - Moved earlier, before decoder call
        # if self.training:
        #     self.global_iter += 1
        
        # 计算总损失 - 添加熵正则项（负号表示我们想要最大化熵）
        kl_loss = kl_vae_weight * kl_vae_nats_per_dim.mean() + kl_mb_weight * kl_mb_nats_per_dim.mean()
        entropy_reg = -entropy_weight * entropy_per_dim.mean() if isinstance(entropy_per_dim, torch.Tensor) else -entropy_weight * entropy_per_dim
        recon_loss = out_loss.mean()
        
        # 确保各部分都是标量，然后相加
        loss = kl_loss + entropy_reg + recon_loss
                
        # 记录当前使用的熵权重
        with torch.no_grad():
            self._stats_log[f'{"train" if self.training else "eval"}_entropy_weight'] = entropy_weight

        with torch.no_grad():
            im_hat = self.process_output(x_hat.detach())
            im_mse = tnf.mse_loss(im_hat, im, reduction='mean')
            psnr = -10 * math.log10(im_mse.item() if im_mse.item() > 0 else 1e-10) 
            
            mode = 'train' if self.training else 'eval'

            # 记录KL散度信息
            kl_vae_per_layer_bpdim = [(kl_sum_spatial.mean(0) / ndims * self.log2_e).item() 
                                     for kl_sum_spatial in kl_vae_all_layers] if kl_vae_all_layers else []
            self._stats_log[f'{mode}_kl_vae_bpdim_layers'] = kl_vae_per_layer_bpdim
            
            # 记录Maxwell-Boltzmann KL散度
            kl_mb_per_layer_bpdim = [(kl_sum_spatial.mean(0) / ndims * self.log2_e).item() 
                                     for kl_sum_spatial in kl_mb_all_layers] if kl_mb_all_layers else []
            self._stats_log[f'{mode}_kl_mb_bpdim_layers'] = kl_mb_per_layer_bpdim
            
            # 收集比特大小信息
            bit_size_info_layers = []
            total_symbols = 0
            total_max_bits = 0
            total_effective_bits = 0
            
            for stat_block_output in stats_all:
                if 'kl_dict' in stat_block_output and 'layer_stats' in stat_block_output['kl_dict']:
                    layer_stats = stat_block_output['kl_dict']['layer_stats']
                    if 'num_symbols' in layer_stats:
                        bit_info = {
                            'layer_id': layer_stats['layer_id'],
                            'num_symbols': layer_stats['num_symbols'],
                            'qam_order': layer_stats['qam_order'],
                            'max_bits': layer_stats['max_bits'],
                            'entropy_bits_per_symbol': layer_stats['entropy_bits_per_symbol'],
                            'effective_bits': layer_stats['effective_bits'],
                            'compression_ratio': layer_stats['compression_ratio']
                        }
                        bit_size_info_layers.append(bit_info)
                        
                        # 累计总数
                        total_symbols += layer_stats['num_symbols']
                        total_max_bits += layer_stats['max_bits']
                        total_effective_bits += layer_stats['effective_bits']
            
            # 保存各层比特信息
            self._stats_log[f'{mode}_bit_size_layers'] = bit_size_info_layers
            
            # 保存总体比特信息
            if bit_size_info_layers:
                overall_compression_ratio = total_effective_bits / total_max_bits if total_max_bits > 0 else 0
                self._stats_log[f'{mode}_total_symbols'] = total_symbols
                self._stats_log[f'{mode}_total_max_bits'] = total_max_bits
                self._stats_log[f'{mode}_total_effective_bits'] = total_effective_bits
                self._stats_log[f'{mode}_overall_compression_ratio'] = overall_compression_ratio
            
            # 记录Maxwell-Boltzmann熵
            mb_entropy_per_layer = [ent.mean().item() for ent in mb_entropy_all_layers 
                                   if hasattr(ent, 'mean')] if mb_entropy_all_layers else []
            self._stats_log[f'{mode}_mb_entropy_layers'] = mb_entropy_per_layer
            
            # 记录熵正则项
            if mb_entropy_all_layers:
                self._stats_log[f'{mode}_entropy_regularization'] = (-entropy_weight * entropy_per_dim).mean().item()

            # 记录信道错误
            channel_error_per_layer = [err.mean().item() for err in channel_error_all_layers 
                                      if hasattr(err, 'mean')] if channel_error_all_layers else []
            self._stats_log[f'{mode}_channel_error_layers'] = channel_error_per_layer
            
            # 记录当前lambda值 (effective ones)
            self._stats_log[f'{mode}_current_lambda_values'] = {k: v.item() for k, v in current_lambda_values.items()}
            # Log target entropies and calculated lambdas from target
            self._stats_log[f'{mode}_target_total_entropy_bits'] = target_entropy_log # Already items
            self._stats_log[f'{mode}_lambda_from_entropy_target'] = lambda_from_target_log # Already items
            
            # 记录lambda比较信息
            lambda_target_effective_comparison = defaultdict(dict)
            for stat_block_output in stats_all:
                if 'kl_dict' in stat_block_output and 'lambda_from_ratio' in stat_block_output['kl_dict'] and 'effective_lambda_in_prior' in stat_block_output['kl_dict']:
                    kl_d = stat_block_output['kl_dict']
                    if 'layer_stats' in kl_d and 'layer_id' in kl_d['layer_stats']:
                        layer_id = kl_d['layer_stats']['layer_id']
                        lambda_target_effective_comparison[layer_id] = {
                            'target': kl_d['lambda_from_ratio'],
                            'effective': kl_d['effective_lambda_in_prior'],
                            'diff': kl_d['effective_lambda_in_prior'] - kl_d['lambda_from_ratio']
                        }
            self._stats_log[f'{mode}_lambda_comparison'] = lambda_target_effective_comparison
            
            # 总体统计
            total_kl_vae_bpdim = kl_vae_nats_per_dim.mean(0).item() * self.log2_e if kl_vae_all_layers else 0.0
            total_kl_mb_bpdim = kl_mb_nats_per_dim.mean(0).item() * self.log2_e if kl_mb_all_layers else 0.0
            
            self._stats_log[f'{mode}_kl_vae_bpdim_total'] = total_kl_vae_bpdim
            self._stats_log[f'{mode}_kl_mb_bpdim_total'] = total_kl_mb_bpdim
            self._stats_log[f'{mode}_channel_error_avg'] = channel_error_avg.mean(0).item() if hasattr(channel_error_avg, 'mean') else channel_error_avg.item()

            self._stats_log[f'{mode}_bppix'] = total_kl_vae_bpdim 
            self._stats_log[f'{mode}_bppix_mb'] = total_kl_mb_bpdim
            self._stats_log[f'{mode}_psnr'] = psnr
            
            try:
                channel_bpps_vae = []
                for stat_block_output in stats_all:
                    # 确保'kl_vae'存在并且是张量
                    if 'kl_dict' in stat_block_output and 'kl_vae' in stat_block_output['kl_dict']:
                        kl_vae_unsummed = stat_block_output['kl_dict']['kl_vae'] 
                        if isinstance(kl_vae_unsummed, torch.Tensor) and kl_vae_unsummed.ndim == 4: 
                            bpps_c = kl_vae_unsummed.sum(dim=(2,3)).mean(0).cpu() / (imH * imW) 
                            channel_bpps_vae.append((bpps_c * self.log2_e).tolist())
                if channel_bpps_vae:
                     self._stats_log[f'{mode}_channels_kl_vae'] = channel_bpps_vae
            except Exception:
                pass 

        # 构建返回统计信息
        stats = OrderedDict()
        stats['loss']  = loss 
        stats['kl_vae'] = kl_vae_nats_per_dim.mean(0).item() if kl_vae_all_layers else 0.0
        stats['kl_mb'] = kl_mb_nats_per_dim.mean(0).item() if kl_mb_all_layers else 0.0
        
        # 添加熵和熵正则项到统计
        if mb_entropy_all_layers:
            stats['mb_entropy'] = entropy_per_dim.mean(0).item()
            stats['entropy_reg'] = (-entropy_weight * entropy_per_dim).mean(0).item()
            
        stats['channel_error'] = channel_error_avg.mean(0).item() if hasattr(channel_error_avg, 'mean') else channel_error_avg.item()
        stats[self.out_net.loss_name] = out_loss.detach().cpu().mean(0).item()
        stats['bppix_vae'] = total_kl_vae_bpdim 
        stats['bppix_mb'] = total_kl_mb_bpdim
        stats['psnr'] = psnr
        
        # 添加lambda值到返回的统计信息，每个层单独添加一个条目
        for layer_id, lambda_val_tensor in current_lambda_values.items():
            # 简化层名称，只保留z维度信息
            # 从layer_id中提取z维度 (例如从"layer_w384_z16"提取"z16")
            if "_z" in layer_id:
                z_dim_str = layer_id.split("_z")[-1] # Corrected variable name
                simplified_name = f"lambda_z{z_dim_str}"
            else:
                # 如果没有z维度信息，则使用简短编号
                simplified_name = f"lambda_{layer_id[-2:]}" # Potentially problematic if layer_id is short
                if len(layer_id) >=2:
                    simplified_name = f"lambda_{layer_id[-2:]}"
                else:
                    simplified_name = f"lambda_{layer_id}"


            stats[simplified_name] = float(lambda_val_tensor.item()) # Ensure it's scalar float

        # Log target entropies and their corresponding lambdas to stats output
        for layer_id, target_h_val in target_entropy_log.items():
            prefix = layer_id.split("_z")[-1] if "_z" in layer_id else layer_id[-2:]
            stats[f'z{prefix}_targetH'] = float(target_h_val)
        for layer_id, lambda_target_val in lambda_from_target_log.items():
            prefix = layer_id.split("_z")[-1] if "_z" in layer_id else layer_id[-2:]
            stats[f'z{prefix}_lambda_targetH'] = float(lambda_target_val)

        # 层级误比特率统计
        if return_rec:
            stats['im_hat'] = im_hat
        
        # 添加总体比特大小信息
        if hasattr(self, '_stats_log') and f'{mode}_total_symbols' in self._stats_log:
            stats['total_symbols'] = self._stats_log[f'{mode}_total_symbols']
            stats['total_max_bits'] = self._stats_log[f'{mode}_total_max_bits']
            stats['total_effective_bits'] = self._stats_log[f'{mode}_total_effective_bits']
            stats['overall_compression_ratio'] = self._stats_log[f'{mode}_overall_compression_ratio']
        
        # 收集层级统计信息
        layer_stats = defaultdict(dict)
        total_symbol_errors = 0
        total_bit_errors = 0
        num_layers = 0
        
        for stat_block_output in stats_all:
            if 'kl_dict' in stat_block_output and 'layer_stats' in stat_block_output['kl_dict']:
                layer_info = stat_block_output['kl_dict']['layer_stats']
                layer_id = layer_info['layer_id']
                
                # 收集每层的统计信息
                layer_stats[layer_id] = {
                    'zdim': layer_info['zdim'],
                    'symbol_error_rate': layer_info['symbol_error_rate'],
                    'bit_error_rate': layer_info['bit_error_rate']
                }
                
                # 累计总错误
                total_symbol_errors += layer_info['symbol_error_rate']
                total_bit_errors += layer_info['bit_error_rate']
                num_layers += 1
        
        # 计算平均错误率
        avg_symbol_error_rate = total_symbol_errors / num_layers if num_layers > 0 else 0
        avg_bit_error_rate = total_bit_errors / num_layers if num_layers > 0 else 0
        
        # 添加到stats中
        stats['avg_symbol_error_rate'] = float(avg_symbol_error_rate)
        stats['avg_bit_error_rate'] = float(avg_bit_error_rate)
        
        # 为每一层添加统计信息
        for layer_id, layer_info in layer_stats.items():
            if "_z" in layer_id:
                z_dim = layer_id.split("_z")[-1]
                prefix = f"z{z_dim}"
            else:
                prefix = f"layer_{layer_id[-2:]}"
                
            stats[f'{prefix}_ser'] = float(layer_info['symbol_error_rate'])
            stats[f'{prefix}_ber'] = float(layer_info['bit_error_rate'])
            # stats[f'{prefix}_lambda'] = float(layer_info['lambda'])
            
        # 添加每层的比特信息
        for stat_block_output in stats_all:
            if 'kl_dict' in stat_block_output and 'layer_stats' in stat_block_output['kl_dict']:
                layer_stats = stat_block_output['kl_dict']['layer_stats']
                if 'num_symbols' in layer_stats and 'layer_id' in layer_stats:
                    layer_id = layer_stats['layer_id']
                    
                    # 提取层名称前缀
                    if "_z" in layer_id:
                        z_dim = layer_id.split("_z")[-1]
                        prefix = f"z{z_dim}"
                    else:
                        prefix = f"layer_{layer_id[-2:]}"
                    
                    # 添加比特信息
                    stats[f'{prefix}_symbols'] = int(layer_stats['num_symbols'])
                    stats[f'{prefix}_max_bits'] = int(layer_stats['max_bits'])
                    stats[f'{prefix}_bits'] = float(layer_stats['effective_bits'])
                    stats[f'{prefix}_bits_per_symbol'] = float(layer_stats['entropy_bits_per_symbol'])
                    stats[f'{prefix}_compression'] = float(layer_stats['compression_ratio'])
                    
                    # 计算bits per pixel (bpp)
                    bpp = float(layer_stats['effective_bits']) / (imH * imW)
                    stats[f'{prefix}_bpp'] = bpp
            
        # 添加lambda比较信息
        lambda_comparison = {}
        for stat_block_output in stats_all:
            if 'kl_dict' in stat_block_output and 'lambda_from_ratio' in stat_block_output['kl_dict'] and 'effective_lambda_in_prior' in stat_block_output['kl_dict']:
                kl_d = stat_block_output['kl_dict']
                if 'layer_stats' in kl_d and 'layer_id' in kl_d['layer_stats']:
                    layer_id = kl_d['layer_stats']['layer_id']
                    lambda_target = kl_d['lambda_from_ratio']
                    lambda_effective = kl_d['effective_lambda_in_prior']
                    # 简化层名称显示
                    if "_z" in layer_id:
                        z_dim = layer_id.split("_z")[-1]
                        simple_name = f"z{z_dim}"
                    else:
                        simple_name = f"layer_{layer_id[-2:]}"
                    
                    stats[f'{simple_name}_lambda_target'] = float(lambda_target)
                    stats[f'{simple_name}_lambda_effective'] = float(lambda_effective)
                    stats[f'{simple_name}_lambda_diff'] = float(lambda_effective - lambda_target)
        
        return stats

    @torch.no_grad()
    def forward_eval(self, *args, **kwargs):
        """ a dummy function for evaluation """
        return self.forward(*args, **kwargs)

    @torch.no_grad()
    def uncond_sample(self, nhw_repeat, temprature=1.0):
        """ unconditionally sample, ie, generate new images """
        feature = self.decoder.forward_uncond(nhw_repeat, t=temprature)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    @torch.no_grad()
    def cond_sample(self, latents, nhw_repeat=None, temprature=1.0, paint_box=None):
        """ conditional sampling with latents """
        feature = self.decoder.forward_with_latents(latents, nhw_repeat, t=temprature, paint_box=paint_box)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    def forward_get_latents(self, im):
        """ forward pass and return all the latent variables """
        self.eval() 
        x = self.preprocess_input(im.to(self._dummy.device))
        enc_features = self.encoder(x)
        _, stats_all = self.decoder(enc_features, get_latents=True) 
        return stats_all 

    @torch.no_grad()
    def inpaint(self, im, paint_box, steps=1, temprature=1.0):
        """ Inpainting """
        nB, imC, imH, imW = im.shape
        x1, y1, x2, y2 = paint_box
        h_slice = slice(round(y1*imH), round(y2*imH))
        w_slice = slice(round(x1*imW), round(x2*imW))
        im_input = im.clone()
        for i in range(steps):
            stats_from_current_image = self.forward_get_latents(im_input)
            latents_for_resampling = [st['z'] for st in stats_from_current_image] 

            im_sample = self.cond_sample(latents_for_resampling, temprature=temprature, paint_box=paint_box)
            torch.clamp_(im_sample, min=0, max=1)
            im_input = im.clone() 
            im_input[:, :, h_slice, w_slice] = im_sample[:, :, h_slice, w_slice] 
        return im_sample


    def compress_mode(self, mode=True):
        """ Prepare for entropy coding. """
        if mode:
            self.decoder.update() 
            if hasattr(self.out_net, 'compress'):
                self.out_net.update()
        self.compressing = mode

    @torch.no_grad()
    def compress(self, im):
        """ compress a batch of images """
        x = self.preprocess_input(im.to(self._dummy.device))
        enc_features = self.encoder(x)
        compressed_obj_from_decoder, feature = self.decoder.compress(enc_features)
        
        min_res = min(enc_features.keys())
        compressed_obj_from_decoder.append(tuple(enc_features[min_res].shape)) 
        
        # 计算比特大小统计
        total_symbols = 0
        total_bits = 0
        total_qam_items = []
        
        for comp_data in compressed_obj_from_decoder:
            if isinstance(comp_data, dict) and "symbol_indices" in comp_data:
                symbol_indices = comp_data["symbol_indices"]
                qam_order = comp_data.get("qam_order", 256)  # 默认256-QAM
                pam_levels_count = comp_data.get("pam_levels_count", 16)  # 默认16 PAM电平
                
                num_symbols = symbol_indices.numel()
                bits_per_symbol = math.log2(qam_order)  # 每个符号的比特数 = log2(QAM阶数)
                bits_size = num_symbols * bits_per_symbol
                
                total_symbols += num_symbols
                total_bits += bits_size
                
                # 记录每层的QAM阶数
                total_qam_items.append({
                    "qam_order": int(qam_order),
                    "symbols": num_symbols,
                    "bits": bits_size
                })
        
        # 记录压缩统计信息
        compression_stats = {
            "total_symbols": total_symbols,
            "total_bits": float(total_bits),
            "bits_per_pixel": float(total_bits / (im.shape[2] * im.shape[3]) if im.shape[2] * im.shape[3] > 0 else 0),
            "qam_items": total_qam_items
        }
        
        # 将统计信息添加到压缩数据中
        compressed_obj_from_decoder.append(compression_stats)
        
        if hasattr(self.out_net, 'compress'): 
            x_tgt = self.preprocess_target(im)
            final_out_net_strings = self.out_net.compress(feature, x_tgt)
            compressed_obj_from_decoder.append(final_out_net_strings)
        
        # 打印详细的压缩统计信息
        print(f"压缩统计: 总符号数={total_symbols}, 总比特数={total_bits:.1f}, bpp={compression_stats['bits_per_pixel']:.4f}")
        print("自适应QAM详情:")
        qam_order_counts = {}
        for item in total_qam_items:
            qam_order = item["qam_order"]
            if qam_order not in qam_order_counts:
                qam_order_counts[qam_order] = {"symbols": 0, "bits": 0}
            qam_order_counts[qam_order]["symbols"] += item["symbols"]
            qam_order_counts[qam_order]["bits"] += item["bits"]
        
        for qam_order, stats in sorted(qam_order_counts.items()):
            print(f"  {qam_order}-QAM: {stats['symbols']} 符号, {stats['bits']:.1f} 比特, {math.log2(qam_order):.1f} 比特/符号")
              
        return compressed_obj_from_decoder

    @torch.no_grad()
    def decompress(self, compressed_object_list: list):
        """ decompress a compressed_object_list """
        # 提取并移除压缩统计信息（如果存在）
        compression_stats = None
        for i, item in enumerate(compressed_object_list):
            if isinstance(item, dict) and "total_symbols" in item and "total_bits" in item:
                compression_stats = compressed_object_list.pop(i)
                break
        
        if compression_stats:
            print(f"解压统计: 总符号数={compression_stats['total_symbols']}, "
                  f"总比特数={compression_stats['total_bits']:.1f}, "
                  f"bpp={compression_stats['bits_per_pixel']:.4f}")
            
            # 打印自适应QAM详情
            if "qam_items" in compression_stats:
                print("自适应QAM详情:")
                qam_order_counts = {}
                for item in compression_stats["qam_items"]:
                    qam_order = item["qam_order"]
                    if qam_order not in qam_order_counts:
                        qam_order_counts[qam_order] = {"symbols": 0, "bits": 0}
                    qam_order_counts[qam_order]["symbols"] += item["symbols"]
                    qam_order_counts[qam_order]["bits"] += item["bits"]
                
                for qam_order, stats in sorted(qam_order_counts.items()):
                    print(f"  {qam_order}-QAM: {stats['symbols']} 符号, {stats['bits']:.1f} 比特, {math.log2(qam_order):.1f} 比特/符号")
        
        if hasattr(self.out_net, 'compress'): 
            decoder_input_list = compressed_object_list[:-1] 
            out_net_strings = compressed_object_list[-1]
            
            feature = self.decoder.decompress(decoder_input_list)
            x_hat = self.out_net.decompress(feature, out_net_strings)
        else: 
            feature = self.decoder.decompress(compressed_object_list)
            x_hat = self.out_net.mean(feature) 
        im_hat = self.process_output(x_hat)
        return im_hat

    @torch.no_grad()
    def compress_file(self, img_path, output_path):
        """ Compress an image file and save to `output_path` """
        self.eval()
        img = Image.open(img_path)
        original_size = (img.height, img.width)
        img_padded = pad_divisible_by(img, div=self.max_stride)
        im_tensor = tvf.to_tensor(img_padded).unsqueeze_(0)
        
        compressed_obj_list = self.compress(im_tensor) 
        
        data_to_save = {"compressed_payload": compressed_obj_list, "original_size": original_size}
        
        with open(output_path, 'wb') as f:
            pickle.dump(data_to_save, file=f)

    @torch.no_grad()
    def decompress_file(self, bits_path):
        """ Decompress a bits file """
        self.eval()
        with open(bits_path, 'rb') as f:
            saved_data = pickle.load(file=f)
        
        compressed_obj_list = saved_data["compressed_payload"]
        img_h, img_w = saved_data["original_size"]
        
        im_hat_tensor = self.decompress(compressed_obj_list) 
        
        im_hat_cropped = im_hat_tensor[:, :, :img_h, :img_w]
        im_pil = tvf.to_pil_image(im_hat_cropped.squeeze(0).cpu())
        return im_pil

    def collect_lambda_history(self):
        """
        收集所有层级的lambda历史记录
        
        Returns:
            dict: 每个层的lambda历史记录，格式为 {layer_name: [(step, lambda_value), ...]}
        """
        lambda_history = {}
        
        # 遍历所有QLatentBlockX层
        for name, module in self.decoder.named_modules():
            if isinstance(module, QLatentBlockX) and hasattr(module.mb_prior, 'lambda_history'):
                # 获取lambda历史
                history = module.mb_prior.lambda_history.detach().cpu().numpy()
                # 只保留已经记录的部分
                valid_idx = module.mb_prior.history_index.item() % 10000
                if valid_idx > 0:
                    history = history[:valid_idx]
                
                layer_name = module.layer_id
                lambda_history[layer_name] = history
                
        return lambda_history
    
    def plot_lambda_history(self, save_path=None):
        """
        绘制每层lambda值随训练步骤的变化图
        
        Args:
            save_path (str, optional): 保存图像的路径。如果为None，则显示图像。
        """
        import matplotlib.pyplot as plt
        import numpy as np
        
        lambda_history = self.collect_lambda_history()
        
        if not lambda_history:
            print("没有找到任何lambda历史记录")
            return
            
        # 创建图表
        plt.figure(figsize=(12, 8))
        
        # 绘制每层的lambda历史
        for layer_name, history in lambda_history.items():
            if len(history) > 0:
                steps = history[:, 0]
                lambdas = history[:, 1]
                plt.plot(steps, lambdas, label=layer_name)
        
        plt.xlabel('训练步数')
        plt.ylabel('Lambda值')
        plt.title('不同层级的Lambda值变化')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            plt.savefig(save_path)
            print(f"Lambda历史图已保存到 {save_path}")
        else:
            plt.show()

    def print_param_indices(self, indices_to_print=None):
        """打印模型参数的索引和名称，用于调试分布式训练中的未使用参数问题
        
        Args:
            indices_to_print: 要打印的参数索引列表，如果为None则打印所有参数
        """
        if indices_to_print is None:
            print("\n===== 所有模型参数索引和名称 =====")
            for i, (name, param) in enumerate(self.named_parameters()):
                print(f"索引 {i}: {name}, 形状 {param.shape}")
            print("===== 参数打印完毕 =====\n")
        else:
            print("\n===== 指定的模型参数索引和名称 =====")
            all_params = list(self.named_parameters())
            for i in indices_to_print:
                if 0 <= i < len(all_params):
                    name, param = all_params[i]
                    print(f"索引 {i}: {name}, 形状 {param.shape}")
                else:
                    print(f"索引 {i}: 超出参数范围")
            print("===== 参数打印完毕 =====\n")


def pad_divisible_by(img, div=64):
    """ Pad an PIL.Image at right and bottom border """
    h_old, w_old = img.height, img.width
    
    h_target = math.ceil(h_old / div) * div
    w_target = math.ceil(w_old / div) * div

    if h_old == h_target and w_old == w_target:
        return img

    padding = (0, 0, (w_target - w_old), (h_target - h_old))
    padded_img = tvf.pad(img, padding=padding, padding_mode='edge')
    return padded_img

# 在损失函数中添加正则化，防止lambda变得过小
def lambda_regularization_loss(model):
    lambda_values = []
    for name, module in model.named_modules():
        if hasattr(module, 'current_lambda'):
            lambda_values.append(module.current_lambda)
    
    if lambda_values:
        # 防止lambda变得过小的正则化
        lambda_penalty = sum(torch.exp(-5 * lambda_val) for lambda_val in lambda_values)
        return lambda_penalty
    return 0.0

# # 在总损失中添加这个正则化项
# total_loss = reconstruction_loss + kl_loss + 0.01 * lambda_regularization_loss(model)