import pickle
from collections import OrderedDict
from PIL import Image
import math
import torch
import torch.nn as nn
import torch.nn.functional as tnf
import torch.distributions as td
import torchvision.transforms.functional as tvf
from compressai.entropy_models import GaussianConditional
import numpy as np
import random
import lvae.models.common as common
from lvae.models.entropy_coding import gaussian_log_prob_mass

# 延迟导入Channel类，避免循环导入
def get_channel_class():
    from .channel import Channel
    return Channel


class AdaptiveTransmissionEncoder(nn.Module):
    """基于概率的自适应传输编码器 - 修复eta参数控制"""

    def __init__(self, rate_choice=[0.01, 0.05, 0.1, 0.3, 0.5, 0.7, 1.0]):
        """
        Args:
            rate_choice: 可选的传输比例列表，从低到高 - 扩展到支持极低传输比例
        """
        super().__init__()
        self.rate_choice = rate_choice
        self.rate_num = len(rate_choice)
        self.register_buffer("rate_choice_tensor", torch.tensor(np.asarray(rate_choice)))

    def compute_information_content(self, qm_probs):
        """计算信息量：I(x) = -log₂(P(x))

        Args:
            qm_probs: qm的概率 [B, C, H, W]

        Returns:
            information_content: 信息量 [B, C, H, W]
        """
        # 确保概率在有效范围内
        qm_probs = torch.clamp(qm_probs, min=1e-10, max=1.0)

        # 计算信息量：I(x) = -log₂(P(x))
        information_content = -torch.log2(qm_probs)

        return information_content

    def adaptive_rate_selection(self, qm_probs, eta=0.5):
        """基于概率的自适应速率选择

        Args:
            qm_probs: qm的概率 [B, C, H, W]
            eta: 信息量到传输比例的缩放因子

        Returns:
            transmission_mask: 传输掩码 [B, C, H, W]
            rate_stats: 传输统计信息
        """
        B, C, H, W = qm_probs.shape

        # 1. 计算信息量
        information_content = self.compute_information_content(qm_probs)

        # 2. 计算每个空间位置的总信息量（跨通道求和）
        spatial_info = torch.sum(information_content, dim=1)  # [B, H, W]

        # 3. 应用eta缩放
        scaled_info = spatial_info * eta  # [B, H, W]

        # 4. 基于信息量选择传输比例
        # 将信息量映射到rate_choice的索引
        max_info = scaled_info.max()
        min_info = scaled_info.min()

        if max_info > min_info:
            # 归一化到[0, rate_num-1]范围
            normalized_info = (scaled_info - min_info) / (max_info - min_info) * (self.rate_num - 1)
        else:
            # 如果信息量相同，使用中等传输比例
            normalized_info = torch.full_like(scaled_info, self.rate_num // 2)

        # 5. 选择传输比例索引
        rate_indices = torch.round(normalized_info).long().clamp(0, self.rate_num - 1)

        # 🔥 极速优化：完全向量化的传输掩码生成
        # 6. 生成传输掩码 - 无循环版本
        transmission_mask = torch.zeros_like(qm_probs, dtype=torch.bool)

        # 🔥 修复：直接使用eta作为传输比例，忽略复杂的信息量映射
        target_transmission_ratio = min(max(eta, 0.001), 1.0)  # 限制在0.1%-100%范围内

        # 计算每个元素的重要性（展平处理）
        flattened_info = information_content.view(B, -1)  # [B, C*H*W]

        # 根据eta直接确定传输元素数量
        total_elements_per_batch = C * H * W
        num_transmit_per_batch = max(1, int(total_elements_per_batch * target_transmission_ratio))

        # 为每个batch选择最重要的元素
        for b in range(B):
            # 选择当前batch中最重要的元素
            _, top_indices = torch.topk(flattened_info[b], num_transmit_per_batch)

            # 将一维索引转换回多维索引
            c_indices = top_indices // (H * W)
            hw_indices = top_indices % (H * W)
            h_indices = hw_indices // W
            w_indices = hw_indices % W

            transmission_mask[b, c_indices, h_indices, w_indices] = True

        # 7. 统计信息
        total_elements = qm_probs.numel()
        transmitted_elements = transmission_mask.sum().item()

        rate_stats = {
            'total_elements': total_elements,
            'transmitted_elements': transmitted_elements,
            'transmission_ratio': transmitted_elements / total_elements,
            'target_transmission_ratio': target_transmission_ratio,  # 添加目标传输比例
            'avg_information_content': information_content.mean().item(),
            'avg_transmitted_info': information_content[transmission_mask].mean().item() if transmitted_elements > 0 else 0.0,
            'eta_used': eta  # 记录使用的eta值
        }

        return transmission_mask, rate_stats



    def forward(self, qm, qm_probs, eta=0.5):
        """前向传播：基于概率的自适应传输 - 不可微分版本

        Args:
            qm: 潜变量均值 [B, C, H, W]
            qm_probs: qm的概率 [B, C, H, W]
            eta: 传输控制参数

        Returns:
            qm_masked: 掩码后的qm
            transmission_weights: 传输权重/掩码
            rate_stats: 传输统计
        """
        # 🔥 修复：始终使用不可微分的硬掩码，避免梯度传播问题
        with torch.no_grad():
            transmission_weights, rate_stats = self.adaptive_rate_selection(qm_probs, eta)

        # 应用权重/掩码 - 使用detach()确保不参与梯度计算
        qm_masked = qm * transmission_weights.detach()

        return qm_masked, transmission_weights, rate_stats


class GaussianNLLOutputNet(nn.Module):
    def __init__(self, conv_mean, conv_scale, bin_size=1/127.5):
        super().__init__()
        self.conv_mean  = conv_mean
        self.conv_scale = conv_scale
        self.bin_size = bin_size
        self.loss_name = 'nll'

    def forward_loss(self, feature, x_tgt):
        """ compute negative log-likelihood loss

        Args:
            feature (torch.Tensor): feature given by the top-down decoder
            x_tgt (torch.Tensor): original image
        """
        feature = feature.float()
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_logscale = tnf.softplus(p_logscale + 16) - 16 # logscale lowerbound
        log_prob = gaussian_log_prob_mass(p_mean, torch.exp(p_logscale), x_tgt, bin_size=self.bin_size)
        assert log_prob.shape == x_tgt.shape
        nll = -log_prob.mean(dim=(1,2,3)) # BCHW -> (B,)
        return nll, p_mean

    def mean(self, feature):
        p_mean = self.conv_mean(feature)
        return p_mean

    def sample(self, feature, mode='continuous', temprature=None):
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_scale = torch.exp(p_logscale)
        if temprature is not None:
            p_scale = p_scale * temprature

        if mode == 'continuous':
            samples = p_mean + p_scale * torch.randn_like(p_mean)
        elif mode == 'discrete':
            raise NotImplementedError()
        else:
            raise ValueError()
        return samples

    def update(self):
        self.discrete_gaussian = GaussianConditional(None, scale_bound=0.11)
        device = next(self.parameters()).device
        self.discrete_gaussian = self.discrete_gaussian.to(device=device)
        lower = self.discrete_gaussian.lower_bound_scale.bound.item()
        max_scale = 20
        scale_table = torch.exp(torch.linspace(math.log(lower), math.log(max_scale), steps=128))
        updated = self.discrete_gaussian.update_scale_table(scale_table)
        self.discrete_gaussian.update()

    def _preapre_codec(self, feature, x=None):
        assert not feature.requires_grad
        pm = self.conv_mean(feature)
        pm = torch.round(pm * 127.5 + 127.5) / 127.5 - 1 # workaround to make sure lossless
        plogv = self.conv_scale(feature)
        # scale (-1,1) range to (-127.5, 127.5) range
        pm = pm / self.bin_size
        plogv = plogv - math.log(self.bin_size)
        if x is not None:
            x = x / self.bin_size
        return pm, plogv, x

    def compress(self, feature, x):
        pm, plogv, x = self._preapre_codec(feature, x)
        # compress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        strings = self.discrete_gaussian.compress(x, indexes, means=pm)
        return strings

    def decompress(self, feature, strings):
        pm, plogv, _ = self._preapre_codec(feature)
        # decompress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        x_hat = self.discrete_gaussian.decompress(strings, indexes, means=pm)
        x_hat = x_hat * self.bin_size
        return x_hat


class MSEOutputNet(nn.Module):
    def __init__(self, mse_lmb):
        super().__init__()
        self.mse_lmb = float(mse_lmb)
        self.loss_name = 'mse'

    def forward_loss(self, x_hat, x_tgt):
        """ compute MSE loss

        Args:
            x_hat (torch.Tensor): reconstructed image
            x_tgt (torch.Tensor): original image
        """
        assert x_hat.shape == x_tgt.shape
        mse = tnf.mse_loss(x_hat, x_tgt, reduction='none').mean(dim=(1,2,3)) # (B,3,H,W) -> (B,)
        loss = mse * self.mse_lmb
        return loss, x_hat

    def mean(self, x_hat, temprature=None):
        return x_hat
    sample = mean


class VDBlock(nn.Module):
    """ Adapted from VDVAE (https://github.com/openai/vdvae)
    - Paper: Very Deep VAEs Generalize Autoregressive Models and Can Outperform Them on Images
    - arxiv: https://arxiv.org/abs/2011.10650
    """
    def __init__(self, in_ch, hidden_ch=None, out_ch=None, residual=True,
                 use_3x3=True, zero_last=False):
        super().__init__()
        out_ch = out_ch or in_ch
        hidden_ch = hidden_ch or round(in_ch * 0.25)
        self.in_channels = in_ch
        self.out_channels = out_ch
        self.residual = residual
        self.c1 = common.conv_k1s1(in_ch, hidden_ch)
        self.c2 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c3 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c4 = common.conv_k1s1(hidden_ch, out_ch, zero_weights=zero_last)

    def residual_scaling(self, N):
        # This residual scaling improves stability and performance with many layers
        # https://arxiv.org/pdf/2011.10650.pdf, Appendix Table 3
        self.c4.weight.data.mul_(math.sqrt(1 / N))

    def forward(self, x):
        xhat = self.c1(tnf.gelu(x))
        xhat = self.c2(tnf.gelu(xhat))
        xhat = self.c3(tnf.gelu(xhat))
        xhat = self.c4(tnf.gelu(xhat))
        out = (x + xhat) if self.residual else xhat
        return out

class VDBlockPatchDown(VDBlock):
    def __init__(self, in_ch, out_ch, down_rate=2):
        super().__init__(in_ch, residual=True)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


from timm.models.convnext import ConvNeXtBlock
class MyConvNeXtBlock(ConvNeXtBlock):
    def __init__(self, dim, mlp_ratio=2, kernel_size=7, **kwargs):
        # 过滤掉timm.ConvNeXtBlock不支持的kernel_size参数
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'kernel_size'}
        super().__init__(dim, mlp_ratio=mlp_ratio, **filtered_kwargs)
        self.norm.affine = True # this variable is useless. just a workaround for flops computation

        # 如果需要自定义kernel_size，需要重新创建conv_dw层
        if kernel_size != 7:  # timm默认是7
            pad = (kernel_size - 1) // 2
            self.conv_dw = nn.Conv2d(dim, dim, kernel_size=kernel_size, padding=pad, groups=dim)

    def forward(self, x):
        shortcut = x
        x = self.conv_dw(x)
        if self.use_conv_mlp:
            x = self.norm(x)
            x = self.mlp(x)
        else:
            x = x.permute(0, 2, 3, 1).contiguous()
            x = self.norm(x)
            x = self.mlp(x)
            x = x.permute(0, 3, 1, 2).contiguous()
        if self.gamma is not None:
            x = x.mul(self.gamma.reshape(1, -1, 1, 1))
        x = self.drop_path(x) + shortcut
        return x

class MyConvNeXtPatchDown(MyConvNeXtBlock):
    def __init__(self, in_ch, out_ch, down_rate=2, mlp_ratio=2, kernel_size=7):
        super().__init__(in_ch, mlp_ratio=mlp_ratio, kernel_size=kernel_size)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


class BottomUpEncoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.enc_blocks = nn.ModuleList(blocks)

    def forward(self, x):
        feature = x
        enc_features = dict()
        for i, block in enumerate(self.enc_blocks):
            feature = block(feature)
            res = int(feature.shape[2])
            enc_features[res] = feature
        return enc_features


class QLatentBlockX(nn.Module):
    """ Latent block as described in the paper.
    """
    def __init__(self, width, zdim, enc_width=None, kernel_size=7,
                 snr_db=20.0, direct_noise=True, verbose=False, eta=0.5):
        """初始化QLatentBlockX

        Args:
            width: 块宽度
            zdim: 隐变量维度
            enc_width: 编码器宽度(可选)
            kernel_size: 卷积核大小
            snr_db: 信噪比(dB)
            direct_noise: 是否启用直接噪声模式
            verbose: 是否启用详细输出
            eta: 自适应传输控制参数
        """
        super().__init__()
        self.width = width
        self.enc_width = enc_width or width
        self.zdim = zdim
        self.residual = width == zdim

        # 初始化状态变量
        self.entropy = 0.0    # 默认熵值
        self.symbol_count = 0
        self.actual_bits = 0
        self.mb_kl_loss = 0.0

        self.in_channels  = width
        self.out_channels = width

        # 🔥 新方案：每个QLatentBlockX创建自己的Channel实例
        from .channel import Channel
        self.channel = Channel(
            noise_type='awgn',
            snr=snr_db,
            verbose=verbose,
            direct_noise=direct_noise,
            random_seed=42
        )

        # 添加统计变量
        self.last_qm = None  # 最近的qm值，用于熵计算
        self.actual_bits = 0  # 实际使用的比特数
        self.verbose = verbose  # 详细输出控制

        enc_width = enc_width or width
        hidden = int(max(width, enc_width) * 0.25)
        concat_ch = (width * 2) if enc_width is None else (width + enc_width)
        use_3x3 = (kernel_size >= 3)
        self.resnet_front = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.resnet_end   = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.posterior = VDBlock(concat_ch, hidden, zdim, residual=False, use_3x3=use_3x3)
        self.prior     = VDBlock(width, hidden, zdim * 2, residual=False, use_3x3=use_3x3,
                                 zero_last=True)
        self.z_proj = nn.Sequential(
            common.conv_k3s1(zdim, hidden//2) if use_3x3 else common.conv_k1s1(zdim, hidden//2),
            nn.GELU(),
            common.conv_k1s1(hidden//2, width),
        )
        self.discrete_gaussian = GaussianConditional(None)

        # 🔥 自适应传输相关属性
        self.adaptive_transmission = True
        self.adaptive_encoder = AdaptiveTransmissionEncoder([0.01, 0.05, 0.1, 0.3, 0.5, 0.7, 1.0])  # 支持极低传输比例
        self.transmission_eta = eta  # 使用传入的eta参数
        self.last_transmission_stats = None
        self.last_transmission_mask = None

        # 🔥 添加Maxwell-Boltzmann λ参数预测网络（类似qresvae的prior VDBlock）
        # 这样可以避免动态创建网络层，确保EMA正确处理
        self.mb_lambda_predictor = VDBlock(
            width, hidden, 2,  # 输出2个通道：lambda_mean和lambda_logvar
            residual=False, use_3x3=use_3x3, zero_last=True
        )

    def residual_scaling(self, N):
        self.z_proj[2].weight.data.mul_(math.sqrt(1 / (3*N)))

    def transform_prior(self, feature):
        """ prior p(z_i | z_<i) - Gaussian版本（保留兼容性）

        Args:
            feature (torch.Tensor): feature map
        """
        feature = self.resnet_front(feature)
        # prior p(z)
        pm, plogv = self.prior(feature).chunk(2, dim=1)
        plogv = tnf.softplus(plogv + 2.3) - 2.3 # make logscale > -2.3
        return feature, pm, plogv


    def _apply_channel(self, qm, current_res, is_training, transmission_mask=None):
        """应用信道模块处理量化媒体 - 支持真正的masked_select操作"""
        if not isinstance(qm, torch.Tensor) or not hasattr(self, 'channel') or self.channel is None:
            return qm

        try:
            # 获取先验分布参数（如果可用）
            pm = getattr(self, '_current_pm', None)
            pv = getattr(self, '_current_pv', None)

            if pm is not None and pv is not None:
                # 1. 重要性评估：基于概率分布计算重要性
                importance_map = self._compute_importance_map(qm, pm, pv)

                # 2. Maxwell-Boltzmann整形：根据重要性调整概率分布
                shaped_qm, mb_info = self._apply_mb_shaping(qm, importance_map, is_training)
            else:
                # 如果没有先验分布参数，跳过重要性感知处理
                shaped_qm = qm
                mb_info = None
                if self.verbose:
                    print("警告: 缺少先验分布参数，跳过重要性感知处理")

            # 3. 准备信道处理
            feature_dict = {current_res: shaped_qm}

            # 🔥 关键改进：如果有传输掩码，使用真正的masked_select
            if transmission_mask is not None:
                transmission_masks = {current_res: transmission_mask}
                processed_features, transmission_stats = self.channel(
                    feature_dict,
                    is_training=is_training,
                    transmission_masks=transmission_masks
                )

                # 保存传输统计信息
                if hasattr(self, 'last_transmission_stats'):
                    self.last_transmission_stats.update(transmission_stats.get(current_res, {}))

                if self.verbose:
                    stats = transmission_stats.get(current_res, {})
                    print(f"🔥 真实压缩: {stats.get('original_elements', 0)} → {stats.get('transmitted_elements', 0)} 元素")
                    print(f"   压缩率: {stats.get('compression_ratio', 0):.3f}")
            else:
                # 原有的处理方式
                processed_features = self.channel(feature_dict, is_training=is_training)

            if isinstance(processed_features, dict) and current_res in processed_features:
                processed_qm = processed_features[current_res]
                if isinstance(processed_qm, torch.Tensor) and processed_qm.numel() == qm.numel():
                    return processed_qm.reshape(qm.shape)
                return processed_qm
            return qm
        except Exception as e:
            if self.verbose:
                print(f"信道处理失败: {e}，使用备用方案")
            return qm + torch.randn_like(qm) * 0.01

    def forward_train(self, feature, enc_feature, get_latents=False, snr_db=None, use_mb_prior=True):
        """训练模式前向传播

        Args:
            feature: 特征图
            enc_feature: 编码器特征图
            get_latents: 是否返回潜变量
            snr_db: 信噪比（用于Maxwell-Boltzmann）
            use_mb_prior: 是否使用Maxwell-Boltzmann先验（默认True）
        """

        # 🔥 自动从channel获取SNR参数（如果没有显式提供）
        if snr_db is None and self.channel is not None and hasattr(self.channel, 'snr'):
            snr_db = self.channel.snr
        
        # 🔥 统一使用Gaussian先验，支持Maxwell-Boltzmann等价转换
        feature, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
    
        # posterior q(z|x) 这是潜变量空间！
        qm = self.posterior(torch.cat([feature, enc_feature], dim=1))

        # 🔍 跟踪1: 记录原始qm
        if hasattr(self, 'verbose') and self.verbose:
            print(f"\n🔍 QM跟踪 - 分辨率{feature.shape[2]}:")
            print(f"  原始qm: 形状{qm.shape}, 范围[{qm.min():.4f}, {qm.max():.4f}], 均值{qm.mean():.4f}, 标准差{qm.std():.4f}")
            # 保存原始qm用于后续对比
            self._original_qm = qm.clone().detach()

        # 🔥 计算qm的概率分布并应用自适应传输
        # 计算qm在先验分布下的概率
        qm_probs = torch.exp(gaussian_log_prob_mass(pm, pv, x=qm, bin_size=1.0, prob_clamp=1e-6))

        # 应用自适应传输
        eta = self.transmission_eta
        qm_masked, transmission_mask, rate_stats = self.adaptive_encoder(qm, qm_probs, eta)

        # 保存统计信息用于监控
        self.last_transmission_stats = rate_stats
        self.last_transmission_mask = transmission_mask

        # 🔥 关键修复：训练和压缩都对qm_masked应用信道处理，保持一致性
        if hasattr(self, 'verbose') and self.verbose:
            print(f"🔄 训练模式信道处理: qm_masked形状{qm_masked.shape}, 范围[{qm_masked.min():.4f}, {qm_masked.max():.4f}]")

        qm_processed = self._apply_channel(qm_masked, feature.shape[2], is_training=self.training, transmission_mask=transmission_mask)

        if hasattr(self, 'verbose') and self.verbose:
            print(f"🔄 训练模式信道处理后: qm_processed形状{qm_processed.shape}, 范围[{qm_processed.min():.4f}, {qm_processed.max():.4f}]")

        # 理论上希望 z ~ N(pm, pv)
        if self.training:
            z_sample = qm_processed  # 使用信道处理后的qm
            # 实际上 z_sample ~ Uniform(qm-0.5, qm+0.5)
            log_prob = gaussian_log_prob_mass(pm, pv, x=z_sample, bin_size=1.0, prob_clamp=1e-6)
            # 量化过程就是卷积过程
            kl = -1.0 * log_prob
        else:
            z_sample, probs = self.discrete_gaussian(qm_processed, scales=pv, means=pm)
            kl = -1.0 * torch.log(probs)

        z_proj_output = self.z_proj(z_sample)
        feature = feature + z_proj_output
        feature = self.resnet_end(feature)

        # 🔥 新增：返回自适应传输统计信息
        result_dict = dict(kl=kl)
        if self.last_transmission_stats is not None:
            result_dict['transmission_stats'] = self.last_transmission_stats
        if self.last_transmission_mask is not None:
            result_dict['transmission_mask'] = self.last_transmission_mask
        
        if get_latents:
            result_dict['z'] = z_sample.detach()

        return feature, result_dict
    
    def forward_uncond(self, feature, t=1.0, latent=None, paint_box=None):
        """ Sampling mode.

        Args:
            feature   (Tensor): feature map.
            t         (float):  tempreture. Defaults to 1.0.
            latent    (Tensor): latent variable z. Sample it from prior if not provided.
            paint_box (Tensor): masked box for inpainting. (x1, y1, x2, y2).
        """
        feature, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
        pv = pv * t
        
        if latent is None: # 正常采样
            # 修改：首先生成潜在变量的分布参数
            if self.channel is not None:
                # 将pm作为原始潜在变量分布输入信道
                z_dict = {feature.shape[2]: pm}
                # 应用信道处理
                z_dict_noisy = self.channel(z_dict, is_training=True)
                # 获取处理后的pm
                pm_noisy = z_dict_noisy[feature.shape[2]]
                # 使用处理后的pm进行采样
                z = pm_noisy + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
            else:
                # 如果没有信道，直接采样
                # 确保所有参数都参与计算图
                dummy = torch.zeros_like(pm)
                pm = pm + dummy  # 无效操作，但确保参数参与计算图
                z = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
        elif paint_box is not None: # 局部修复采样
            # 类似地处理局部修复逻辑...
            nB, zC, zH, zW = latent.shape
            if min(zH, zW) == 1:
                z = latent
            else:
                x1, y1, x2, y2 = paint_box
                h_slice = slice(round(y1*zH), round(y2*zH))
                w_slice = slice(round(x1*zW), round(x2*zW))
                
                # 修改：应用相同的信道处理
                if self.channel is not None:
                    # 处理pm
                    z_dict = {feature.shape[2]: pm}
                    z_dict_noisy = self.channel(z_dict, is_training=True)
                    pm_noisy = z_dict_noisy[feature.shape[2]]
                    # 使用处理后的pm生成新样本
                    z_sample = pm_noisy + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
                else:
                    # 生成新样本 
                    # 确保所有参数都参与计算图
                    dummy = torch.zeros_like(pm)
                    pm = pm + dummy  # 无效操作，但确保参数参与计算图
                    z_sample = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
                
                z_patch = z_sample[:, :, h_slice, w_slice]
                z = torch.clone(latent)
                z[:, :, h_slice, w_slice] = z_patch
        else:
            assert pm.shape == latent.shape
            z = latent
            
        # # 🔥 处理z_proj形状不匹配的问题
        # try:
        z_proj_output = self.z_proj(z)
        feature = feature + z_proj_output
        # except RuntimeError as e:
        #     if "channels" in str(e):
        #         print(f"forward_uncond模式z_proj形状不匹配: z({z.shape}) vs z_proj期望输入通道数")

        #         # 获取z_proj期望的输入通道数
        #         expected_channels = self.z_proj[0].in_channels if hasattr(self.z_proj[0], 'in_channels') else self.zdim
        #         current_channels = z.shape[1]

        #         if current_channels < expected_channels:
        #             # 通道数不足，通过零填充扩展
        #             padding_channels = expected_channels - current_channels
        #             padding = torch.zeros(z.shape[0], padding_channels, z.shape[2], z.shape[3],
        #                                 device=z.device, dtype=z.dtype)
        #             z_padded = torch.cat([z, padding], dim=1)
        #             z_proj_output = self.z_proj(z_padded)
        #             print(f"forward_uncond模式通过零填充扩展z: {z.shape} -> {z_padded.shape}")
        #         elif current_channels > expected_channels:
        #             # 通道数过多，截取前面的通道
        #             z_truncated = z[:, :expected_channels, :, :]
        #             z_proj_output = self.z_proj(z_truncated)
        #             print(f"forward_uncond模式截取z通道: {z.shape} -> {z_truncated.shape}")
        #         else:
        #             # 通道数相同但仍然出错，重新抛出异常
        #             raise e

        #         feature = feature + z_proj_output
        #     else:
        #         # 其他类型的错误，重新抛出
        #         raise e

        feature = self.resnet_end(feature)
        return feature

    def get_stats(self):
        """获取符号统计信息 - 考虑自适应传输的影响"""
        # 🔥 修复：使用当前QAM阶数重新计算比特数，确保压缩和解压缩统计一致
        current_qam_order = getattr(self, 'qam_order', 256)
        symbol_count = getattr(self, 'symbol_count', 0)

        # 🔥 新增：考虑自适应传输的实际传输符号数
        actual_transmitted_symbols = symbol_count
        if hasattr(self, 'last_transmission_stats') and self.last_transmission_stats is not None:
            # 使用自适应传输的实际传输元素数
            transmitted_elements = self.last_transmission_stats.get('transmitted_elements', symbol_count)
            total_elements = self.last_transmission_stats.get('total_elements', symbol_count)
            transmission_ratio = self.last_transmission_stats.get('transmission_ratio', 1.0)

            # 计算实际传输的符号数
            actual_transmitted_symbols = int(symbol_count * transmission_ratio)

            if hasattr(self, 'verbose') and self.verbose:
                print(f"  自适应传输统计: 原始符号={symbol_count}, 实际传输={actual_transmitted_symbols}, 传输比例={transmission_ratio:.3f}")

        # 重新计算比特数，使用实际传输的符号数
        current_bits = actual_transmitted_symbols * math.log2(current_qam_order) if current_qam_order > 0 and actual_transmitted_symbols > 0 else 0.0

        return {
            'symbols': actual_transmitted_symbols,  # 使用实际传输的符号数
            'original_symbols': symbol_count,       # 保留原始符号数用于参考
            'entropy': getattr(self, 'entropy', 0.0),
            'bits': current_bits,  # 使用重新计算的比特数
            'qam_order': current_qam_order,
            'zdim': self.zdim,
            'bits_per_symbol': math.log2(current_qam_order) if current_qam_order > 0 else 0.0,
            'transmission_ratio': getattr(self, 'last_transmission_stats', {}).get('transmission_ratio', 1.0) if hasattr(self, 'last_transmission_stats') else 1.0
        }


    def update(self):
        """ Prepare for entropy coding. Musted be called before compression.
        """
        min_scale = 0.1
        max_scale = 20
        log_scales = torch.linspace(math.log(min_scale), math.log(max_scale), steps=64)
        scale_table = torch.exp(log_scales)
        updated = self.discrete_gaussian.update_scale_table(scale_table)
        self.discrete_gaussian.update()

    def compress(self, feature, enc_feature):
        """Forward pass, compression (encoding) mode."""
        feature, pm, plogv = self.transform_prior(feature)
        qm = self.posterior(torch.cat([feature, enc_feature], dim=1))
        self.last_qm = qm.detach()

        # 🔍 跟踪1: 压缩模式的原始qm
        if hasattr(self, 'verbose') and self.verbose:
            print(f"\n🔍 压缩模式QM跟踪 - 分辨率{feature.shape[2]}:")
            print(f"  原始qm: 形状{qm.shape}, 范围[{qm.min():.4f}, {qm.max():.4f}], 均值{qm.mean():.4f}, 标准差{qm.std():.4f}")
            self._compress_original_qm = qm.clone().detach()

        # 🔥 关键修复：压缩时也要使用adaptive_encoder，与训练保持一致
        # 始终计算pv（确保一致性）
        pv = torch.exp(plogv)

        # 始终使用自适应处理
        qm_for_compression = qm
        # 计算qm的概率分布（用于重要性感知）
        qm_probs = torch.exp(gaussian_log_prob_mass(pm, pv, x=qm, bin_size=1.0, prob_clamp=1e-6))

        # 应用自适应传输
        eta = self.transmission_eta
        qm_masked, transmission_mask, rate_stats = self.adaptive_encoder(qm, qm_probs, eta)

        # 保存统计信息用于监控
        self.last_transmission_stats = rate_stats
        self.last_transmission_mask = transmission_mask

        # 使用掩码后的qm进行压缩
        qm_for_compression = qm_masked
        print(f"压缩时使用自适应传输: 传输比例={rate_stats.get('transmission_ratio', 'N/A'):.3f}, 传输元素={rate_stats.get('transmitted_elements', 0)}/{rate_stats.get('total_elements', 0)}")

        # 保存数据用于后续分析
        if hasattr(self, '_compress_original_qm'):
            self._compress_qm_after_adaptive = qm_for_compression.clone().detach()

        # 🔥 关键修复：与训练模式保持一致，使用相同的信道处理
        if hasattr(self, 'verbose') and self.verbose:
            print(f"🔄 压缩模式信道处理: qm_masked形状{qm_for_compression.shape}, 范围[{qm_for_compression.min():.4f}, {qm_for_compression.max():.4f}]")

        qm_processed = self._apply_channel(qm_for_compression, feature.shape[2], is_training=False, transmission_mask=transmission_mask)

        if hasattr(self, 'verbose') and self.verbose:
            print(f"🔄 压缩模式信道处理后: qm_processed形状{qm_processed.shape}, 范围[{qm_processed.min():.4f}, {qm_processed.max():.4f}]")

        # 🔥 关键修复：如果qm_processed形状发生变化，需要调整pm和plogv的形状
        if qm_processed.shape != pm.shape:
            print(f"压缩模式形状调整: qm_processed({qm_processed.shape}) vs pm({pm.shape})")

            # 计算元素数量
            qm_elements = qm_processed.numel() // qm_processed.shape[0]
            pm_elements = pm.numel() // pm.shape[0]

            if qm_elements == pm_elements:
                # 元素数量相同，直接重塑
                pm = pm.reshape(qm_processed.shape)
                plogv = plogv.reshape(qm_processed.shape)
                print(f"直接重塑pm和plogv为: {qm_processed.shape}")
            elif qm_elements < pm_elements:
                # qm_processed元素较少，截取pm和plogv的前面部分
                pm_flat = pm.reshape(pm.shape[0], -1)
                plogv_flat = plogv.reshape(plogv.shape[0], -1)

                pm_truncated = pm_flat[:, :qm_elements]
                plogv_truncated = plogv_flat[:, :qm_elements]

                pm = pm_truncated.reshape(qm_processed.shape)
                plogv = plogv_truncated.reshape(qm_processed.shape)
                print(f"截取并重塑pm和plogv为: {qm_processed.shape}")
            else:
                # qm_processed元素较多，扩展pm和plogv
                pm_flat = pm.reshape(pm.shape[0], -1)
                plogv_flat = plogv.reshape(plogv.shape[0], -1)

                repeat_factor = (qm_elements + pm_elements - 1) // pm_elements
                pm_expanded = pm_flat.repeat(1, repeat_factor)[:, :qm_elements]
                plogv_expanded = plogv_flat.repeat(1, repeat_factor)[:, :qm_elements]

                pm = pm_expanded.reshape(qm_processed.shape)
                plogv = plogv_expanded.reshape(qm_processed.shape)
                print(f"扩展并重塑pm和plogv为: {qm_processed.shape}")

        zhat = qm_processed  
        raw_data = zhat 

        # 🔍 跟踪5: 最终传输的数据
        if hasattr(self, 'verbose') and self.verbose:
            print(f"  最终传输数据: 范围[{raw_data.min():.4f}, {raw_data.max():.4f}]")
            if hasattr(self, '_compress_original_qm'):
                total_change = torch.mean(torch.abs(raw_data - self._compress_original_qm))
                print(f"  总体变化: 原始qm → 传输数据，平均绝对差异 {total_change:.6f}")

                # 计算各阶段的变化贡献
                if hasattr(self, '_compress_qm_after_adaptive') and hasattr(self, '_compress_qm_after_channel'):
                    adaptive_contrib = torch.mean(torch.abs(self._compress_qm_after_adaptive - self._compress_original_qm))
                    channel_contrib = torch.mean(torch.abs(self._compress_qm_after_channel - self._compress_qm_after_adaptive))
                    print(f"  变化分解: 自适应传输贡献{adaptive_contrib:.6f}, 信道处理贡献{channel_contrib:.6f}")
            self._compress_final_data = raw_data.clone().detach()

        # 构建调制信息用于统计
        modulation_info = {
            'qam_order': getattr(self, 'qam_order', 16),
            'entropy': getattr(self, 'current_entropy', 0.0),
            'snr_db': getattr(self, 'snr_db', 20.0),
            'resolution': feature.shape[2] if len(feature.shape) > 2 else 0
        }

        # 🔥 构建输出 - 处理zhat形状不匹配的问题
        try:
            z_proj_output = self.z_proj(zhat)
            feature = feature + z_proj_output
        except RuntimeError as e:
            if "channels" in str(e):
                print(f"压缩模式z_proj形状不匹配: zhat({zhat.shape}) vs z_proj期望输入通道数")

                # 获取z_proj期望的输入通道数
                expected_channels = self.z_proj[0].in_channels if hasattr(self.z_proj[0], 'in_channels') else self.zdim
                current_channels = zhat.shape[1]

                if current_channels < expected_channels:
                    # 通道数不足，通过零填充扩展
                    padding_channels = expected_channels - current_channels
                    padding = torch.zeros(zhat.shape[0], padding_channels, zhat.shape[2], zhat.shape[3],
                                        device=zhat.device, dtype=zhat.dtype)
                    zhat_padded = torch.cat([zhat, padding], dim=1)
                    z_proj_output = self.z_proj(zhat_padded)
                    print(f"压缩模式通过零填充扩展zhat: {zhat.shape} -> {zhat_padded.shape}")
                elif current_channels > expected_channels:
                    # 通道数过多，截取前面的通道
                    zhat_truncated = zhat[:, :expected_channels, :, :]
                    z_proj_output = self.z_proj(zhat_truncated)
                    print(f"压缩模式截取zhat通道: {zhat.shape} -> {zhat_truncated.shape}")
                else:
                    # 通道数相同但仍然出错，重新抛出异常
                    raise e

                feature = feature + z_proj_output
            else:
                # 其他类型的错误，重新抛出
                raise e

        feature = self.resnet_end(feature)

        # 🔥 返回原始数据而不是熵编码字符串
        return feature, raw_data, modulation_info

    def decompress(self, feature, raw_data):
        """ Forward pass, decompression (decoding) mode.

        Args:
            feature (torch.Tensor): feature map
            raw_data (torch.Tensor): 直接传输的QAM处理后数据
        """
        feature, pm, plogv = self.transform_prior(feature)
        # 🔥 跳过熵解码：直接使用传输的原始数据
        # 不再进行熵解码，直接使用传输的QAM处理后数据
        zhat = raw_data  # 直接使用传输的数据

        # 🔍 跟踪6: 解压缩接收到的数据
        print(f"\n🔍 解压缩验证噪声传递:")
        print(f"  接收到的raw_data: 形状{raw_data.shape}, 范围[{raw_data.min():.4f}, {raw_data.max():.4f}], 均值{raw_data.mean():.4f}")
        print(f"  解压缩使用的zhat: 形状{zhat.shape}, 范围[{zhat.min():.4f}, {zhat.max():.4f}], 均值{zhat.mean():.4f}")

        try:
            z_proj_output = self.z_proj(zhat)
            feature = feature + z_proj_output
        except RuntimeError as e:
            if "channels" in str(e):
                print(f"解压缩模式z_proj形状不匹配: zhat({zhat.shape}) vs z_proj期望输入通道数")

                # 获取z_proj期望的输入通道数
                expected_channels = self.z_proj[0].in_channels if hasattr(self.z_proj[0], 'in_channels') else self.zdim
                current_channels = zhat.shape[1]

                if current_channels < expected_channels:
                    # 通道数不足，通过零填充扩展
                    padding_channels = expected_channels - current_channels
                    padding = torch.zeros(zhat.shape[0], padding_channels, zhat.shape[2], zhat.shape[3],
                                        device=zhat.device, dtype=zhat.dtype)
                    zhat_padded = torch.cat([zhat, padding], dim=1)
                    z_proj_output = self.z_proj(zhat_padded)
                    print(f"解压缩模式通过零填充扩展zhat: {zhat.shape} -> {zhat_padded.shape}")
                elif current_channels > expected_channels:
                    # 通道数过多，截取前面的通道
                    zhat_truncated = zhat[:, :expected_channels, :, :]
                    z_proj_output = self.z_proj(zhat_truncated)
                    print(f"解压缩模式截取zhat通道: {zhat.shape} -> {zhat_truncated.shape}")
                else:
                    # 通道数相同但仍然出错，重新抛出异常
                    raise e

                feature = feature + z_proj_output
            else:
                # 其他类型的错误，重新抛出
                raise e

        feature = self.resnet_end(feature)

        # 🔍 跟踪7: 解压缩最终输出
        if hasattr(self, 'verbose') and self.verbose:
            print(f"  z_proj处理后特征: 范围[{feature.min():.4f}, {feature.max():.4f}]")
            print(f"  解压缩完成，返回特征形状: {feature.shape}")

        return feature

class TopDownDecoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.dec_blocks = nn.ModuleList(blocks)

        width = self.dec_blocks[0].in_channels
        self.bias = nn.Parameter(torch.zeros(1, width, 1, 1))

        self._init_weights()

    def _init_weights(self):
        total_blocks = len([1 for b in self.dec_blocks if hasattr(b, 'residual_scaling')])
        for block in self.dec_blocks:
            if hasattr(block, 'residual_scaling'):
                block.residual_scaling(total_blocks)

    def forward(self, enc_features, get_latents=False):
        stats = []
        min_res = min(enc_features.keys())
        feature = self.bias.expand(enc_features[min_res].shape)
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_train'):
                res = int(feature.shape[2])
                f_enc = enc_features[res]
                feature, block_stats = block.forward_train(feature, f_enc, get_latents=get_latents)
                stats.append(block_stats)
            else:
                feature = block(feature)
        return feature, stats

    def forward_uncond(self, nhw_repeat=(1, 1, 1), t=1.0):
        nB, nH, nW = nhw_repeat
        feature = self.bias.expand(nB, -1, nH, nW)
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'):
                feature = block.forward_uncond(feature, t)
            else:
                feature = block(feature)
        return feature

    def forward_with_latents(self, latents, nhw_repeat=None, t=1.0, paint_box=None):
        if nhw_repeat is None:
            nB, _, nH, nW = latents[0].shape
            feature = self.bias.expand(nB, -1, nH, nW)
        else: # use defined
            nB, nH, nW = nhw_repeat
            feature = self.bias.expand(nB, -1, nH, nW)
        idx = 0
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'):
                feature = block.forward_uncond(feature, t, latent=latents[idx], paint_box=paint_box)
                idx += 1
            else:
                feature = block(feature)
        return feature

    def update(self):
        for block in self.dec_blocks:
            if hasattr(block, 'update'):
                block.update()

    def compress(self, enc_features):
        # assert len(self.bias_xs) == 1
        min_res = min(enc_features.keys())
        feature = self.bias.expand(enc_features[min_res].shape)
        strings_all = []
        channel_info_all = []  # 新增：存储所有层的channel_info
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'compress'):
                # res = block.up_rate * feature.shape[2]
                res = feature.shape[2]
                f_enc = enc_features[res]
                # 🔥 修复：处理原始数据版本的返回值
                result = block.compress(feature, f_enc)
                if len(result) == 3:
                    # 原始数据版本：返回feature, raw_data, modulation_info
                    feature, raw_data, modulation_info = result
                    strings_all.append(raw_data)  # 保存原始数据而不是熵编码字符串
                    channel_info_all.append(modulation_info)
                elif len(result) == 2:
                    # 兼容旧版本：只返回feature和raw_data
                    feature, raw_data = result
                    strings_all.append(raw_data)
                    channel_info_all.append({})
                else:
                    # 旧版本：三个返回值
                    feature, raw_data_batch, channel_info = result
                    strings_all.append(raw_data_batch)
                    channel_info_all.append(channel_info)
            else:
                feature = block(feature)
        # 修改：同时返回channel_info_all
        return strings_all, feature, channel_info_all



    def decompress(self, compressed_object: list):
    # 查找形状信息
        shape_index = -1
        
        # 在压缩对象中找到形状信息和通道信息
        for i, item in enumerate(compressed_object):
            # 查找形状信息（一个四元组）
            if isinstance(item, tuple) and len(item) == 4 and all(isinstance(x, int) for x in item):
                smallest_shape = item
                shape_index = i
                
            # 查找通道信息（一个字典列表）
            if isinstance(item, list) and all(isinstance(x, dict) for x in item if x is not None):
                channel_info_all = item
                channel_info_index = i
        
        # 如果找不到形状信息，使用默认值
        if shape_index == -1:
            smallest_shape = compressed_object[-1] if isinstance(compressed_object[-1], tuple) else (1, 384, 12, 8)
        
        # 筛选出原始数据（QAM处理后的潜在变量）
        raw_data_items = []
        for item in compressed_object:
            # 检查是否为torch.Tensor（原始数据）
            if isinstance(item, torch.Tensor):
                raw_data_items.append(item)
            # 兼容旧的字符串格式
            elif isinstance(item, list) and all(isinstance(s, bytes) for s in item if s is not None):
                raw_data_items.append(item)

        feature = self.bias.expand(smallest_shape)
        data_i = 0
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'decompress'):
                if data_i < len(raw_data_items):
                    raw_data_batch = raw_data_items[data_i]
                    data_i += 1
                    feature = block.decompress(feature, raw_data_batch)
                else:
                    print(f"警告: 缺少第{data_i}批原始数据")
                    # 处理缺少数据的情况
            else:
                feature = block(feature)
        return feature

class HierarchicalVAE(nn.Module):
    """ Class of general hierarchical VAEs
    """
    log2_e = math.log2(math.e)

    def __init__(self, config: dict):
        """ Initialize model

        Args:
            config (dict): model config dict
        """
        super().__init__()
        # 保存配置信息用于分辨率映射
        self.z_dims = config.get('z_dims', [])
        # 从zoo配置推断dec_nums（如果没有直接提供）
        if 'dec_nums' in config:
            self.dec_nums = config['dec_nums']
        else:
            # 根据z_dims推断dec_nums（回退方案）
            self.dec_nums = [1] * len(self.z_dims) if self.z_dims else []
        
        # 创建解码器块，将channel传递给每个QLatentBlockX实例
        dec_blocks = []
        for block in config.pop('dec_blocks'):
            if isinstance(block, dict) and 'type' in block and block['type'] == 'QLatentBlockX':
                # 为QLatentBlockX添加信道参数
                args = block.get('args', {}).copy()
                args['channel'] = channel
                block_instance = QLatentBlockX(**args)
                dec_blocks.append(block_instance)
            else:
                dec_blocks.append(block)
        
        self.encoder = BottomUpEncoder(blocks=config.pop('enc_blocks'))
        self.decoder = TopDownDecoder(blocks=dec_blocks)
        self.out_net = config.pop('out_net')

        self.im_shift = float(config['im_shift'])
        self.im_scale = float(config['im_scale'])
        self.max_stride = config['max_stride']
        self.register_buffer('_dummy', torch.zeros(1), persistent=False)
        self._dummy: torch.Tensor

        self._stats_log = dict()
        self._flops_mode = False
        self.compressing = False

    def preprocess_input(self, im: torch.Tensor):
        """ Shift and scale the input image

        Args:
            im (torch.Tensor): a batch of images, (N, C, H, W), values between (0, 1)
        """
        assert (im.shape[2] % self.max_stride == 0) and (im.shape[3] % self.max_stride == 0)
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im + self.im_shift) * self.im_scale
        return x

    def process_output(self, x: torch.Tensor):
        """ scale the decoder output from range (-1, 1) to (0, 1)

        Args:
            x (torch.Tensor): network decoder output, (N, C, H, W), values between (-1, 1)
        """
        assert not x.requires_grad
        im_hat = x.clone().clamp_(min=-1.0, max=1.0).mul_(0.5).add_(0.5)
        return im_hat

    def preprocess_target(self, im: torch.Tensor):
        """ Shift and scale the image to make it reconstruction target

        Args:
            im (torch.Tensor): a batch of images, (N, C, H, W), values between (0, 1)
        """
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im - 0.5) * 2.0
        return x

    def forward(self, im, return_rec=False):
        """ Forward pass for training

        Args:
            im (tensor): image, (B, 3, H, W)
            return_rec (bool, optional): if True, return the reconstructed image \
                in addition to losses. Defaults to False.

        Returns:
            dict: str -> loss
        """
        im = im.to(self._dummy.device)
        x = self.preprocess_input(im)
        x_target = self.preprocess_target(im)

        enc_features = self.encoder(x)
        
        # 收集所有QLatentBlockX的qm并映射到分辨率
        qm_values_by_res = {}
        block_to_res_map = {}  # 映射块索引到分辨率
        
        # 首先建立块索引到分辨率的映射
        for i, block in enumerate(self.decoder.dec_blocks):
            if hasattr(block, 'last_qm') and hasattr(block, 'in_channels'):
                for res, feature in enc_features.items():
                    if feature.shape[1] == block.in_channels:
                        block_to_res_map[i] = res
                        # 初始化分辨率键
                        if res not in qm_values_by_res:
                            qm_values_by_res[res] = []
                        break
        
        # 收集qm并按分辨率组织
        for i, block in enumerate(self.decoder.dec_blocks):
            if hasattr(block, 'last_qm') and i in block_to_res_map:
                res = block_to_res_map[i]
                qm_values_by_res[res].append(block.last_qm)
        
        feature, stats_all = self.decoder(enc_features)
        out_loss, x_hat = self.out_net.forward_loss(feature, x_target)
        
        # 处理KL散度
        nB, imC, imH, imW = im.shape
        kl_divergences = [stat['kl'].sum(dim=(1, 2, 3)) for stat in stats_all]
        ndims = imC * imH * imW
        kl = sum(kl_divergences) / ndims
        # 计算nats_per_dim
        nats_per_dim = kl.detach().cpu().mean(0).item()

        # 设置损失权重 - 根据数值分析调整MB KL权重
        kl_vae_weight = 1.0      # 高斯KL权重
        kl_mb_weight = 1000.0    # Maxwell-Boltzmann KL权重（增加到1000.0以产生更强约束）
        recon_weight = 1.0       # 重建损失权重

        loss = (kl_vae_weight * kl + recon_weight * out_loss ).mean(0)

        if self._flops_mode: # testing flops
            return x_hat

        # ================ Logging ================
        with torch.no_grad():
            # 计算PSNR
            im_hat = self.process_output(x_hat.detach())
            im_mse = tnf.mse_loss(im_hat, im, reduction='mean')
            psnr = -10 * math.log10(im_mse.item())
            
            # 收集所有QLatentBlockX的符号统计信息
            total_symbols = 0
            total_bits = 0
            
            # 其他现有统计代码...
            
            # 收集所有QLatentBlockX的符号统计信息
            # 按照z_dims = [16, 14, 12, 10, 8]的维度分组统计
            total_symbols = 0
            total_bits = 0
            total_entropy = 0.0

            # 按z_dims分组的统计 - 从配置中获取z_dims
            z_dims = self.z_dims  # 使用从配置中获取的z_dims
            zdim_stats = {zdim: {'symbols': 0, 'bits': 0, 'entropy': 0.0, 'count': 0} for zdim in z_dims}

            for i, block in enumerate(self.decoder.dec_blocks):
                if isinstance(block, QLatentBlockX):
                    try:
                        stats = block.get_stats()
                        if stats and 'symbols' in stats and stats['symbols'] > 0:
                            # 获取当前层的z维度
                            zdim = block.zdim

                            # 累计总统计
                            total_symbols += stats['symbols']
                            total_bits += stats['bits']
                            total_entropy += stats['entropy'] * stats['symbols']  # 权重平均

                            # 按z维度分组统计
                            if zdim in zdim_stats:
                                zdim_stats[zdim]['symbols'] += stats['symbols']
                                zdim_stats[zdim]['bits'] += stats['bits']
                                zdim_stats[zdim]['entropy'] += stats['entropy'] * stats['symbols']  # 权重累加
                                zdim_stats[zdim]['count'] += 1

                    except (TypeError, AttributeError, KeyError) as e:
                        print(f"警告: 获取统计信息时出错: {e}")
                        continue

            # 计算每个z维度的平均熵
            layer_symbols = []
            layer_entropy = []
            layer_qam = []
            layer_zdims = []  # 记录z维度而不是层索引

            for zdim in z_dims:
                if zdim_stats[zdim]['symbols'] > 0:
                    avg_entropy = zdim_stats[zdim]['entropy'] / zdim_stats[zdim]['symbols']
                    qam_order = self.entropy_estimator.select_qam_order(avg_entropy) if hasattr(self, 'entropy_estimator') else 256

                    layer_symbols.append(zdim_stats[zdim]['symbols'])
                    layer_entropy.append(avg_entropy)
                    layer_qam.append(qam_order)
                    layer_zdims.append(zdim)
            
            # 确保total_symbols不是None
            total_symbols = 0 if total_symbols is None else total_symbols
            
            if total_symbols > 0:
                avg_entropy = total_entropy / total_symbols
            else:
                avg_entropy = 0.0

        # 初始化stats字典
        stats = OrderedDict()
        stats['loss']  = loss
        stats['kl']    = nats_per_dim
        if hasattr(self.out_net, 'loss_name') and self.out_net.loss_name is not None:
            stats[self.out_net.loss_name] = out_loss.detach().cpu().mean(0).item()
        else:
            # 使用默认名称
            stats['mse'] = out_loss.detach().cpu().mean(0).item()
        
        # 添加符号统计到输出统计
        stats['symbols'] = total_symbols
        stats['bits'] = total_bits
        if total_symbols > 0:
            stats['bits_per_symbol'] = total_bits / total_symbols
            stats['avg_entropy'] = avg_entropy
        
        # Maxwell-Boltzmann KL散度已经在各个block中处理，这里不需要额外处理
        
        stats['bppix'] = nats_per_dim * self.log2_e * imC
        stats['psnr']  = psnr
        
        # Channel相关统计信息现在由各个QLatentBlockX内部处理

        # 添加层熵值信息到stats - 按z维度分组
        if layer_zdims and layer_entropy:
            for zdim, entropy in zip(layer_zdims, layer_entropy):
                stats[f'zdim{zdim}_entropy'] = entropy

        # 添加lambda相关的统计信息到stats
        if hasattr(self, '_stats_log') and self._stats_log:
            # 添加层次化lambda值
            if 'hierarchical_lambdas' in self._stats_log:
                for res, lambda_val in self._stats_log['hierarchical_lambdas'].items():
                    stats[f'lambda_{res}'] = lambda_val

            # 添加其他lambda相关信息
            for key in ['lambda_value', 'target_entropy', 'snr_db']:
                if key in self._stats_log:
                    stats[key] = self._stats_log[key]

            # 添加层级KL散度信息
            if 'layer_kl_divergences' in self._stats_log:
                for res, kl_val in self._stats_log['layer_kl_divergences'].items():
                    stats[f'kl_{res}'] = kl_val

        if return_rec:
            stats['im_hat'] = im_hat
        return stats
        


    @torch.no_grad()
    def forward_eval(self, *args, **kwargs):
        """ a dummy function for evaluation
        """
        return self.forward(*args, **kwargs)

    @torch.no_grad()
    def uncond_sample(self, nhw_repeat, temprature=1.0):
        """ unconditionally sample, ie, generate new images

        Args:
            nhw_repeat (tuple): repeat the initial constant feature n,h,w times
            temprature (float): temprature
        """
        feature = self.decoder.forward_uncond(nhw_repeat, t=temprature)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    @torch.no_grad()
    def cond_sample(self, latents, nhw_repeat=None, temprature=1.0, paint_box=None):
        """ conditional sampling with latents

        Args:
            latents (torch.Tensor): latent variables
            nhw_repeat (tuple): repeat the constant n,h,w times
            temprature (float): temprature
            paint_box (tuple of floats): (x1,y1,x2,y2), in 0-1 range
        """
        feature = self.decoder.forward_with_latents(latents, nhw_repeat, t=temprature, paint_box=paint_box)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    def forward_get_latents(self, im):
        """ forward pass and return all the latent variables
        """
        x = self.preprocess_input(im)
        activations = self.encoder.forward(x)
        _, stats = self.decoder.forward(activations, get_latents=True)
        return stats

    @torch.no_grad()
    def inpaint(self, im, paint_box, steps=1, temprature=1.0):
        """ Inpainting

        Args:
            im (tensor): image (with paint_box mased out)
            paint_box (tuple): (x1, y1, x2, y2)
            steps (int, optional): A larger `step` gives a slightly better result.
            temprature (float, optional): tempreture. Defaults to 1.0.

        Returns:
            tensor: inpainted image
        """
        _, _, imH, imW = im.shape
        x1, y1, x2, y2 = paint_box
        h_slice = slice(round(y1*imH), round(y2*imH))
        w_slice = slice(round(x1*imW), round(x2*imW))
        im_input = im.clone()
        for i in range(steps):
            stats_all = self.forward_get_latents(im_input)
            latents = [st['z'] for st in stats_all]
            im_sample = self.cond_sample(latents, temprature=temprature, paint_box=paint_box)
            torch.clamp_(im_sample, min=0, max=1)
            im_input = im.clone()
            im_input[:, :, h_slice, w_slice] = im_sample[:, :, h_slice, w_slice]
        return im_sample

    def compress_mode(self, mode=True):
        """设置压缩模式

        Args:
            mode (bool): 是否启用压缩模式
        """
        if mode:
            # 1. 首先调用解码器的update方法初始化熵编码
            print("正在初始化熵编码模块...")
            try:
                self.decoder.update()
                print("✅ 解码器熵编码初始化成功")
            except Exception as e:
                print(f"⚠️  解码器熵编码初始化失败: {e}")

            # 2. 如果有输出网络，也需要初始化
            if hasattr(self.out_net, 'update'):
                try:
                    self.out_net.update()
                    print("✅ 输出网络熵编码初始化成功")
                except Exception as e:
                    print(f"⚠️  输出网络熵编码初始化失败: {e}")

            # 3. 更新所有Channel的工作模式为压缩模式
            Channel = get_channel_class()
            for module in self.modules():
                if isinstance(module, Channel):
                    module.set_mode(training=False)

            # 4. 更新解码器中所有QLatentBlockX的信道模式
            for block in self.decoder.dec_blocks:
                if isinstance(block, QLatentBlockX) and hasattr(block, 'channel') and block.channel is not None:
                    block.channel.set_mode(training=False)

            print("✅ 所有信道已切换到压缩模式(自适应QAM阶数)")
        else:
            # 更新所有Channel的工作模式为训练模式
            Channel = get_channel_class()
            for module in self.modules():
                if isinstance(module, Channel):
                    module.set_mode(training=True)

            # 更新解码器中所有QLatentBlockX的信道模式
            for block in self.decoder.dec_blocks:
                if isinstance(block, QLatentBlockX) and hasattr(block, 'channel') and block.channel is not None:
                    block.channel.set_mode(training=True)

            print("✅ 所有信道已切换到训练模式(固定256QAM)")

        # 更新压缩状态
        self.compressing = mode

    @torch.no_grad()
    def compress(self, im):
        """ compress a batch of images

        Args:
            im (torch.Tensor): a batch of images, (N, C, H, W), values between (0, 1)

        Returns:
            list: [string1, string2, string2, ..., string_N, feature_shape]
        """
        # 🔥 重置所有Channel传输统计并保存图像形状
        self.reset_all_channel_stats()
        self._current_image_shape = im.shape

        x = self.preprocess_input(im)
        enc_features = self.encoder(x)

        # 压缩
        compressed_obj, feature, channel_info_all = self.decoder.compress(enc_features)

        # 打印详细的压缩统计信息
        self._print_compression_stats(enc_features, channel_info_all)
        
        min_res = min(enc_features.keys())
        compressed_obj.append(tuple(enc_features[min_res].shape))
        
        # 添加通道信息
        compressed_obj.append(channel_info_all)
        
        if hasattr(self.out_net, 'compress'): # lossless compression
            x_tgt = self.preprocess_target(im)
            final_str = self.out_net.compress(feature, x_tgt)
            compressed_obj.append(final_str)
        return compressed_obj

    def _print_compression_stats(self, enc_features, channel_info_all):
        """使用所有Channel的准确传输统计 - 简化版本"""
        try:
            # 收集所有QLatentBlockX的channel统计
            all_channel_stats = self.collect_all_channel_stats()

            if all_channel_stats:
                # 获取图像形状用于计算BPP
                image_shape = None
                if hasattr(self, '_current_image_shape'):
                    image_shape = self._current_image_shape

                # 打印汇总的传输统计
                self.print_aggregated_channel_stats(all_channel_stats, image_shape)
            else:
                print("⚠️  没有找到Channel统计信息")

        except Exception as e:
            print(f"警告: 打印压缩统计时出错: {e}")
            import traceback
            traceback.print_exc()

    def collect_all_channel_stats(self):
        """收集所有QLatentBlockX的channel统计信息"""
        all_stats = []

        for i, block in enumerate(self.decoder.dec_blocks):
            if isinstance(block, QLatentBlockX) and hasattr(block, 'channel'):
                stats = block.channel.get_transmission_stats()
                stats['block_index'] = i
                stats['zdim'] = block.zdim
                all_stats.append(stats)

        return all_stats

    def print_aggregated_channel_stats(self, all_stats, image_shape=None):
        """打印汇总的channel统计信息"""
        if not all_stats:
            return

        # 汇总统计
        total_symbols = sum(stats['total_symbols_transmitted'] for stats in all_stats)
        total_bits = sum(stats['total_bits_transmitted'] for stats in all_stats)
        total_errors = sum(stats['symbol_errors'] for stats in all_stats)
        total_decisions = sum(stats['total_symbol_decisions'] for stats in all_stats)

        # 计算平均值
        avg_bits_per_symbol = total_bits / total_symbols if total_symbols > 0 else 0.0
        avg_error_rate = total_errors / total_decisions if total_decisions > 0 else 0.0

        # 获取SNR（假设所有block使用相同的SNR）
        snr_db = all_stats[0]['snr_db'] if all_stats else 0

        print("=" * 60)
        print("📡 汇总信道传输统计")
        print("=" * 60)
        print(f"SNR: {snr_db}dB")

        # 🔥 新增：显示掩码统计信息
        total_mask_elements = sum(stats.get('mask_stats', {}).get('total_elements', 0) for stats in all_stats)
        total_transmitted_elements = sum(stats.get('mask_stats', {}).get('transmitted_elements', 0) for stats in all_stats)
        total_masked_elements = sum(stats.get('mask_stats', {}).get('masked_elements', 0) for stats in all_stats)

        if total_mask_elements > 0:
            transmission_ratio = total_transmitted_elements / total_mask_elements
            print(f"🎭 掩码统计:")
            print(f"  总元素数: {total_mask_elements:,}")
            print(f"  传输元素数: {total_transmitted_elements:,}")
            print(f"  掩码元素数: {total_masked_elements:,}")
            print(f"  传输比例: {transmission_ratio:.3f} ({transmission_ratio*100:.1f}%)")

        print(f"传输符号数: {total_symbols:,}")
        print(f"传输比特数: {total_bits:,.0f}")
        print(f"平均比特/符号: {avg_bits_per_symbol:.2f}")
        print(f"符号错误率: {avg_error_rate:.4f}")

        if image_shape is not None:
            pixels = image_shape[-2] * image_shape[-1]  # height * width
            bpp = total_bits / pixels
            print(f"比特率: {bpp:.4f} bpp")

            # 计算总体压缩比
            if len(image_shape) >= 4:  # (N, C, H, W)
                channels = image_shape[1]
                height = image_shape[2]
                width = image_shape[3]
            else:  # (C, H, W)
                channels = image_shape[0]
                height = image_shape[1]
                width = image_shape[2]

            original_bits = channels * height * width * 8  # 每通道8比特
            compression_ratio = original_bits / total_bits if total_bits > 0 else 0
            compression_rate = (1 - total_bits / original_bits) * 100 if original_bits > 0 else 0

            print(f"压缩比: {compression_ratio:.2f}:1")
            print(f"压缩率: {compression_rate:.2f}%")

        # 按z维度分组统计
        zdim_stats = {}
        for stats in all_stats:
            zdim = stats['zdim']
            if zdim not in zdim_stats:
                zdim_stats[zdim] = {
                    'symbols': 0, 'bits': 0, 'blocks': 0,
                    'qam_usage': {}
                }
            zdim_stats[zdim]['symbols'] += stats['total_symbols_transmitted']
            zdim_stats[zdim]['bits'] += stats['total_bits_transmitted']
            zdim_stats[zdim]['blocks'] += 1

            # 合并QAM使用统计
            for qam_order, count in stats['qam_order_usage'].items():
                if qam_order not in zdim_stats[zdim]['qam_usage']:
                    zdim_stats[zdim]['qam_usage'][qam_order] = 0
                zdim_stats[zdim]['qam_usage'][qam_order] += count

        # 打印按z维度的统计
        if zdim_stats:
            print(f"\n按z维度统计:")
            print("z维度  符号数    比特数    块数  QAM使用")
            print("-" * 60)
            for zdim in sorted(zdim_stats.keys(), reverse=True):
                stats = zdim_stats[zdim]
                qam_usage = dict(stats['qam_usage'])
                print(f"{zdim:^4}  {stats['symbols']:^8,}  {stats['bits']:^8,.0f}  {stats['blocks']:^4}  {qam_usage}")

        print("=" * 60)

    def reset_all_channel_stats(self):
        """重置所有QLatentBlockX的channel统计"""
        for block in self.decoder.dec_blocks:
            if isinstance(block, QLatentBlockX) and hasattr(block, 'channel'):
                block.channel.reset_transmission_stats()

    @torch.no_grad()
    def decompress(self, compressed_object):
        """ decompress a batch of images

        Args:
            compressed_object (list): [string1, string2, string2, ..., string_N, feature_shape]

        Returns:
            tensor: reconstructed images, (N, C, H, W), values between (0, 1)
        """
        if hasattr(self.out_net, 'compress'): # lossless compression
            feature = self.decoder.decompress(compressed_object[:-1])
            x_hat = self.out_net.decompress(feature, compressed_object[-1])
        else: # lossy compression
            feature = self.decoder.decompress(compressed_object)
            x_hat = self.out_net.mean(feature)
        im_hat = self.process_output(x_hat)
        return im_hat

    @torch.no_grad()
    def compress_file(self, img_path, output_path):
        """ Compress an image file specified by `img_path` and save to `output_path`
        
        Args:
            img_path    (str): input image path
            output_path (str): output bits path
        """
        # read image
        img = Image.open(img_path)
        img_padded = pad_divisible_by(img, div=self.max_stride)
        device = next(self.parameters()).device
        im = tvf.to_tensor(img_padded).unsqueeze_(0).to(device=device)
        # compress by model
        compressed_obj = self.compress(im)
        compressed_obj.append((img.height, img.width))
        # save bits to file
        with open(output_path, 'wb') as f:
            pickle.dump(compressed_obj, file=f)

    @torch.no_grad()
    def decompress_file(self, bits_path):
        """ Decompress a bits file specified by `bits_path`
        
        Args:
            bits_path (str): input bits path
            
        Returns:
            torch.Tensor: reconstructed image
        """
        # read from file
        with open(bits_path, 'rb') as f:
            compressed_obj = pickle.load(file=f)
        img_h, img_w = compressed_obj.pop()
        # decompress by model
        im_hat = self.decompress(compressed_obj)
        return im_hat[:, :, :img_h, :img_w]
        
    def _build_layer_resolution_map(self, resolutions):
        """构建QLatentBlockX层索引到分辨率的正确映射"""
        layer_to_res_map = {}
        layer_idx = 0

        if hasattr(self, 'z_dims') and hasattr(self, 'dec_nums'):
            z_dims = self.z_dims
            dec_nums = self.dec_nums

            # 建立分辨率与z_dims的映射关系
            res_to_zdim = {}
            for i, zdim in enumerate(z_dims):
                if i < len(resolutions):
                    res_to_zdim[resolutions[-(i+1)]] = zdim

            # 根据dec_nums分配层到分辨率
            for zdim, num_layers in zip(z_dims, dec_nums):
                target_res = None
                for res, mapped_zdim in res_to_zdim.items():
                    if mapped_zdim == zdim:
                        target_res = res
                        break

                for _ in range(num_layers):
                    layer_to_res_map[layer_idx] = target_res or -1
                    layer_idx += 1
        else:
            # 回退方案
            for i in range(20):
                layer_to_res_map[i] = resolutions[i % len(resolutions)]

        return layer_to_res_map


def pad_divisible_by(img, div=64):
    """ Pad an PIL.Image at right and bottom border \
         such that both sides are divisible by `div`.

    Args:
        img (PIL.Image): image
        div (int, optional): `div`. Defaults to 64.

    Returns:
        PIL.Image: padded image
    """
    h_old, w_old = img.height, img.width
    if (h_old % div == 0) and (w_old % div == 0):
        return img
    h_tgt = round(div * math.ceil(h_old / div))
    w_tgt = round(div * math.ceil(w_old / div))
    # left, top, right, bottom
    padding = (0, 0, (w_tgt - w_old), (h_tgt - h_old))
    padded = tvf.pad(img, padding=padding, padding_mode='edge')
    return padded