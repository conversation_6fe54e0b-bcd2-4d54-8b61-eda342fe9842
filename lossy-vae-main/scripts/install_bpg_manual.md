# BPG安装手册

## 🚨 问题分析

您遇到的错误是BPG编译时x265依赖的问题。这是常见的BPG安装难题。

## 💡 解决方案

### 方案1: 简化安装（推荐）

```bash
# 1. 清理之前的安装
cd /tmp
rm -rf libbpg-0.9.8*

# 2. 重新下载
wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz
tar -xzf libbpg-0.9.8.tar.gz
cd libbpg-0.9.8

# 3. 简化编译（跳过x265/x264）
make CONFIG_X265=n CONFIG_X264=n

# 4. 安装
sudo make install CONFIG_X265=n CONFIG_X264=n
sudo ldconfig

# 5. 验证
bpgenc -h
```

### 方案2: 使用预编译二进制文件

```bash
# 下载预编译版本
cd /tmp
wget https://bellard.org/bpg/libbpg-0.9.8-linux-x86_64.tar.gz
tar -xzf libbpg-0.9.8-linux-x86_64.tar.gz

# 复制到系统路径
sudo cp libbpg-0.9.8-linux-x86_64/bpgenc /usr/local/bin/
sudo cp libbpg-0.9.8-linux-x86_64/bpgdec /usr/local/bin/
sudo chmod +x /usr/local/bin/bpg*

# 验证
bpgenc -h
```

### 方案3: 使用Docker（最简单）

```bash
# 创建BPG Docker容器
docker run -it --rm -v $(pwd):/workspace ubuntu:20.04 bash

# 在容器内安装BPG
apt-get update
apt-get install -y wget build-essential
cd /tmp
wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz
tar -xzf libbpg-0.9.8.tar.gz
cd libbpg-0.9.8
make CONFIG_X265=n CONFIG_X264=n
make install CONFIG_X265=n CONFIG_X264=n

# 复制编译好的文件到主机
cp bpgenc bpgdec /workspace/
```

### 方案4: 跳过BPG，使用现有方案

如果BPG安装太复杂，您可以：

1. **继续使用当前的4种方法**：WebP, JPEG, AVIF, VAE
2. **系统会自动跳过BPG测试**，不影响其他功能
3. **AVIF已经是很好的现代压缩格式**，性能接近BPG

## 🔍 验证安装

安装成功后，运行：

```bash
# 检查命令是否可用
which bpgenc
which bpgdec

# 检查版本
bpgenc -h

# 测试压缩比较系统
cd /home/<USER>/LOSSY_VAE/lossy-vae-main
python scripts/qresvae_sc/compression_comparison.py
```

## 📊 预期结果

BPG安装成功后，您将看到：

```
测试方法3: BPG + LDPC + 16-QAM...
BPG-20+LDPC+16QAM: PSNR=XX.XX dB, BPP=X.XX
BPG-30+LDPC+16QAM: PSNR=XX.XX dB, BPP=X.XX  
BPG-40+LDPC+16QAM: PSNR=XX.XX dB, BPP=X.XX
```

## 🎯 建议

1. **先尝试方案1**（简化编译）
2. **如果失败，使用方案2**（预编译二进制）
3. **如果都不行，继续使用现有的4种方法**

您的VAE方法已经表现很优秀（22.76dB PSNR），BPG只是额外的对比基准。
