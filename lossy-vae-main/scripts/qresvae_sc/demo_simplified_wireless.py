#!/usr/bin/env python3
"""
简化的无线VAE传输系统演示
展示去掉熵编码后的直接QAM传输效果
"""

import sys
from pathlib import Path
import os
import numpy as np
import matplotlib.pyplot as plt
import pickle
from PIL import Image
import torch
import torchvision.transforms.functional as tvf

# 添加项目路径
notebook_dir = Path(os.getcwd()).resolve()
project_root = notebook_dir.parent.parent
sys.path.insert(0, str(project_root))

torch.set_grad_enabled(False)

from lvae.models.qrvesvae_sc.zoo import qres34m_sc

def main():
    """主演示函数"""
    print("🚀 简化无线VAE传输系统演示")
    print("=" * 80)
    
    # 模型路径
    model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc_17/best_ema_snr10_eta0.5.pt"
    
    # 测试图像路径
    img_path = '../../images/collie.png'
    
    # 测试多个SNR值
    snr_values = [20, 10, 0, -10, -20]
    results = []
    
    # 加载和预处理图像
    print(f"📷 加载测试图像: {img_path}")
    try:
        original_img = Image.open(img_path).convert('RGB')
        print(f"✅ 图像加载成功，尺寸: {original_img.size}")
    except FileNotFoundError:
        print("⚠️  测试图像未找到，使用合成图像")
        # 创建合成测试图像
        test_img_array = np.zeros((256, 256, 3), dtype=np.uint8)
        for i in range(256):
            test_img_array[:, i, 0] = i  # 红色渐变
            test_img_array[i, :, 1] = i  # 绿色渐变
        test_img_array[64:192, 64:192, 2] = 255  # 蓝色方块
        test_img_array[100:156, 100:156, :] = [255, 255, 0]  # 黄色方块
        original_img = Image.fromarray(test_img_array)
    
    # 显示原始图像
    plt.figure(figsize=(6, 6))
    plt.title('原始图像')
    plt.imshow(original_img)
    plt.axis('off')
    plt.show()
    
    # 转换为tensor
    im = tvf.to_tensor(original_img).unsqueeze_(0)
    print(f"📊 输入张量形状: {im.shape}")
    
    # 测试不同SNR值
    for snr_db in snr_values:
        print(f"\n{'='*60}")
        print(f"🎯 测试 SNR = {snr_db} dB")
        print(f"{'='*60}")
        
        try:
            # 初始化模型
            print("🔧 初始化模型...")
            model = qres34m_sc(pretrained=model_path, snr_db=snr_db, eta=0.5, verbose=False)
            model.eval()
            model.compress_mode(True)
            print("✅ 模型初始化完成")
            
            # 压缩阶段
            print("🔄 开始压缩...")
            transmission_obj = model.compress(im)
            print(f"✅ 压缩完成，传输对象类型: {type(transmission_obj)}")
            
            # 分析传输对象
            if isinstance(transmission_obj, list):
                print(f"📦 传输对象包含 {len(transmission_obj)} 个元素")
                for i, item in enumerate(transmission_obj[:3]):  # 只显示前3个
                    if isinstance(item, torch.Tensor):
                        print(f"  元素{i}: Tensor形状={item.shape}")
                    else:
                        print(f"  元素{i}: {type(item)}")
            
            # 保存传输对象
            save_path = f'../../runs/wireless_snr{snr_db}.transmission'
            with open(save_path, 'wb') as f:
                pickle.dump(transmission_obj, file=f)
            
            # 计算传输对象大小
            file_size = Path(save_path).stat().st_size
            file_bits = file_size * 8
            file_bpp = file_bits / (im.shape[2] * im.shape[3])
            
            print(f"💾 传输对象保存: {save_path}")
            print(f"📊 文件大小: {file_size} bytes = {file_bits} bits = {file_bpp:.6f} bpp")
            
            # 获取QAM传输统计
            try:
                symbol_stats = model.collect_symbol_stats()
                qam_bits = symbol_stats['total_bits']
                qam_symbols = symbol_stats['total_symbols']
                qam_bpp = qam_bits / (im.shape[2] * im.shape[3])
                
                print(f"📡 QAM传输统计:")
                print(f"  符号数量: {qam_symbols}")
                print(f"  传输比特数: {qam_bits:.1f} bits")
                print(f"  比特率: {qam_bpp:.6f} bpp")
                print(f"  平均比特/符号: {symbol_stats['bits_per_symbol']:.2f}")
                
            except Exception as e:
                print(f"⚠️  QAM统计获取失败: {e}")
                qam_bits = file_bits  # 使用文件大小作为备选
                qam_bpp = file_bpp
            
            # 解压缩阶段
            print("🔄 开始解压缩...")
            with open(save_path, 'rb') as f:
                loaded_transmission_obj = pickle.load(file=f)
            
            im_hat = model.decompress(loaded_transmission_obj).squeeze(0).cpu()
            print(f"✅ 解压缩完成，重建图像形状: {im_hat.shape}")
            
            # 质量评估
            reconstructed_img = tvf.to_pil_image(im_hat)
            original_array = np.array(original_img)
            reconstructed_array = np.array(reconstructed_img)
            
            # 计算PSNR
            mse = np.mean((original_array.astype(float) - reconstructed_array.astype(float)) ** 2)
            psnr = 10 * np.log10(255**2 / mse) if mse > 0 else float('inf')
            
            # 计算压缩比
            original_bits = im.shape[2] * im.shape[3] * 24  # RGB 24 bits/pixel
            compression_ratio = original_bits / qam_bits if qam_bits > 0 else 0
            
            print(f"📈 质量评估:")
            print(f"  MSE: {mse:.4f}")
            print(f"  PSNR: {psnr:.2f} dB")
            print(f"  压缩比: {compression_ratio:.2f}:1")
            
            # 显示重建图像（仅显示关键SNR值）
            if snr_db in [20, 0, -20]:
                plt.figure(figsize=(6, 6))
                plt.title(f'重建图像 (SNR={snr_db}dB, PSNR={psnr:.2f}dB)')
                plt.imshow(reconstructed_img)
                plt.axis('off')
                plt.show()
            
            # 记录结果
            result = {
                'snr_db': snr_db,
                'mse': mse,
                'psnr': psnr,
                'bpp': qam_bpp,
                'compression_ratio': compression_ratio,
                'file_size': file_size
            }
            results.append(result)
            
        except Exception as e:
            print(f"❌ SNR={snr_db}dB 测试失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 记录失败结果
            result = {
                'snr_db': snr_db,
                'mse': float('inf'),
                'psnr': 0.0,
                'bpp': 0.0,
                'compression_ratio': 0.0,
                'file_size': 0,
                'error': str(e)
            }
            results.append(result)
    
    # 显示汇总结果
    print(f"\n{'='*80}")
    print("🎯 简化无线VAE系统测试结果汇总")
    print(f"{'='*80}")
    print(f"{'SNR(dB)':<8} {'PSNR(dB)':<10} {'MSE':<12} {'BPP':<10} {'压缩比':<8} {'状态':<8}")
    print(f"{'-'*70}")
    
    successful_results = []
    for result in results:
        status = "✅成功" if 'error' not in result else "❌失败"
        print(f"{result['snr_db']:<8} {result['psnr']:<10.2f} {result['mse']:<12.4f} "
              f"{result['bpp']:<10.6f} {result['compression_ratio']:<8.2f} {status:<8}")
        
        if 'error' not in result:
            successful_results.append(result)
    
    # 分析结果
    if len(successful_results) > 1:
        psnr_values = [r['psnr'] for r in successful_results]
        psnr_range = max(psnr_values) - min(psnr_values)
        
        print(f"\n📊 结果分析:")
        print(f"成功测试数量: {len(successful_results)}/{len(results)}")
        print(f"PSNR变化范围: {min(psnr_values):.2f} - {max(psnr_values):.2f} dB (变化 {psnr_range:.4f} dB)")
        
        if psnr_range > 1.0:
            print("✅ 系统对SNR变化有明显响应")
        elif psnr_range > 0.1:
            print("⚠️  系统对SNR变化有轻微响应")
        else:
            print("ℹ️  系统对SNR变化响应很小（体现了VAE+QAM系统的鲁棒性）")
        
        # 显示系统特点
        print(f"\n🎯 系统特点:")
        print(f"• 无熵编码设计：直接传输QAM调制的特征")
        print(f"• 端到端优化：VAE编码 + QAM调制 + AWGN信道 + 硬判决解调")
        print(f"• 分层鲁棒性：VAE解码器对特征扰动具有天然鲁棒性")
        print(f"• 实时传输友好：无需复杂的熵编码/解码过程")
    
    print(f"\n🎉 演示完成！")
    return results

if __name__ == "__main__":
    results = main()
