import torch
from torch.hub import load_state_dict_from_url

from lvae.models.registry import register_model
import lvae.models.common as common
import lvae.models.qrvesvae_sc.model as qres


@register_model
def qres34m_sc(lmb=32, pretrained=False, snr_db=20, compression_ratio=0.5, enable_compression=True,
               verbose=False, compression_method='smart_selection', adaptive_compression=False, **kwargs):
    cfg = dict()

    enc_nums = [6, 6, 6, 4, 2]
    dec_nums = [1, 2, 3, 3, 3]

    # 添加z_dims定义
    z_dims = [16, 14, 12, 10, 8]  # 从高层到低层的潜在变量维度

    im_channels = 3
    ch = 96 # 128
    cfg['enc_blocks'] = [
        common.patch_downsample(im_channels, ch*2, rate=4),
        *[qres.MyConvNeXtBlock(ch*2, kernel_size=7) for _ in range(enc_nums[0])], # 16x16
        qres.MyConvNeXtPatchDown(ch*2, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=7) for _ in range(enc_nums[1])], # 8x8
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=5) for _ in range(enc_nums[2])], # 4x4
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=3) for _ in range(enc_nums[3])], # 2x2
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=1) for _ in range(enc_nums[4])], # 1x1
    ]

    # 创建信道层，支持通过model_args传递参数
    channel = qres.Channel(
        noise_type='awgn',
        snr=snr_db,
        compression_ratio=compression_ratio,
        enable_compression=enable_compression,
        verbose=verbose
    )

    # 设置压缩方法和自适应压缩（如果Channel类支持这些方法）
    if hasattr(channel, 'set_compression_method'):
        channel.set_compression_method(compression_method)
    if hasattr(channel, 'set_adaptive_compression'):
        channel.set_adaptive_compression(adaptive_compression)

    cfg['channel'] = channel
    
    cfg['dec_blocks'] = [
        *[qres.QLatentBlockX(ch*4, z_dims[0], kernel_size=1, channel=channel) for _ in range(dec_nums[0])], # 1x1
        common.patch_upsample(ch*4, ch*4, rate=2),
        *[qres.QLatentBlockX(ch*4, z_dims[1], kernel_size=3, channel=channel) for _ in range(dec_nums[1])], # 2x2
        common.patch_upsample(ch*4, ch*4, rate=2),
        *[qres.QLatentBlockX(ch*4, z_dims[2], kernel_size=5, channel=channel) for _ in range(dec_nums[2])], # 4x4
        common.patch_upsample(ch*4, ch*4, rate=2),
        *[qres.QLatentBlockX(ch*4, z_dims[3], kernel_size=7, channel=channel) for _ in range(dec_nums[3])], # 8x8
        common.patch_upsample(ch*4, ch*2, rate=2),
        *[qres.QLatentBlockX(ch*2, z_dims[4], kernel_size=7, channel=channel) for _ in range(dec_nums[4])], # 16x16
        common.patch_upsample(ch*2, im_channels, rate=4)
    ]
    cfg['out_net'] = qres.MSEOutputNet(mse_lmb=lmb)

    # mean and std computed on imagenet
    cfg['im_shift'] = -0.4546259594901961
    cfg['im_scale'] = 3.67572653978347
    cfg['max_stride'] = 64

    model = qres.HierarchicalVAE(cfg)
    if (pretrained is True) and (lmb in {16, 32, 64, 128, 256, 512, 1024, 2048}):
        url = f'https://huggingface.co/duanzh0/my-model-weights/resolve/main/qres34m/qres34m-lmb{lmb}.pt'
        msd = load_state_dict_from_url(url)['model']
        model.load_state_dict(msd)
    elif isinstance(pretrained, str):
        msd = torch.load(pretrained)['model']
        model.load_state_dict(msd)
    else:
        assert pretrained is False, f'Invalid {pretrained=} and {lmb=}'
    return model


@register_model
def qres34m_lossless_sc(pretrained=False, snr_db=20, compression_ratio=0.5, enable_compression=True,
                        verbose=False, compression_method='smart_selection', adaptive_compression=False, **kwargs):
    cfg = dict()

    enc_nums = [6, 6, 6, 4, 2]
    dec_nums = [1, 2, 3, 3, 3]
    z_dims = [16, 14, 12, 10, 8]

    im_channels = 3
    ch = 96
    cfg['enc_blocks'] = [
        common.patch_downsample(im_channels, ch*2, rate=4),
        *[qres.MyConvNeXtBlock(ch*2, kernel_size=7) for _ in range(enc_nums[0])],
        qres.MyConvNeXtPatchDown(ch*2, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=7) for _ in range(enc_nums[1])],
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=5) for _ in range(enc_nums[2])],
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=3) for _ in range(enc_nums[3])],
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=1) for _ in range(enc_nums[4])],
    ]

    # 创建信道层，支持通过model_args传递参数
    channel = qres.Channel(
        noise_type='awgn',
        snr=snr_db,
        compression_ratio=compression_ratio,
        enable_compression=enable_compression,
        verbose=verbose
    )

    # 设置压缩方法和自适应压缩
    if hasattr(channel, 'set_compression_method'):
        channel.set_compression_method(compression_method)
    if hasattr(channel, 'set_adaptive_compression'):
        channel.set_adaptive_compression(adaptive_compression)

    cfg['channel'] = channel

    cfg['dec_blocks'] = [
        *[qres.QLatentBlockX(ch*4, z_dims[0], kernel_size=1, channel=channel) for _ in range(dec_nums[0])],
        common.patch_upsample(ch*4, ch*4, rate=2),
        *[qres.QLatentBlockX(ch*4, z_dims[1], kernel_size=3, channel=channel) for _ in range(dec_nums[1])],
        common.patch_upsample(ch*4, ch*4, rate=2),
        *[qres.QLatentBlockX(ch*4, z_dims[2], kernel_size=5, channel=channel) for _ in range(dec_nums[2])],
        common.patch_upsample(ch*4, ch*4, rate=2),
        *[qres.QLatentBlockX(ch*4, z_dims[3], kernel_size=7, channel=channel) for _ in range(dec_nums[3])],
        common.patch_upsample(ch*4, ch*2, rate=2),
        *[qres.QLatentBlockX(ch*2, z_dims[4], kernel_size=7, channel=channel) for _ in range(dec_nums[4])],
    ]
    cfg['out_net'] = qres.GaussianNLLOutputNet(
        conv_mean=common.patch_upsample(ch*2, im_channels, rate=4),
        conv_scale=common.patch_upsample(ch*2, im_channels, rate=4)
    )

    cfg['im_shift'] = -0.4546259594901961
    cfg['im_scale'] = 3.67572653978347
    cfg['max_stride'] = 64

    model = qres.HierarchicalVAE(cfg)
    if pretrained is True:
        # 这里可以添加预训练权重的URL
        pass
    elif isinstance(pretrained, str):
        msd = torch.load(pretrained)['model']
        model.load_state_dict(msd)
    else:
        assert pretrained is False, f'Invalid {pretrained=}'
    return model


@register_model
def qres17m_sc(lmb=8, pretrained=False, snr_db=20, compression_ratio=0.5, enable_compression=True,
               verbose=False, compression_method='smart_selection', adaptive_compression=False, **kwargs):
    cfg = dict()

    enc_nums = [6,6,4,2]
    dec_nums = [1,2,4,5]
    z_dims = [16, 8, 6, 4]

    im_channels = 3
    ch = 72
    cfg['enc_blocks'] = [
        common.patch_downsample(im_channels, ch*2, rate=4),
        *[qres.MyConvNeXtBlock(ch*2, kernel_size=7) for _ in range(enc_nums[0])],
        qres.MyConvNeXtPatchDown(ch*2, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=5) for _ in range(enc_nums[1])],
        qres.MyConvNeXtPatchDown(ch*4, ch*4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=3) for _ in range(enc_nums[2])],
        qres.MyConvNeXtPatchDown(ch*4, ch*4, down_rate=4),
        *[qres.MyConvNeXtBlock(ch*4, kernel_size=1) for _ in range(enc_nums[3])],
    ]

    # 创建信道层，支持通过model_args传递参数
    channel = qres.Channel(
        noise_type='awgn',
        snr=snr_db,
        compression_ratio=compression_ratio,
        enable_compression=enable_compression,
        verbose=verbose
    )

    # 设置压缩方法和自适应压缩
    if hasattr(channel, 'set_compression_method'):
        channel.set_compression_method(compression_method)
    if hasattr(channel, 'set_adaptive_compression'):
        channel.set_adaptive_compression(adaptive_compression)

    cfg['channel'] = channel

    from torch.nn import Upsample
    cfg['dec_blocks'] = [
        *[qres.QLatentBlockX(ch*4, z_dims[0], kernel_size=1, channel=channel) for _ in range(dec_nums[0])],
        Upsample(scale_factor=4),
        *[qres.QLatentBlockX(ch*4, z_dims[1], kernel_size=3, channel=channel) for _ in range(dec_nums[1])],
        common.deconv(ch*4, ch*4, kernel_size=3),
        *[qres.QLatentBlockX(ch*4, z_dims[2], kernel_size=5, channel=channel) for _ in range(dec_nums[2])],
        common.deconv(ch*4, ch*2),
        *[qres.QLatentBlockX(ch*2, z_dims[3], kernel_size=7, channel=channel) for _ in range(dec_nums[3])],
        common.patch_upsample(ch*2, im_channels, rate=4)
    ]
    cfg['out_net'] = qres.MSEOutputNet(mse_lmb=lmb)

    cfg['im_shift'] = -0.4356
    cfg['im_scale'] = 3.397893306150187
    cfg['max_stride'] = 64

    model = qres.HierarchicalVAE(cfg)
    if pretrained is True:
        # 这里可以添加预训练权重的URL
        pass
    elif isinstance(pretrained, str):
        msd = torch.load(pretrained)['model']
        model.load_state_dict(msd)
    else:
        assert pretrained is False, f'Invalid {pretrained=} and {lmb=}'
    return model
