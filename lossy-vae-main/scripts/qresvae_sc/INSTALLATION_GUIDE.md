# 四种压缩方法比较系统 - 安装指南

## 概述

本系统实现了四种压缩方法的比较：
1. **PNG + LDPC编码 + 16-QAM + SNR=20噪声**
2. **JPEG + LDPC编码 + 16-QAM + SNR=20噪声**  
3. **BPG + LDPC编码 + 16-QAM + SNR=20噪声**
4. **VAE模型（已包含QAM信道编码和解调）**

## 依赖安装

### 1. BPG编码器安装

#### Ubuntu/Debian:
```bash
# 安装依赖
sudo apt-get update
sudo apt-get install build-essential cmake libpng-dev libjpeg-dev

# 下载并编译BPG
wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz
tar -xzf libbpg-0.9.8.tar.gz
cd libbpg-0.9.8
make
sudo make install

# 确保bpgenc和bpgdec在PATH中
sudo ln -s /usr/local/bin/bpgenc /usr/bin/bpgenc
sudo ln -s /usr/local/bin/bpgdec /usr/bin/bpgdec
```

#### macOS:
```bash
# 使用Homebrew安装
brew install libbpg
```

### 2. Sionna库安装（可选，用于更精确的LDPC实现）

```bash
# 安装TensorFlow（Sionna的依赖）
pip install tensorflow

# 安装Sionna
pip install sionna
```

注意：如果不安装Sionna，系统会使用简化的LDPC模拟实现。

### 3. 其他Python依赖

```bash
pip install numpy matplotlib pillow torch torchvision
```

## 使用方法

### 1. 基本使用

```python
from compression_comparison import CompressionComparator

# 创建比较器
comparator = CompressionComparator(
    model_path="path/to/vae/model.pt",  # VAE模型路径，可选
    snr_db=20.0  # 信噪比设置
)

# 执行比较
results = comparator.compare_all_methods("path/to/test/image.png")

# 显示结果
comparator.print_comparison_table(results)
comparator.plot_rd_curve(results, "comparison_results.png")
```

### 2. 运行完整比较

```bash
cd lossy-vae-main/scripts/qresvae_sc
python compression_comparison.py
```

### 3. 运行测试

```bash
python test_new_comparison.py
```

## 预期结果

系统会输出：
- **比较表格**：显示各方法的PSNR、压缩比、BPP等指标
- **率失真曲线**：可视化各方法的性能对比
- **详细结果JSON**：保存完整的测试数据

## 故障排除

### BPG相关问题

1. **"BPG编码器不可用"**
   - 确保bpgenc和bpgdec已正确安装并在PATH中
   - 运行 `bpgenc -h` 测试是否可用

2. **编译错误**
   - 确保安装了所有依赖：`build-essential`, `cmake`, `libpng-dev`, `libjpeg-dev`
   - 检查系统架构兼容性

### LDPC相关问题

1. **"Sionna库未安装"**
   - 这是警告，不是错误
   - 系统会自动使用简化的LDPC实现
   - 如需精确实现，请安装Sionna

### 图像处理问题

1. **"PNG解压失败"**
   - 这是正常现象，因为数据经过了噪声信道
   - 系统会自动使用fallback处理

2. **PSNR较低**
   - 这是预期的，因为所有方法都经过了SNR=20的噪声信道
   - 可以调整SNR参数进行测试

## 配置选项

### SNR设置
```python
# 修改信噪比
comparator = CompressionComparator(snr_db=25.0)  # 更高的SNR
```

### 质量参数
- **JPEG质量**: 50, 75, 85 (可在代码中修改)
- **BPG质量**: 20, 30, 40 (可在代码中修改)
- **LDPC编码率**: 0.5 (可在代码中修改)

## 性能优化建议

1. **使用GPU加速**（如果有VAE模型）
2. **并行处理多个质量参数**
3. **缓存中间结果**
4. **使用更大的测试图像集**

## 扩展功能

系统设计为模块化，可以轻松添加：
- 新的压缩方法
- 不同的信道模型
- 其他评估指标
- 批量测试功能
