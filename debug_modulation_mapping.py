#!/usr/bin/env python3
"""调试调制映射的精度问题"""

import sys
import os
import torch
import numpy as np

# 添加项目路径
project_root = os.path.join(os.path.dirname(__file__), 'lossy-vae-main')
sys.path.insert(0, project_root)

from lvae.models.qrvesvae_sc.channel import Channel

def debug_modulation_precision():
    """调试调制映射的精度"""
    print("🔍 调试调制映射精度")
    print("=" * 50)
    
    # 创建简单的测试数据
    test_values = torch.tensor([[-5.0, -2.5, 0.0, 2.5, 5.0]]).unsqueeze(0).unsqueeze(0)  # [1,1,1,5]
    print(f"输入测试值: {test_values.flatten().tolist()}")
    
    channel = Channel(snr=30.0, verbose=True)  # 高SNR减少噪声影响
    
    # 测试无噪声的调制解调
    print(f"\n🧪 无噪声调制解调测试:")
    
    # 手动调制
    qam_order = 256
    feature_min = test_values.min()
    feature_max = test_values.max()
    feature_range = feature_max - feature_min
    
    print(f"特征范围: [{feature_min:.3f}, {feature_max:.3f}], 范围={feature_range:.3f}")
    
    # 归一化到[0, qam_order-1]
    normalized = (test_values - feature_min) / feature_range * (qam_order - 1)
    print(f"归一化后: {normalized.flatten().tolist()}")
    
    # 量化到最近的整数索引
    quantized_indices = torch.round(normalized).long()
    print(f"量化索引: {quantized_indices.flatten().tolist()}")
    
    # 映射到星座点
    constellation_points = channel._get_constellation_points(qam_order, test_values.device)
    modulated = constellation_points[quantized_indices]
    print(f"调制后值: {modulated.flatten().tolist()}")
    
    # 反向映射检查
    # 从星座点索引恢复到归一化值
    recovered_normalized = quantized_indices.float()
    # 反归一化
    recovered_values = recovered_normalized / (qam_order - 1) * feature_range + feature_min
    print(f"理论恢复值: {recovered_values.flatten().tolist()}")
    
    # 实际解调测试（无噪声）
    print(f"\n🔄 实际解调测试（无噪声）:")
    demodulated = channel._qam_demodulate(modulated, qam_order=qam_order, hard_decision=True)
    print(f"解调后值: {demodulated.flatten().tolist()}")
    
    # 计算精度损失
    precision_loss = torch.abs(test_values - demodulated)
    print(f"精度损失: {precision_loss.flatten().tolist()}")
    print(f"最大精度损失: {precision_loss.max():.6f}")
    
    # 测试有噪声的情况
    print(f"\n🌊 有噪声测试:")
    noisy_modulated = channel._add_awgn_noise(modulated)
    print(f"噪声后值: {noisy_modulated.flatten().tolist()}")
    
    noisy_demodulated = channel._qam_demodulate(noisy_modulated, qam_order=qam_order, hard_decision=True)
    print(f"噪声解调后: {noisy_demodulated.flatten().tolist()}")
    
    noise_loss = torch.abs(test_values - noisy_demodulated)
    print(f"噪声损失: {noise_loss.flatten().tolist()}")
    print(f"最大噪声损失: {noise_loss.max():.6f}")

def test_different_qam_orders():
    """测试不同QAM阶数的精度"""
    print(f"\n📊 不同QAM阶数精度测试:")
    print("=" * 50)
    
    # 创建测试数据
    test_range = 10.0
    test_values = torch.linspace(-test_range, test_range, 21).unsqueeze(0).unsqueeze(0).unsqueeze(0)  # [1,1,1,21]
    
    qam_orders = [4, 16, 64, 256]
    
    for qam_order in qam_orders:
        print(f"\n🎯 {qam_order}-QAM测试:")
        
        channel = Channel(snr=30.0, verbose=False)
        
        # 手动精确调制解调
        feature_min = test_values.min()
        feature_max = test_values.max()
        feature_range = feature_max - feature_min
        
        # 归一化并量化
        normalized = (test_values - feature_min) / feature_range * (qam_order - 1)
        quantized_indices = torch.round(normalized).long()
        
        # 映射到星座点并解调
        constellation_points = channel._get_constellation_points(qam_order, test_values.device)
        modulated = constellation_points[quantized_indices]
        demodulated = channel._qam_demodulate(modulated, qam_order=qam_order, hard_decision=True)
        
        # 计算量化误差
        quantization_error = torch.abs(test_values - demodulated)
        max_error = quantization_error.max()
        mean_error = quantization_error.mean()
        
        # 计算理论量化步长
        theoretical_step = feature_range / (qam_order - 1)
        
        print(f"  理论量化步长: {theoretical_step:.6f}")
        print(f"  最大量化误差: {max_error:.6f}")
        print(f"  平均量化误差: {mean_error:.6f}")
        print(f"  误差/步长比: {max_error/theoretical_step:.3f}")

if __name__ == "__main__":
    debug_modulation_precision()
    test_different_qam_orders()
