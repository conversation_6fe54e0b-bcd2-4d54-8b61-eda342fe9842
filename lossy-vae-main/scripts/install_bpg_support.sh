#!/bin/bash

echo "=== BPG编码器安装指南 ==="
echo ""

# 检查操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "检测到Linux系统"
    
    # Ubuntu/Debian系统
    if command -v apt-get &> /dev/null; then
        echo ""
        echo "🔧 Ubuntu/Debian系统安装步骤："
        echo "1. 安装编译依赖："
        echo "   sudo apt-get update"
        echo "   sudo apt-get install -y build-essential cmake git"
        echo "   sudo apt-get install -y libjpeg-dev libpng-dev"
        echo "   sudo apt-get install -y yasm nasm"
        echo ""
        echo "2. 下载并编译libbpg："
        echo "   cd /tmp"
        echo "   wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz"
        echo "   tar -xzf libbpg-0.9.8.tar.gz"
        echo "   cd libbpg-0.9.8"
        echo "   make"
        echo "   sudo make install"
        echo ""
        echo "3. 更新库路径："
        echo "   sudo ldconfig"
        echo ""
        
    # CentOS/RHEL/Fedora系统
    elif command -v yum &> /dev/null || command -v dnf &> /dev/null; then
        echo ""
        echo "🔧 CentOS/RHEL/Fedora系统安装步骤："
        echo "1. 安装编译依赖："
        if command -v dnf &> /dev/null; then
            echo "   sudo dnf groupinstall -y 'Development Tools'"
            echo "   sudo dnf install -y cmake git libjpeg-devel libpng-devel yasm nasm"
        else
            echo "   sudo yum groupinstall -y 'Development Tools'"
            echo "   sudo yum install -y cmake git libjpeg-devel libpng-devel yasm nasm"
        fi
        echo ""
        echo "2. 下载并编译libbpg："
        echo "   cd /tmp"
        echo "   wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz"
        echo "   tar -xzf libbpg-0.9.8.tar.gz"
        echo "   cd libbpg-0.9.8"
        echo "   make"
        echo "   sudo make install"
        echo ""
        echo "3. 更新库路径："
        echo "   sudo ldconfig"
        echo ""
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "检测到macOS系统"
    echo ""
    echo "🔧 macOS系统安装步骤："
    echo "1. 使用Homebrew安装依赖："
    echo "   brew install cmake jpeg libpng yasm nasm"
    echo ""
    echo "2. 下载并编译libbpg："
    echo "   cd /tmp"
    echo "   wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz"
    echo "   tar -xzf libbpg-0.9.8.tar.gz"
    echo "   cd libbpg-0.9.8"
    echo "   make"
    echo "   sudo make install"
    echo ""
fi

echo "=== BPG编译选项 ==="
echo "选项1: 简化编译（推荐）"
echo "   make CONFIG_X265=n CONFIG_X264=n"
echo ""
echo "选项2: 完整编译（需要更多依赖）"
echo "   git submodule update --init"
echo "   make"
echo ""
echo "=== Python BPG库安装 ==="
echo "注意：目前没有可用的Python BPG绑定库"
echo "我们将使用命令行工具 bpgenc/bpgdec"
echo ""

echo "=== 验证安装 ==="
echo "安装完成后，运行以下命令验证BPG支持："
echo "bpgenc -h  # 检查命令行工具"
echo "python -c \"import subprocess; subprocess.run(['bpgenc', '-h'])\""
echo ""

echo "=== 替代方案 ==="
echo "如果BPG编译失败，系统会自动使用AVIF/WebP作为替代方案。"
echo ""

echo "=== 自动安装脚本 ==="
echo "如果您想自动安装，请运行："
echo "chmod +x scripts/install_bpg_support.sh"
echo "./scripts/install_bpg_support.sh auto"
echo ""

# 如果传入auto参数，自动执行安装
if [[ "$1" == "auto" ]]; then
    echo "开始自动安装BPG..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]] && command -v apt-get &> /dev/null; then
        echo "正在安装Ubuntu/Debian依赖..."
        sudo apt-get update
        sudo apt-get install -y build-essential cmake git libjpeg-dev libpng-dev yasm nasm
        
        echo "下载并编译libbpg..."
        cd /tmp
        rm -rf libbpg-0.9.8*  # 清理之前的下载
        wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz
        tar -xzf libbpg-0.9.8.tar.gz
        cd libbpg-0.9.8

        echo "尝试简化编译（不包含x265/x264）..."
        make CONFIG_X265=n CONFIG_X264=n
        if [ $? -eq 0 ]; then
            echo "简化编译成功！"
            sudo make install CONFIG_X265=n CONFIG_X264=n
            sudo ldconfig
        else
            echo "简化编译失败，尝试完整编译..."
            git submodule update --init
            make
            if [ $? -eq 0 ]; then
                sudo make install
                sudo ldconfig
            else
                echo "编译失败，请检查依赖是否完整安装"
                exit 1
            fi
        fi
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "正在安装macOS依赖..."
        brew install cmake jpeg libpng yasm nasm
        
        echo "下载并编译libbpg..."
        cd /tmp
        rm -rf libbpg-0.9.8*  # 清理之前的下载
        wget https://bellard.org/bpg/libbpg-0.9.8.tar.gz
        tar -xzf libbpg-0.9.8.tar.gz
        cd libbpg-0.9.8

        echo "尝试简化编译（不包含x265/x264）..."
        make CONFIG_X265=n CONFIG_X264=n
        if [ $? -eq 0 ]; then
            echo "简化编译成功！"
            sudo make install CONFIG_X265=n CONFIG_X264=n
        else
            echo "简化编译失败，尝试完整编译..."
            git submodule update --init
            make
            if [ $? -eq 0 ]; then
                sudo make install
            else
                echo "编译失败，请检查依赖是否完整安装"
                exit 1
            fi
        fi
    fi
    
    echo "验证安装结果..."
    bpgenc -h
fi

echo ""
echo "=== 注意事项 ==="
echo "1. BPG编译可能需要较长时间"
echo "2. 确保有足够的磁盘空间（至少500MB）"
echo "3. 如果编译失败，检查是否安装了所有依赖"
echo "4. 某些系统可能需要额外配置环境变量"
