# %%
import sys
from pathlib import Path # 确保 Path 也被导入了
import os
import numpy as np
# 下面的代码会把项目根目录 (lossy-vae-main) 添加到 Python 的搜索路径中
# os.getcwd() 在 notebook 中运行时，通常是 notebook 文件所在的目录
notebook_dir = Path(os.getcwd()).resolve()
project_root = notebook_dir.parent.parent # 从 scripts/qresvae 退两级到 lossy-vae-main
sys.path.insert(0, str(project_root))
import matplotlib.pyplot as plt
print(os.getcwd())
import pickle
from pathlib import Path
from PIL import Image
import torch
import torchvision.transforms.functional as tvf
import io
import time

torch.set_grad_enabled(False)

from lvae.models.qrvesvae_sc.zoo import qres34m_sc

# %% [markdown]
# Initialize model and load pre-trained weights

# %%
model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc_1/best.pt"
# model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres17m_sc_0/best.pt"
# model_path = "/home/<USER>/LOSSY_VAE/lossy-vae-main/runs/default/qres34m_sc2_21/best.pt"
# 读取并显示原始图像

model = qres34m_sc(pretrained=model_path,snr_db=10)
# model = qres17m_sc(pretrained=model_path,snr_db=0)

model.eval()
model.compress_mode(True)
# model.update()
# 先查看模型的max_stride
# Get model's max_stride parameter
max_stride = model.max_stride
print(f"Model's max_stride: {max_stride}")

# %% [markdown]
# Compress an RGB image

# %%
# img_path = '../../images/kodim09.png'
# img_path = '../../images/732bf474788c19c0c1fae6dd7689d4cda2f4e0632a1c7725e970b69d44d08f3e.png'
# img_path = '../../images/myself.jpg'
img_path = '../../images/000000000901.jpg'

original_img = Image.open(img_path)
# 读取并填充图像
width, height = original_img.size
# Check if dimensions are divisible by max_stride
if width % max_stride != 0 or height % max_stride != 0:
    print(f"Original image dimensions ({width}x{height}) are not divisible by max_stride={max_stride}")
    
    # Calculate new dimensions that are divisible by max_stride
    new_width = ((width + max_stride - 1) // max_stride) * max_stride
    new_height = ((height + max_stride - 1) // max_stride) * max_stride
    
    print(f"Resizing image to {new_width}x{new_height}")
    
    # Resize the image to meet the max_stride requirement
    # Use a method that maintains the image quality (e.g., BICUBIC)
    resized_img = original_img.resize((new_width, new_height), Image.BICUBIC)
    
    # For demonstration, save the padded image
    padded_img_path = '../../images/collie_padded.png'
    resized_img.save(padded_img_path)
    
    # Use the resized image for compression
    original_img = resized_img
plt.figure(figsize=(5, 5))
plt.title('Original Image')
plt.imshow(original_img)
plt.axis('off')
plt.show()
im = tvf.to_tensor(original_img).unsqueeze_(0)
compressed_obj = model.compress(im)

# %% [markdown]
# Save to file, compute bit rate

# %%
save_path = '../../runs/image.bits'
with open(save_path, 'wb') as f:
    pickle.dump(compressed_obj, file=f)

total_bits = Path(save_path).stat().st_size * 8
bpp = total_bits / (im.shape[2] * im.shape[3])
print(f'Compressed file size: {total_bits} bits = {bpp:.6f} bpp')

# %% [markdown]
# save_path = '../../runs/image.bits'
# with open(save_path, 'wb') as f:
#     pickle.dump(compressed_obj, file=f)
# 
# total_bits = Path(save_path).stat().st_size * 8
# bpp = total_bits / (im.shape[2] * im.shape[3])
# print(f'Compressed file size: {total_bits} bits = {bpp:.6f} bpp')
# model.channel.debug_mb_kl_calculation([1.0, 5.0, 10.0, 20.0])

# %%
with open(save_path,'rb') as f:
    compressed_obj = pickle.load(file=f)

im_hat = model.decompress(compressed_obj).squeeze(0).cpu()
# 显示重建图像
reconstructed_img = tvf.to_pil_image(im_hat)
plt.figure(figsize=(5, 5))
plt.title('Reconstructed Image')
plt.imshow(reconstructed_img)
plt.axis('off')
plt.show()

# %% [markdown]
# Check if the compression is lossless

# %%
# 导入额外的质量评估库
try:
    from skimage.metrics import structural_similarity as ssim
    from skimage.metrics import peak_signal_noise_ratio as psnr_skimage
    SSIM_AVAILABLE = True
except ImportError:
    print("警告: scikit-image未安装，无法计算SSIM")
    SSIM_AVAILABLE = False


import lpips
import torch
import torchvision.transforms as transforms
LPIPS_AVAILABLE = True
from pytorch_msssim import ms_ssim
    # print("警告: lpips未安装，无法计算LPIPS")
    # LPIPS_AVAILABLE = False

# 使用NumPy数组比较原始图像和重建图像
original_img_array = np.array(original_img)
reconstructed_img_array = np.array(reconstructed_img)
is_lossless = np.array_equal(original_img_array, reconstructed_img_array)
print(f'\n质量分析:')
print(f'压缩是否无损: {is_lossless}')

# 计算PSNR (峰值信噪比) - 使用NumPy数组
mse = np.mean((original_img_array.astype(float) - reconstructed_img_array.astype(float)) ** 2)
print(f'MSE: {mse:.4f}')
psnr = 10 * np.log10(255**2 / mse) if mse > 0 else float('inf')
print(f'PSNR: {psnr:.2f} dB')

# 计算SSIM (结构相似性指数)
if SSIM_AVAILABLE:
    # 转换为灰度图像计算SSIM
    if len(original_img_array.shape) == 3:
        original_gray = np.mean(original_img_array, axis=2)
        reconstructed_gray = np.mean(reconstructed_img_array, axis=2)
    else:
        original_gray = original_img_array
        reconstructed_gray = reconstructed_img_array
    
    ssim_value = ssim(original_gray, reconstructed_gray, data_range=255)
    print(f'SSIM: {ssim_value:.4f}')
    
    # 计算完整版MS-SSIM (多尺度结构相似性指数)
    try:
        # 方法1: 使用PyTorch MS-SSIM库 (推荐)
        try:
            from pytorch_msssim import ms_ssim as pytorch_ms_ssim
            import torch

            # 转换为tensor格式
            img1_tensor = torch.from_numpy(original_gray).float().unsqueeze(0).unsqueeze(0)
            img2_tensor = torch.from_numpy(reconstructed_gray).float().unsqueeze(0).unsqueeze(0)

            # 检查图像尺寸 (MS-SSIM需要至少160x160)
            h, w = img1_tensor.shape[-2:]
            if h >= 160 and w >= 160:
                ms_ssim_val = pytorch_ms_ssim(img1_tensor, img2_tensor, data_range=255, size_average=True)
                ms_ssim = ms_ssim_val.item()

                # 计算MS-SSIM的dB表示 (可选)
                if ms_ssim >= 1.0:
                    ms_ssim_db = float('inf')
                elif ms_ssim <= 0.0:
                    ms_ssim_db = float('-inf')
                else:
                    ms_ssim_db = -10 * np.log10(1 - ms_ssim)

                print(f'MS-SSIM (完整版): {ms_ssim:.6f}')
                print(f'MS-SSIM (dB): {ms_ssim_db:.2f} dB')
            else:
                print(f'图像尺寸 {h}x{w} 太小，MS-SSIM需要至少160x160，使用简化版本')
                raise ValueError("图像太小")

        except (ImportError, ValueError) as e:
            # 方法2: 使用简化的多窗口SSIM作为fallback
            print("使用简化版MS-SSIM...")
            ssim_values = []
            for win_size in [7, 11, 15]:
                if min(original_gray.shape) > win_size:
                    ssim_val = ssim(original_gray, reconstructed_gray,
                                  data_range=255, win_size=win_size)
                    ssim_values.append(ssim_val)

            if ssim_values:
                ms_ssim = np.mean(ssim_values)
                print(f'MS-SSIM (近似): {ms_ssim:.4f}')

    except Exception as e:
        print(f"MS-SSIM计算失败: {e}")
        pass
else:
    ssim_value = None

# 计算LPIPS (学习感知图像块相似性)
if LPIPS_AVAILABLE:
    try:
        # 初始化LPIPS模型
        lpips_model = lpips.LPIPS(net='alex')  # 可以选择 'alex', 'vgg', 'squeeze'
        
        # 转换图像为tensor格式
        transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((256, 256)),  # LPIPS通常需要固定尺寸
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])  # 归一化到[-1,1]
        ])
        
        # 处理图像
        if len(original_img_array.shape) == 3:
            original_tensor = transform(original_img_array).unsqueeze(0)
            reconstructed_tensor = transform(reconstructed_img_array).unsqueeze(0)
        else:
            # 灰度图像转RGB
            original_rgb = np.stack([original_img_array]*3, axis=-1)
            reconstructed_rgb = np.stack([reconstructed_img_array]*3, axis=-1)
            original_tensor = transform(original_rgb).unsqueeze(0)
            reconstructed_tensor = transform(reconstructed_rgb).unsqueeze(0)
        
        # 计算LPIPS
        with torch.no_grad():
            lpips_value = lpips_model(original_tensor, reconstructed_tensor).item()
        print(f'LPIPS (Alex): {lpips_value:.4f}')
        
        # 尝试使用VGG网络
        try:
            lpips_vgg = lpips.LPIPS(net='vgg')
            with torch.no_grad():
                lpips_vgg_value = lpips_vgg(original_tensor, reconstructed_tensor).item()
            print(f'LPIPS (VGG): {lpips_vgg_value:.4f}')
        except:
            pass
            
    except Exception as e:
        print(f"LPIPS计算失败: {e}")
        lpips_value = None
else:
    lpips_value = None

# 显示差异图 - 使用NumPy数组
diff = np.abs(original_img_array.astype(float) - reconstructed_img_array.astype(float))
# 统计像素差异
non_zero_diff = np.count_nonzero(diff)
total_pixels = diff.size
print(f'不同像素数: {non_zero_diff}/{total_pixels} ({non_zero_diff/total_pixels*100:.4f}%)')
print(f'最大像素差异: {np.max(diff):.2f}')

# 放大差异以便可视化
diff_scaled = diff * 10

plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1)
plt.title('Original Image')
plt.imshow(original_img)
plt.axis('off')

plt.subplot(1, 3, 2)
plt.title('Reconstructed Image')
plt.imshow(reconstructed_img)
plt.axis('off')

plt.subplot(1, 3, 3)
plt.title('Difference Image (10x magnified)')
plt.imshow(diff_scaled.astype(np.uint8))
plt.colorbar()
plt.axis('off')
plt.tight_layout()
plt.show()

# 性能总结
print(f'\n性能总结:')
print(f'{"方法":<12} {"大小(字节)":<12} {"PSNR(dB)":<10} {"BPP":<8} {"压缩比":<8}')
print('-' * 60)
# print(f'{"VAE+QAM":<12} {compressed_size:<12,} {psnr:<10.2f} {bpp:<8.4f} {compression_ratio:<8.2f}')


