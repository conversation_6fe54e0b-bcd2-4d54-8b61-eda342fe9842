#======== File I/O =====================
BitstreamFile                 : str.bin
ReconFile                     : rec.yuv

#======== Profile ================
Profile                       : auto

#======== Unit definition ================
MaxCUWidth                    : 64          # Maximum coding unit width in pixel
MaxCUHeight                   : 64          # Maximum coding unit height in pixel

#======== Coding Structure =============
IntraPeriod                   : 1           # Period of I-Frame ( -1 = only first)
DecodingRefreshType           : 1           # Random Accesss 0:none, 1:CRA, 2:IDR, 3:Recovery Point SEI
GOPSize                       : 1           # GOP Size (number of B slice = GOPSize-1)
#        Type POC QPoffset QPfactor tcOffsetDiv2 betaOffsetDiv2  temporal_id #ref_pics_active #ref_pics reference pictures

#=========== Motion Search =============
FastSearch                    : 1           # 0:Full search  1:TZ search
SearchRange                   : 64          # (0: Search range is a Full frame)
HadamardME                    : 1           # Use of hadamard measure for fractional ME
FEN                           : 1           # Fast encoder decision
FDM                           : 1           # Fast Decision for Merge RD cost

#======== Quantization =============
QP                            : 32          # Quantization parameter(0-51)
MaxDeltaQP                    : 0           # CU-based multi-QP optimization
MaxCuDQPSubdiv                : 0           # Maximum subdiv for CU luma Qp adjustment
DeltaQpRD                     : 0           # Slice-based multi-QP optimization
RDOQ                          : 1           # RDOQ
RDOQTS                        : 1           # RDOQ for transform skip

#=========== Deblock Filter ============
DeblockingFilterOffsetInPPS         : 1           # Dbl params: 0=varying params in SliceHeader, param = base_param + GOP_offset_param; 1 (default) =constant params in PPS, param = base_param)
DeblockingFilterDisable             : 0           # Disable deblocking filter (0=Filter, 1=No Filter)
DeblockingFilterBetaOffset_div2     : -2           # base_param: -12 ~ 12
DeblockingFilterTcOffset_div2       : -5           # base_param: -12 ~ 12
DeblockingFilterCbBetaOffset_div2   : -2           # base_param: -12 ~ 12
DeblockingFilterCbTcOffset_div2     : -5           # base_param: -12 ~ 12
DeblockingFilterCrBetaOffset_div2   : -2           # base_param: -12 ~ 12
DeblockingFilterCrTcOffset_div2     : -5           # base_param: -12 ~ 12
DeblockingFilterMetric        : 0           # blockiness metric (automatically configures deblocking parameters in bitstream). Applies slice-level loop filter offsets (DeblockingFilterOffsetInPPS and DeblockingFilterDisable must be 0)

#=========== Misc. ============
InternalBitDepth              : 10          # codec operating bit-depth

#=========== Coding Tools =================
SAO                           : 1           # Sample adaptive offset  (0: OFF, 1: ON)
TransformSkip                 : 1           # Transform skipping (0: OFF, 1: ON)
TransformSkipFast             : 1           # Fast Transform skipping (0: OFF, 1: ON)
TransformSkipLog2MaxSize      : 5
SAOLcuBoundary                : 0           # SAOLcuBoundary using non-deblocked pixels (0: OFF, 1: ON)

#============ VTM settings ======================
SEIDecodedPictureHash               : 0
CbQpOffset                          : 0
CrQpOffset                          : 0
SameCQPTablesForAllChroma           : 1
QpInValCb                           : 17 27 32 44
QpOutValCb                          : 17 29 34 41
TemporalSubsampleRatio              : 8

ReWriteParamSets                    : 1
#============ NEXT ====================

# General
CTUSize                      : 128
LCTUFast                     : 1

DualITree                    : 1      # separate partitioning of luma and chroma channels for I-slices
MinQTLumaISlice              : 8
MinQTChromaISliceInChromaSamples: 4      # minimum QT size in chroma samples for chroma separate tree
MinQTNonISlice               : 8
MaxMTTHierarchyDepth         : 3
MaxMTTHierarchyDepthISliceL  : 3
MaxMTTHierarchyDepthISliceC  : 3

MTS                          : 1
MTSIntraMaxCand              : 4
MTSInterMaxCand              : 4
SBT                          : 1
LFNST                        : 1
ISP                          : 1
Affine                       : 1
SbTMVP                       : 1
MaxNumMergeCand              : 6
LMChroma                     : 1      # use CCLM only
DepQuant                     : 1
IMV                          : 1
ALF                          : 1
IBC                          : 0      # turned off in CTC
AllowDisFracMMVD             : 1
AffineAmvr                   : 0
LMCSEnable                   : 1      # LMCS: 0: disable, 1:enable
LMCSSignalType               : 0      # Input signal type: 0:SDR, 1:HDR-PQ, 2:HDR-HLG
LMCSUpdateCtrl               : 1      # LMCS model update control: 0:RA, 1:AI, 2:LDB/LDP
LMCSOffset                   : 2      # chroma residual scaling offset
MRL                          : 1
MIP                          : 1
JointCbCr                    : 1      # joint coding of chroma residuals (if available): 0: disable, 1: enable
ChromaTS                     : 1

# Fast tools
PBIntraFast                  : 1
ISPFast                      : 1
FastMrg                      : 1
AMaxBT                       : 1
FastMIP                      : 1
FastLFNST                    : 1

# Encoder optimization tools
AffineAmvrEncOpt             : 0
ALFAllowPredefinedFilters    : 1
ALFStrengthTargetLuma        : 1.0
ALFStrengthTargetChroma      : 1.0
CCALFStrengthTarget          : 1.0
### DO NOT ADD ANYTHING BELOW THIS LINE ###
### DO NOT DELETE THE EMPTY LINE BELOW ###


