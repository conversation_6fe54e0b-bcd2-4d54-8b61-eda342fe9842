import torch.nn as nn
import numpy as np
import torch
import random
from lvae.models.entropy_coding import gaussian_log_prob_mass
import math

# 精简版 Channel 类 - 只保留核心功能
class Channel(nn.Module):
    """精简的信道模块，只保留核心功能"""
    
    def __init__(self, noise_type='awgn', snr=20.0, verbose=False, direct_noise=False, random_seed=42):
        super().__init__()

        # 设置随机种子
        torch.manual_seed(random_seed)
        np.random.seed(random_seed)
        random.seed(random_seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(random_seed)
            torch.cuda.manual_seed_all(random_seed)

        # 核心参数
        self.snr = snr
        self.direct_noise = direct_noise
        self.verbose = verbose
        self.training_mode = False  # 默认推理模式

        # Gumbel Softmax 温度控制
        self.temperature = 1.0  # 初始温度
        self.min_temperature = 0.1  # 最小温度
        self.temperature_decay = 0.99  # 温度衰减率
        self.use_progressive_hardening = False  # 是否使用渐进式硬化

        # Maxwell-<PERSON><PERSON><PERSON> 相关参数
        self.lambda_value = self._compute_lambda_from_snr(snr)
        self.mb_kl_loss = None

        # 重要性感知
        self.importance_map = None

        # 统计信息
        self.symbol_error_rate = 0.0
        self.total_symbols = 0

        # 🔥 新增：准确的传输统计
        self.reset_transmission_stats()

        if verbose:
            print(f"🎲 Channel模块初始化: SNR={snr}dB, λ={self.lambda_value:.3f}, 温度={self.temperature:.3f}")
    
    def _compute_lambda_from_snr(self, snr_db):
        """根据SNR计算λ值的简化版本"""
        # 简化的SNR到λ映射
        snr_linear = 10.0 ** (snr_db / 10.0)
        lambda_val = 1.0 / (1.0 + snr_linear * 0.1)
        return max(0.01, min(1.0, lambda_val))
    
    def set_mode(self, training):
        """设置训练/推理模式"""
        self.training_mode = training
        if self.verbose:
            mode_str = "训练" if training else "推理"
            print(f"🔄 Channel模式切换: {mode_str}模式, 当前温度={self.temperature:.3f}")

    def set_temperature(self, temperature):
        """设置Gumbel Softmax温度"""
        self.temperature = max(self.min_temperature, temperature)
        if self.verbose:
            print(f"🌡️  温度设置: {self.temperature:.3f}")

    def enable_progressive_hardening(self, enable=True):
        """启用/禁用渐进式硬化"""
        self.use_progressive_hardening = enable
        if self.verbose:
            status = "启用" if enable else "禁用"
            print(f"🔄 渐进式硬化: {status}")

    def update_temperature(self):
        """更新温度（用于渐进式硬化）"""
        if self.use_progressive_hardening and self.training_mode:
            old_temp = self.temperature
            self.temperature = max(self.min_temperature, self.temperature * self.temperature_decay)
            if self.verbose and abs(old_temp - self.temperature) > 0.001:
                print(f"🌡️  温度衰减: {old_temp:.3f} → {self.temperature:.3f}")

    def _compute_importance_map(self, qm, pm, pv):
        """基于概率分布计算qm_for_sampling的重要性分布"""
        # 计算qm在先验分布下的概率
        qm_probs = torch.exp(gaussian_log_prob_mass(pm, pv, x=qm, bin_size=1.0, prob_clamp=1e-6))

        # 信息量 = -log(概率)，概率越低信息量越大，越重要
        information_content = -torch.log(qm_probs + 1e-8)

        # 归一化到[0,1]范围，信息量大的重要性高
        importance_map = information_content / (information_content.max() + 1e-8)

        return importance_map

    def _apply_mb_shaping(self, qm, importance_map, is_training):
        """应用Maxwell-Boltzmann分布整形 - 简化版本"""
        # 简化的MB整形：基于重要性映射调整特征
        if importance_map is not None and not is_training:
            # 推理模式：根据重要性调整特征分布
            shaped_qm = qm * (1.0 + importance_map * self.lambda_value)
            
            # 计算简化的MB KL损失
            mb_kl_loss = torch.mean(importance_map * torch.log(importance_map + 1e-8))
            mb_info = {'mb_kl_loss': mb_kl_loss}
            
            if self.verbose:
                print(f"MB整形: λ={self.lambda_value:.3f}, KL损失={mb_kl_loss:.6f}")
            
            return shaped_qm, mb_info
        else:
            # 训练模式或无重要性映射：直接返回
            return qm, None

    def forward(self, features, is_training=None, transmission_masks=None):
        """前向传播 - 支持真正的masked_select操作

        Args:
            features: 特征字典 {res: tensor}
            is_training: 是否训练模式
            transmission_masks: 传输掩码字典 {res: mask_tensor}，如果提供则进行真正的元素删除
        """
        # 确定当前模式
        if is_training is None:
            is_training = self.training_mode

        processed_features = {}
        transmission_stats = {}

        for res, feature in features.items():
            if self.verbose:
                print(f"处理分辨率{res}: 形状{feature.shape}")

            # 🔥 记录传输统计
            feature_count = feature.numel()
            self.transmission_stats['total_features_processed'] += feature_count

            # 记录分辨率统计
            if res not in self.transmission_stats['resolution_stats']:
                self.transmission_stats['resolution_stats'][res] = {
                    'features': 0, 'symbols': 0, 'bits': 0
                }
            self.transmission_stats['resolution_stats'][res]['features'] += feature_count

            # 检查是否有对应的传输掩码
            mask = transmission_masks.get(res) if transmission_masks else None

            if mask is not None:
                # 🔥 新功能：真正的masked_select操作
                processed_feature, stats = self._process_with_mask(feature, mask, is_training)
                processed_features[res] = processed_feature
                transmission_stats[res] = stats
            else:
                # 原有的处理方式
                if self.direct_noise:
                    processed_features[res] = self._add_awgn_noise(feature)
                else:
                    # QAM调制模式：完整的调制-信道-解调流程
                    if is_training:
                        # 训练模式：软量化，保持可微分性
                        processed_features[res] = self._training_mode_process(feature, res)
                    else:
                        # 推理模式：硬量化，精确处理
                        processed_features[res] = self._inference_mode_process(feature, res)

                transmission_stats[res] = {
                    'original_elements': feature.numel(),
                    'transmitted_elements': feature.numel(),
                    'compression_ratio': 1.0
                }

        # 如果有传输统计，返回额外信息
        if transmission_masks is not None:
            return processed_features, transmission_stats
        else:
            return processed_features

    def _process_with_mask(self, feature, mask, is_training):
        """使用掩码进行真正的元素删除和重构

        Args:
            feature: 输入特征 [B, C, H, W]
            mask: 布尔掩码 [B, C, H, W]
            is_training: 是否训练模式

        Returns:
            reconstructed_feature: 重构后的特征 [B, C, H, W]
            stats: 传输统计信息
        """
        original_shape = feature.shape
        original_elements = feature.numel()

        # 🔥 关键步骤1：真正删除mask=False的元素
        mask_bool = mask.bool()
        selected_elements = torch.masked_select(feature, mask_bool)  # 一维张量

        transmitted_elements = selected_elements.numel()
        compression_ratio = transmitted_elements / original_elements if original_elements > 0 else 0

        if self.verbose:
            print(f"  🔥 Masked Select: {original_elements} → {transmitted_elements} 元素 (压缩率: {compression_ratio:.3f})")

        # 🔥 关键步骤2：对选中的元素进行信道处理
        if transmitted_elements > 0:
            # 将一维张量重塑为适合处理的形状
            # 为了保持兼容性，我们创建一个临时的4D张量
            temp_shape = (1, 1, 1, transmitted_elements)
            temp_feature = selected_elements.view(temp_shape)

            # 应用信道处理
            if self.direct_noise:
                processed_temp = self._add_awgn_noise(temp_feature)
            else:
                if is_training:
                    processed_temp = self._gumbel_softmax_qam(temp_feature, temperature=self.temperature, hard=False)
                    processed_temp = self._add_awgn_noise(processed_temp)
                else:
                    processed_temp = self._gumbel_softmax_qam(temp_feature, temperature=0.1, hard=True)
                    processed_temp = self._add_awgn_noise(processed_temp)

            # 提取处理后的一维数据
            processed_elements = processed_temp.view(-1)
        else:
            processed_elements = selected_elements  # 空张量

        # 🔥 关键步骤3：重构回原始形状
        reconstructed_feature = torch.zeros_like(feature)
        if transmitted_elements > 0:
            reconstructed_feature[mask_bool] = processed_elements

        # 统计信息
        stats = {
            'original_elements': original_elements,
            'transmitted_elements': transmitted_elements,
            'compression_ratio': compression_ratio,
            'mask_ratio': mask_bool.float().mean().item()
        }

        return reconstructed_feature, stats

    def _add_awgn_noise(self, feature):
        """添加AWGN噪声的简化版本"""
        # 计算信号功率
        signal_power = torch.mean(feature.pow(2))
        signal_power = torch.clamp(signal_power, min=1e-10)
        
        # 计算噪声功率
        snr_linear = 10.0 ** (self.snr / 10.0)
        noise_power = signal_power / snr_linear
        
        # 生成并添加噪声
        noise = torch.sqrt(noise_power) * torch.randn_like(feature)
        noisy_feature = feature + noise
        
        # print(f"  AWGN噪声: SNR={self.snr}dB, 信号功率={signal_power:.6f}")
        
        return noisy_feature
    
    def _gumbel_softmax_qam(self, feature, temperature=1.0, hard=False):
        """统一的Gumbel Softmax QAM调制方法 - 修复版本

        Args:
            feature: 输入特征
            temperature: 温度参数，越小越接近硬判决
            hard: 是否使用硬判决（straight-through estimator）
        """
        qam_order = 256

        # 获取星座点
        constellation_points = self._get_constellation_points(qam_order, feature.device)
        constellation_min = constellation_points.min()
        constellation_max = constellation_points.max()

        # 1. 将特征直接映射到星座点范围
        feature_min = feature.min()
        feature_max = feature.max()
        feature_range = feature_max - feature_min

        if feature_range > 1e-8:
            # 直接映射到星座点范围
            mapped_to_constellation = (feature - feature_min) / feature_range * (constellation_max - constellation_min) + constellation_min

            # 2. 计算到每个星座点的距离
            distances = torch.abs(mapped_to_constellation.unsqueeze(-1) - constellation_points.view(1, 1, 1, 1, -1))
            logits = -distances  # 距离越近，logits越大

            # 3. Gumbel Softmax
            if hard:
                # 硬判决：直接取最近的符号
                symbol_indices = torch.argmin(distances, dim=-1)
                # Straight-through estimator: 前向用硬判决，反向用软判决
                soft_samples = torch.softmax(logits / temperature, dim=-1)
                hard_samples = torch.zeros_like(soft_samples)
                hard_samples.scatter_(-1, symbol_indices.unsqueeze(-1), 1.0)
                # 使用straight-through技巧
                samples = hard_samples - soft_samples.detach() + soft_samples
            else:
                # 软判决：Gumbel Softmax
                gumbel_noise = -torch.log(-torch.log(torch.rand_like(logits) + 1e-8) + 1e-8)
                gumbel_logits = (logits + gumbel_noise) / temperature
                samples = torch.softmax(gumbel_logits, dim=-1)

            # 4. 映射到QAM星座点
            modulated = torch.sum(samples * constellation_points.view(1, 1, 1, 1, -1), dim=-1)

            # 保存调制信息用于解调
            self.last_feature_min = feature_min
            self.last_feature_max = feature_max
            self.last_constellation_min = constellation_min
            self.last_constellation_max = constellation_max

            # 保存调制索引用于错误率计算
            if hard:
                self.last_modulated_indices = symbol_indices
            else:
                # 软判决时保存最可能的索引
                self.last_modulated_indices = torch.argmin(distances, dim=-1)

            if self.verbose:
                mode_str = "硬判决" if hard else "软判决"
                print(f"  Gumbel Softmax QAM: {mode_str}, 温度={temperature:.3f}")
                print(f"  特征范围: [{feature_min:.4f}, {feature_max:.4f}] → 星座范围: [{constellation_min:.4f}, {constellation_max:.4f}]")
                print(f"  调制后范围: [{modulated.min():.4f}, {modulated.max():.4f}]")
        else:
            modulated = feature

        return modulated

    def _training_mode_process(self, feature, resolution):
        """训练模式处理：使用软判决Gumbel Softmax"""
        qam_order = 256  # 固定使用256-QAM

        # 使用当前温度进行软判决
        modulated = self._gumbel_softmax_qam(feature, temperature=self.temperature, hard=False)

        # 如果启用渐进式硬化，更新温度
        if self.use_progressive_hardening:
            self.update_temperature()

        # 添加信道噪声
        noisy = self._add_awgn_noise(modulated)

        # 解调：软判决（保持可微分性）
        demodulated = self._qam_demodulate(noisy, qam_order=qam_order, hard_decision=False)

        # 🔥 记录传输统计
        self._record_transmission_stats(feature, qam_order, resolution, hard_decision=False)

        return demodulated

    def _inference_mode_process(self, feature, resolution):
        """推理模式处理：使用硬判决Gumbel Softmax（温度接近0）"""
        qam_order = 256  # 固定使用256-QAM

        # 推理时使用硬判决，温度接近0
        modulated = self._gumbel_softmax_qam(feature, temperature=0.1, hard=True)

        # 添加信道噪声
        noisy = self._add_awgn_noise(modulated)

        # 解调：硬判决（精确恢复）
        demodulated = self._qam_demodulate(noisy, qam_order=qam_order, hard_decision=True)

        # 🔥 记录传输统计
        self._record_transmission_stats(feature, qam_order, resolution, hard_decision=True)

        return demodulated

    def _get_constellation_points(self, qam_order, device):
        """获取QAM星座点 - 修复版本"""
        if qam_order == 4:
            # 4-QAM: 均匀分布4个点
            points = torch.linspace(-3, 3, 4)
        elif qam_order == 16:
            # 16-QAM: 均匀分布16个点
            points = torch.linspace(-7, 7, 16)
        elif qam_order == 64:
            # 64-QAM: 均匀分布64个点
            points = torch.linspace(-15, 15, 64)
        elif qam_order == 256:
            # 256-QAM: 均匀分布256个点
            points = torch.linspace(-31, 31, 256)
        else:
            # 默认：均匀分布
            # 使用对称的范围，确保0附近有点
            max_val = qam_order // 4  # 简单的范围估计
            points = torch.linspace(-max_val, max_val, qam_order)

        return points.to(device=device, dtype=torch.float32)

    def _qam_demodulate(self, noisy_signal, qam_order=256, hard_decision=True):
        """QAM解调：将噪声信号解调回原始特征范围 - 修复版本

        Args:
            noisy_signal: 经过噪声的信号
            qam_order: QAM阶数
            hard_decision: 是否使用硬判决

        Returns:
            demodulated: 解调后的信号（映射回原始特征范围）
        """
        # 获取星座点
        constellation_points = self._get_constellation_points(qam_order, noisy_signal.device)

        if hard_decision:
            # 硬判决：找到最近的星座点
            distances = torch.abs(noisy_signal.unsqueeze(-1) - constellation_points.view(1, 1, 1, 1, -1))
            nearest_indices = torch.argmin(distances, dim=-1)
            # 映射到最近的星座点
            constellation_values = constellation_points[nearest_indices]

            # 计算符号错误率
            if hasattr(self, 'last_modulated_indices'):
                symbol_errors = (nearest_indices != self.last_modulated_indices).float().sum()
                self.total_symbols += nearest_indices.numel()
                self.symbol_error_rate = symbol_errors / nearest_indices.numel()

        else:
            # 软判决：使用加权平均（保持可微分性）
            distances = torch.abs(noisy_signal.unsqueeze(-1) - constellation_points.view(1, 1, 1, 1, -1))
            weights = torch.softmax(-distances / self.temperature, dim=-1)
            constellation_values = torch.sum(weights * constellation_points.view(1, 1, 1, 1, -1), dim=-1)

        # 将星座点值映射回原始特征范围
        if hasattr(self, 'last_feature_min') and hasattr(self, 'last_feature_max'):
            constellation_min = self.last_constellation_min
            constellation_max = self.last_constellation_max
            feature_min = self.last_feature_min
            feature_max = self.last_feature_max

            # 反向映射：星座范围 → 特征范围
            constellation_range = constellation_max - constellation_min
            feature_range = feature_max - feature_min

            if constellation_range > 1e-8:
                demodulated = (constellation_values - constellation_min) / constellation_range * feature_range + feature_min
            else:
                demodulated = constellation_values
        else:
            # 如果没有保存的映射信息，直接返回星座点值
            demodulated = constellation_values

        if self.verbose:
            decision_type = "硬判决" if hard_decision else "软判决"
            print(f"  QAM解调: {decision_type}")
            if hasattr(self, 'last_feature_min'):
                print(f"  星座范围: [{constellation_min:.4f}, {constellation_max:.4f}] → 特征范围: [{feature_min:.4f}, {feature_max:.4f}]")
            print(f"  解调输出范围: [{demodulated.min():.4f}, {demodulated.max():.4f}]")
            if hard_decision and hasattr(self, 'symbol_error_rate'):
                print(f"  符号错误率: {self.symbol_error_rate:.6f}")

        return demodulated

    def reset_transmission_stats(self):
        """重置传输统计信息"""
        self.transmission_stats = {
            'total_symbols_transmitted': 0,
            'total_bits_transmitted': 0,
            'total_features_processed': 0,
            'qam_order_usage': {},  # 记录每种QAM阶数的使用次数
            'resolution_stats': {},  # 按分辨率统计
            'symbol_errors': 0,
            'total_symbol_decisions': 0
        }

    def get_transmission_stats(self):
        """获取准确的传输统计信息"""
        stats = self.transmission_stats.copy()

        # 计算平均值
        if stats['total_symbols_transmitted'] > 0:
            stats['avg_bits_per_symbol'] = stats['total_bits_transmitted'] / stats['total_symbols_transmitted']
            stats['symbol_error_rate'] = stats['symbol_errors'] / stats['total_symbol_decisions'] if stats['total_symbol_decisions'] > 0 else 0.0
        else:
            stats['avg_bits_per_symbol'] = 0.0
            stats['symbol_error_rate'] = 0.0

        # 添加信道参数
        stats['snr_db'] = self.snr
        stats['lambda_value'] = self.lambda_value

        return stats
    def _record_transmission_stats(self, feature, qam_order, resolution, hard_decision):
        """记录传输统计信息 - 修复版本，只计算实际传输的非零元素"""
        # 🔥 修复：只计算非零元素（实际传输的符号）
        # 使用一个小的阈值来判断是否为零（考虑浮点精度）
        non_zero_mask = torch.abs(feature) > 1e-8
        num_symbols = non_zero_mask.sum().item()

        # 如果没有非零元素，直接返回
        if num_symbols == 0:
            return

        # 计算比特数
        bits_per_symbol = math.log2(qam_order)
        num_bits = num_symbols * bits_per_symbol

        # 更新总体统计
        self.transmission_stats['total_symbols_transmitted'] += num_symbols
        self.transmission_stats['total_bits_transmitted'] += num_bits

        # 更新QAM阶数使用统计
        if qam_order not in self.transmission_stats['qam_order_usage']:
            self.transmission_stats['qam_order_usage'][qam_order] = 0
        self.transmission_stats['qam_order_usage'][qam_order] += num_symbols

        # 更新分辨率统计
        if resolution in self.transmission_stats['resolution_stats']:
            self.transmission_stats['resolution_stats'][resolution]['symbols'] += num_symbols
            self.transmission_stats['resolution_stats'][resolution]['bits'] += num_bits

        # 记录符号错误（如果有的话）
        if hard_decision and hasattr(self, 'symbol_error_rate'):
            self.transmission_stats['symbol_errors'] += int(self.symbol_error_rate * num_symbols)
            self.transmission_stats['total_symbol_decisions'] += num_symbols

        # 🔥 新增：记录掩码统计信息
        total_elements = feature.numel()
        transmission_ratio = num_symbols / total_elements if total_elements > 0 else 0

        if 'mask_stats' not in self.transmission_stats:
            self.transmission_stats['mask_stats'] = {
                'total_elements': 0,
                'transmitted_elements': 0,
                'masked_elements': 0
            }

        self.transmission_stats['mask_stats']['total_elements'] += total_elements
        self.transmission_stats['mask_stats']['transmitted_elements'] += num_symbols
        self.transmission_stats['mask_stats']['masked_elements'] += (total_elements - num_symbols)
