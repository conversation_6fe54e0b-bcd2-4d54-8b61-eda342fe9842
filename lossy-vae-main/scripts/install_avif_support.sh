#!/bin/bash

echo "=== AVIF编码器安装指南 ==="
echo ""

# 检查操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "检测到Linux系统"
    
    # Ubuntu/Debian系统
    if command -v apt-get &> /dev/null; then
        echo ""
        echo "🔧 Ubuntu/Debian系统安装步骤："
        echo "1. 安装系统依赖库："
        echo "   sudo apt-get update"
        echo "   sudo apt-get install -y libavif-dev libavif15"
        echo ""
        echo "2. 重新安装支持AVIF的Pillow："
        echo "   pip uninstall pillow -y"
        echo "   pip install pillow --upgrade --force-reinstall"
        echo ""
        
    # CentOS/RHEL/Fedora系统
    elif command -v yum &> /dev/null || command -v dnf &> /dev/null; then
        echo ""
        echo "🔧 CentOS/RHEL/Fedora系统安装步骤："
        echo "1. 安装系统依赖库："
        if command -v dnf &> /dev/null; then
            echo "   sudo dnf install -y libavif-devel"
        else
            echo "   sudo yum install -y libavif-devel"
        fi
        echo ""
        echo "2. 重新安装支持AVIF的Pillow："
        echo "   pip uninstall pillow -y"
        echo "   pip install pillow --upgrade --force-reinstall"
        echo ""
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "检测到macOS系统"
    echo ""
    echo "🔧 macOS系统安装步骤："
    echo "1. 使用Homebrew安装libavif："
    echo "   brew install libavif"
    echo ""
    echo "2. 重新安装支持AVIF的Pillow："
    echo "   pip uninstall pillow -y"
    echo "   pip install pillow --upgrade --force-reinstall"
    echo ""
fi

echo "=== 验证安装 ==="
echo "安装完成后，运行以下命令验证AVIF支持："
echo "python -c \"from PIL import Image; print('AVIF支持:', 'AVIF' in Image.registered_extensions().values())\""
echo ""

echo "=== 替代方案 ==="
echo "如果AVIF安装失败，系统会自动使用WebP作为替代方案。"
echo "WebP在现代浏览器中有很好的支持，压缩效果也很优秀。"
echo ""

echo "=== 自动安装脚本 ==="
echo "如果您想自动安装，请运行："
echo "chmod +x scripts/install_avif_support.sh"
echo "./scripts/install_avif_support.sh auto"
echo ""

# 如果传入auto参数，自动执行安装
if [[ "$1" == "auto" ]]; then
    echo "开始自动安装..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]] && command -v apt-get &> /dev/null; then
        echo "正在安装Ubuntu/Debian依赖..."
        sudo apt-get update
        sudo apt-get install -y libavif-dev libavif15
        
        echo "重新安装Pillow..."
        pip uninstall pillow -y
        pip install pillow --upgrade --force-reinstall
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "正在安装macOS依赖..."
        brew install libavif
        
        echo "重新安装Pillow..."
        pip uninstall pillow -y
        pip install pillow --upgrade --force-reinstall
    fi
    
    echo "验证安装结果..."
    python -c "from PIL import Image; print('AVIF支持:', 'AVIF' in Image.registered_extensions().values())"
fi
