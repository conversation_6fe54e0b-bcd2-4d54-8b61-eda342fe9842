#!/usr/bin/env python3
"""
改进的调制解调使用示例
展示如何在训练中使用新的调制解调方法
"""

import torch
import torch.nn as nn
from lvae.models.qrvesvae_sc.model import Channel

def example_usage():
    """展示改进的调制解调使用方法"""
    
    # 创建信道模块
    channel = Channel(noise_type='awgn', snr=20.0)
    
    # 模拟VAE编码器输出的特征
    features = {
        32: torch.randn(2, 128, 32, 32),  # 高分辨率特征
        16: torch.randn(2, 256, 16, 16),  # 中分辨率特征  
        8: torch.randn(2, 384, 8, 8)     # 低分辨率特征
    }
    
    print("=== 改进的调制解调使用示例 ===")
    print(f"输入特征:")
    for res, feat in features.items():
        print(f"  分辨率 {res}: {feat.shape}")
    
    # 前向传播（训练模式）
    channel.train()
    processed_features = channel(features, is_training=True)
    
    print(f"\n处理后特征:")
    for res, feat in processed_features.items():
        print(f"  分辨率 {res}: {feat.shape}")
    
    # 计算PSNR
    total_psnr = 0
    count = 0
    for res in features.keys():
        if res in processed_features:
            original = features[res]
            processed = processed_features[res]
            mse = torch.mean((original - processed) ** 2)
            psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
            print(f"  分辨率 {res} PSNR: {psnr.item():.2f} dB")
            total_psnr += psnr.item()
            count += 1
    
    if count > 0:
        avg_psnr = total_psnr / count
        print(f"\n平均 PSNR: {avg_psnr:.2f} dB")
    
    return processed_features

def training_integration_example():
    """展示如何集成到训练循环中"""
    
    print("\n=== 训练集成示例 ===")
    
    # 创建信道模块
    channel = Channel(noise_type='awgn', snr=20.0)
    
    # 模拟训练循环
    for epoch in range(3):
        print(f"\nEpoch {epoch + 1}:")
        
        # 模拟批次数据
        batch_features = {
            16: torch.randn(4, 256, 16, 16),
            8: torch.randn(4, 384, 8, 8)
        }
        
        # 训练模式
        channel.train()
        
        # 前向传播
        processed_features = channel(batch_features, is_training=True)
        
        # 计算损失（示例）
        total_loss = 0
        for res in batch_features.keys():
            if res in processed_features:
                mse_loss = torch.mean((batch_features[res] - processed_features[res]) ** 2)
                total_loss += mse_loss
        
        print(f"  总损失: {total_loss.item():.6f}")
        
        # 在实际训练中，这里会进行反向传播
        # total_loss.backward()
        # optimizer.step()

def compression_example():
    """展示压缩和解压缩的使用"""
    
    print("\n=== 压缩解压缩示例 ===")
    
    # 创建信道模块
    channel = Channel(noise_type='awgn', snr=25.0)
    
    # 模拟要压缩的特征
    features = {
        16: torch.randn(1, 128, 16, 16),
        8: torch.randn(1, 256, 8, 8)
    }
    
    print("原始特征:")
    for res, feat in features.items():
        print(f"  分辨率 {res}: 均值={feat.mean().item():.4f}, 标准差={feat.std().item():.4f}")
    
    # 调制
    modulated_features, original_shapes, symbol_indices = channel.modulator.modulate(
        features, qam_order=256
    )
    
    print(f"\n调制完成，符号数量:")
    for res, mod_data in modulated_features.items():
        n_symbols = mod_data['n_symbols']
        print(f"  分辨率 {res}: {n_symbols} 符号")
    
    # 添加信道噪声
    noisy_features = channel.noise_adder.add_noise(modulated_features, channel.snr)
    
    # 解调
    demod_features, demod_indices = channel.demodulator.demodulate(noisy_features)
    
    print(f"\n解调后特征:")
    for res, feat in demod_features.items():
        print(f"  分辨率 {res}: 均值={feat.mean().item():.4f}, 标准差={feat.std().item():.4f}")
    
    # 计算重构质量
    print(f"\n重构质量:")
    for res in features.keys():
        if res in demod_features:
            original = features[res]
            reconstructed = demod_features[res]
            mse = torch.mean((original - reconstructed) ** 2)
            psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
            correlation = torch.corrcoef(torch.stack([
                original.flatten(),
                reconstructed.flatten()
            ]))[0, 1]
            print(f"  分辨率 {res}: PSNR={psnr.item():.2f} dB, 相关系数={correlation.item():.4f}")

def adaptive_qam_example():
    """展示自适应QAM阶数选择"""
    
    print("\n=== 自适应QAM阶数示例 ===")
    
    # 创建信道模块
    channel = Channel(noise_type='awgn', snr=20.0)
    
    # 不同复杂度的特征
    features = {
        'simple': torch.ones(1, 64, 8, 8) * 0.5,  # 简单特征
        'complex': torch.randn(1, 64, 8, 8) * 2.0  # 复杂特征
    }
    
    # 计算熵并选择QAM阶数
    entropy_dict = channel.entropy_estimator.estimate_entropy(features)
    
    print("特征熵值和推荐QAM阶数:")
    for name, entropy in entropy_dict.items():
        qam_order = channel.entropy_estimator.select_qam_order(entropy)
        print(f"  {name}: 熵={entropy:.2f}, 推荐QAM={qam_order}")
        
        # 使用推荐的QAM阶数进行调制
        feature_dict = {name: features[name]}
        modulated, _, _ = channel.modulator.modulate(feature_dict, qam_order=qam_order)
        noisy = channel.noise_adder.add_noise(modulated, channel.snr)
        demod, _ = channel.demodulator.demodulate(noisy)
        
        # 计算PSNR
        mse = torch.mean((features[name] - demod[name]) ** 2)
        psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
        print(f"    PSNR: {psnr.item():.2f} dB")

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    
    # 运行示例
    example_usage()
    training_integration_example()
    compression_example()
    adaptive_qam_example()
    
    print("\n=== 使用建议 ===")
    print("1. 在训练中使用 channel(features, is_training=True)")
    print("2. 根据特征复杂度选择合适的QAM阶数")
    print("3. 监控PSNR来评估重构质量")
    print("4. 可以根据SNR调整信道参数")
    print("5. 使用软调制可以获得更好的梯度流")
