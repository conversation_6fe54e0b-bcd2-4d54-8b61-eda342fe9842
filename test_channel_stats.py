#!/usr/bin/env python3
"""测试修复后的Channel统计功能"""

import sys
import os
import torch
from pathlib import Path
from PIL import Image
import torchvision.transforms.functional as tvf

# 添加项目路径
project_root = os.path.join(os.path.dirname(__file__), 'lossy-vae-main')
sys.path.insert(0, project_root)

def test_channel_stats():
    """测试Channel统计功能"""
    print("🧪 测试Channel统计功能")
    print("=" * 60)
    
    try:
        # 导入模型
        from lvae.models.qrvesvae_sc.zoo import qres34m_sc

        # 创建模型
        model = qres34m_sc(lmb=256, snr_db=20)
        model.eval()
        model.compress_mode(True)
        
        print(f"✅ 模型加载成功")
        print(f"📡 Channel SNR: {model.channel.snr}dB")
        
        # 加载测试图像
        img_path = 'lossy-vae-main/images/horse3.png'
        if not Path(img_path).exists():
            print(f"❌ 图像文件不存在: {img_path}")
            return
            
        im = tvf.to_tensor(Image.open(img_path)).unsqueeze_(0)
        print(f"📷 图像形状: {im.shape}")
        
        # 压缩图像（这会自动调用Channel统计）
        print(f"\n🗜️  开始压缩...")
        compressed_obj = model.compress(im)
        
        print(f"\n✅ 压缩完成！")
        print(f"📦 压缩对象包含 {len(compressed_obj)} 个元素")
        
        # 手动获取详细统计
        if hasattr(model, 'channel') and model.channel is not None:
            stats = model.channel.get_transmission_stats()
            print(f"\n📊 详细传输统计:")
            print(f"  总符号数: {stats['total_symbols_transmitted']:,}")
            print(f"  总比特数: {stats['total_bits_transmitted']:,.0f}")
            print(f"  平均比特/符号: {stats['avg_bits_per_symbol']:.2f}")
            print(f"  符号错误率: {stats['symbol_error_rate']:.4f}")
            
            # 计算BPP
            pixels = im.shape[-2] * im.shape[-1]
            bpp = stats['total_bits_transmitted'] / pixels
            print(f"  比特率: {bpp:.4f} bpp")
            
            # QAM使用统计
            if stats['qam_order_usage']:
                print(f"  QAM阶数使用: {dict(stats['qam_order_usage'])}")
            
            # 分辨率统计
            if stats['resolution_stats']:
                print(f"  分辨率统计:")
                for res, res_stats in stats['resolution_stats'].items():
                    print(f"    分辨率{res}: {res_stats['symbols']:,}符号, {res_stats['bits']:.0f}比特")
        
        # 测试解压缩
        print(f"\n🔄 开始解压缩...")
        im_hat = model.decompress(compressed_obj)
        print(f"✅ 解压缩完成！重构图像形状: {im_hat.shape}")
        
        # 计算PSNR
        mse = torch.mean((im - im_hat) ** 2)
        if mse > 0:
            psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
            print(f"📈 PSNR: {psnr:.2f} dB")
        else:
            print(f"📈 PSNR: ∞ dB (完美重构)")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_snr():
    """测试不同SNR下的统计"""
    print(f"\n🌊 测试不同SNR下的统计")
    print("=" * 60)
    
    try:
        from lvae.models.qrvesvae_sc.zoo import qres34m_sc

        # 创建小图像用于快速测试
        test_image = torch.rand(1, 3, 64, 64)  # 小图像

        snr_values = [10, 20, 30]

        for snr in snr_values:
            print(f"\n📡 SNR = {snr}dB:")

            model = qres34m_sc(lmb=256, snr_db=snr)
            model.eval()
            model.compress_mode(True)
            
            # 压缩
            compressed_obj = model.compress(test_image)
            
            # 获取统计
            if hasattr(model, 'channel') and model.channel is not None:
                stats = model.channel.get_transmission_stats()
                pixels = test_image.shape[-2] * test_image.shape[-1]
                bpp = stats['total_bits_transmitted'] / pixels
                
                print(f"  符号数: {stats['total_symbols_transmitted']:,}")
                print(f"  比特数: {stats['total_bits_transmitted']:,.0f}")
                print(f"  BPP: {bpp:.4f}")
                print(f"  符号错误率: {stats['symbol_error_rate']:.4f}")
                
                # 解压缩并计算PSNR
                im_hat = model.decompress(compressed_obj)
                mse = torch.mean((test_image - im_hat) ** 2)
                psnr = 20 * torch.log10(1.0 / torch.sqrt(mse)) if mse > 0 else float('inf')
                print(f"  PSNR: {psnr:.2f} dB")
            
    except Exception as e:
        print(f"❌ SNR测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试Channel统计功能")
    
    test_channel_stats()
    test_different_snr()
    
    print(f"\n✅ 所有测试完成！")
    print(f"\n💡 现在统计信息直接来自Channel，准确反映实际传输的符号和比特数")
