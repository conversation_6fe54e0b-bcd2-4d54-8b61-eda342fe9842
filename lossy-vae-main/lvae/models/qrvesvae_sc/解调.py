import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import norm
from matplotlib import cm
from mpl_toolkits.mplot3d import Axes3D
from scipy.optimize import minimize_scalar, root_scalar

# Remove Chinese font settings
# plt.rcParams['font.sans-serif'] = ['SimHei']
# plt.rcParams['axes.unicode_minus'] = False

# Define QAM constellation
def create_qam_constellation(M):
    """Create M-QAM constellation points"""
    n = int(np.sqrt(M))
    
    # 创建16个PAM电平值（适用于256-QAM）
    if n == 16:  # 256-QAM
        # 使用与model.py相同的PAM电平值
        pam_values = np.array([-15, -13, -11, -9, -7, -5, -3, -1, 1, 3, 5, 7, 9, 11, 13, 15])
        # 计算平均功率
        average_power = np.mean(pam_values**2)
        # 使用功率归一化因子
        normalization_factor = np.sqrt(average_power)
        # 归一化PAM电平
        pam_values = pam_values / normalization_factor
    else:
        # 对于其他QAM阶数，使用均匀分布的电平
        pam_values = np.linspace(-n+1, n-1, n)
    
    # 构建星座点
    constellation = np.array([(x, y) for y in pam_values for x in pam_values])
    return constellation

# Calculate energy of constellation points
def calculate_energy(constellation):
    """Calculate energy of constellation points"""
    return np.sum(constellation**2, axis=1)

# Probability shaping based on Maxwell-Boltzmann distribution
def maxwell_boltzmann_distribution(constellation, lambda_param):
    """
    Calculate probability distribution using Maxwell-Boltzmann
    P(x) ∝ exp(-λ·E(x)), where E(x) is the energy of constellation point
    """
    energy = calculate_energy(constellation)
    probs = np.exp(-lambda_param * energy)
    return probs / np.sum(probs)  # Normalization

# Continuous Maxwell-Boltzmann distribution
def continuous_maxwell_boltzmann(x, y, lambda_param):
    """
    Calculate continuous Maxwell-Boltzmann distribution
    P(x,y) ∝ exp(-λ(x²+y²))
    """
    energy = x**2 + y**2
    return np.exp(-lambda_param * energy)

# Generate symbols with probability shaping
def generate_shaped_symbols(constellation, probs, n_symbols):
    """Generate symbols with given probability distribution"""
    indices = np.random.choice(len(constellation), size=n_symbols, p=probs)
    return constellation[indices], indices

# AWGN channel
def awgn_channel(symbols, snr_db):
    """Transmit symbols through AWGN channel"""
    snr = 10**(snr_db/10)
    # Calculate average signal energy
    signal_power = np.mean(np.sum(np.abs(symbols)**2, axis=1))
    noise_power = signal_power / snr
    # Generate complex Gaussian noise
    noise = np.sqrt(noise_power/2) * (np.random.randn(len(symbols), 2))
    return symbols + noise

# ML detector
def ml_detector(received, constellation, probs=None):
    """
    Maximum likelihood detector
    If prior probabilities are provided, use MAP detection
    """
    distances = np.sum((received[:, np.newaxis, :] - constellation[np.newaxis, :, :])**2, axis=2)
    
    if probs is not None:
        # MAP detection: consider prior probabilities
        log_probs = np.log(probs)
        metrics = distances - log_probs  # Minimize -log(P(y|x)P(x))
    else:
        # ML detection: assume uniform prior
        metrics = distances
        
    indices = np.argmin(metrics, axis=1)
    return indices

# Calculate entropy of a probability distribution
def calculate_entropy(probs):
    """Calculate the entropy of a probability distribution in bits."""
    # Filter out zero probabilities to avoid log(0)
    non_zero_probs = probs[probs > 0]
    return -np.sum(non_zero_probs * np.log2(non_zero_probs))

# Calculate mutual information
def calculate_mutual_information(constellation, probs, snr_db):
    """
    Calculate mutual information given constellation, probability distribution, and SNR
    """
    snr = 10**(snr_db/10)
    noise_var = 1.0 / snr  # Normalized noise variance
    
    # Calculate average constellation energy
    energy = calculate_energy(constellation)
    avg_energy = np.sum(energy * probs)
    
    # Calculate entropy
    entropy = calculate_entropy(probs)
    # AWGN channel capacity upper bound
    capacity_bound = np.log2(1 + avg_energy / noise_var)
    
    return entropy, capacity_bound

# Optimize λ for given SNR
def optimize_lambda_for_snr(constellation, snr_db, method='capacity', lambda_range=(0.0, 3.0)):
    """
    Optimize λ for given SNR
    
    Parameters:
    - constellation: constellation points
    - snr_db: SNR (dB)
    - method: optimization method, 'capacity' for maximizing mutual information
    - lambda_range: λ search range
    
    Returns:
    - Optimal λ
    """
    # Calculate constellation energy
    energy = calculate_energy(constellation)
    
    def objective_function(lambda_param):
        """Define optimization objective function (negative mutual information, because we want to minimize)"""
        if lambda_param <= 0:
            # Prevent λ≤0 cases
            return 1e10
        
        # Calculate probability distribution
        probs = np.exp(-lambda_param * energy)
        probs = probs / np.sum(probs)
        
        # Calculate mutual information
        mi, capacity = calculate_mutual_information(constellation, probs, snr_db)
        
        # Return different optimization objectives based on selected method
        if method == 'capacity':
            # We want to maximize mutual information, but minimize_scalar does minimization, so take negative
            return -mi
        else:
            # If needed, other optimization objectives can be added
            return -mi
    
    # Use scipy minimization method to find optimal λ
    result = minimize_scalar(
        objective_function,
        bounds=lambda_range,
        method='bounded'
    )
    
    optimal_lambda = result.x
    return optimal_lambda

# 根据SNR计算目标功率的函数
def calculate_target_power_for_snr(constellation, snr_db, power_ratio=0.6):
    """
    根据SNR计算对应的目标平均功率
    
    参数:
    - constellation: 星座点
    - snr_db: 信噪比(dB)
    - power_ratio: 功率比例系数，控制相对于均匀分布的功率比例
    
    返回:
    - 目标平均功率值
    """
    # 均匀分布下的平均功率
    uniform_avg_power = np.mean(calculate_energy(constellation))
    
    # *** 平滑自适应整形策略 ***
    # 使用平滑的sigmoid函数计算功率比例
    # 在低SNR区域使用非常小的功率比例，在高SNR区域接近均匀分布
    
    # 基准点：0dB对应约0.1的比例
    mid_point = 0.0  # 0 dB
    steepness = 0.15  # 控制曲线陡峭程度
    
    # Sigmoid函数计算比例
    ratio = 0.1 / (1 + np.exp(-(snr_db - mid_point) / steepness))
    
    # 在低SNR区域额外缩小比例
    if snr_db < -10:
        ratio *= 0.05 * (1 + (snr_db + 10) / 10)  # -20dB时为原来的0.5%，-10dB时为5%
    elif snr_db < 0:
        ratio *= 0.2 + 0.8 * (snr_db + 10) / 10  # -10dB时为原来的20%，0dB时为100%
    
    # 在高SNR区域增加比例
    if snr_db > 10:
        # 从0.1逐渐增加到0.8
        base_ratio = 0.1
        max_ratio = 0.8
        ratio = base_ratio + (max_ratio - base_ratio) * min(1.0, (snr_db - 10) / 20)
    
    # 确保ratio在有效范围内
    ratio = min(0.9, max(0.001, ratio))
    
    return ratio * uniform_avg_power

# 直接根据SNR计算λ值的函数
def calculate_lambda_for_snr(snr_db):
    """
    直接根据SNR计算合适的λ值，确保平滑变化
    
    参数:
    - snr_db: 信噪比(dB)
    
    返回:
    - λ值
    """
    # 使用指数函数确保λ值随SNR平滑变化
    # 在低SNR区域使用较大的λ值，在高SNR区域使用较小的λ值
    
    if snr_db < -18:
        # 极限低SNR区域：λ从200到300+平滑变化，确保熵值降至2.5bits以下
        base = 200.0
        return base + 100.0 * np.exp(-(snr_db + 18) / 10)
    elif snr_db < -15:
        # 极低SNR区域：λ从150到200平滑变化
        return 200.0 - (200.0 - 150.0) * (snr_db + 18) / 3
    elif snr_db < -10:
        # 非常低SNR区域：λ从100到150平滑变化
        return 150.0 - (150.0 - 100.0) * (snr_db + 15) / 5
    elif snr_db < -5:
        # 低SNR区域：λ从50到100平滑变化
        return 100.0 - (100.0 - 50.0) * (snr_db + 10) / 5
    elif snr_db < 0:
        # 中低SNR区域：λ从20到50平滑变化
        return 50.0 - (50.0 - 20.0) * (snr_db + 5) / 5
    elif snr_db < 5:
        # 中SNR区域：λ从6到20平滑变化
        return 20.0 - (20.0 - 6.0) * snr_db / 5
    elif snr_db < 10:
        # 中高SNR区域：λ从2到6平滑变化
        return 6.0 - (6.0 - 2.0) * (snr_db - 5) / 5
    elif snr_db < 15:
        # 高SNR区域：λ从0.8到2平滑变化
        return 2.0 - (2.0 - 0.8) * (snr_db - 10) / 5
    else:
        # 极高SNR区域：λ接近0
        return max(0.01, 0.8 * np.exp(-(snr_db - 15) / 10))

# 根据目标功率约束求解λ的函数（与图中的数值搜索过程一致）
def find_lambda_for_target_power(constellation, target_power, lambda_range=(0.0, 20.0), tol=1e-6):
    """
    根据目标平均功率约束求解λ值
    
    参数:
    - constellation: 星座点
    - target_power: 目标平均功率
    - lambda_range: λ搜索范围
    - tol: 收敛容差
    
    返回:
    - 满足功率约束的λ值
    """
    # 计算星座点能量
    energy = calculate_energy(constellation)
    
    def power_difference(lambda_param):
        """计算当前λ下的平均功率与目标功率的差值"""
        # 当λ≤0时返回大值
        if lambda_param <= 0:
            return 1e10  
        
        # 计算概率分布
        probs = np.exp(-lambda_param * energy)
        probs = probs / np.sum(probs)
        
        # 计算平均功率
        avg_power = np.sum(energy * probs)
        
        # 返回差值
        return avg_power - target_power
    
    # 使用二分搜索方法（与图中提到的数值搜索过程一致）
    left, right = lambda_range
    iterations = 0
    max_iterations = 100
    
    # 确保搜索范围是有效的
    if power_difference(left) * power_difference(right) >= 0:
        # 如果范围端点的函数值同号，需要调整搜索范围
        if abs(power_difference(left)) < abs(power_difference(right)):
            return left
        else:
            # 对于非常低的目标功率，可能需要更大的λ值
            if target_power < 0.05 * np.mean(calculate_energy(constellation)):
                return right * 1.5  # 返回更大的λ值
            return right
    
    # 二分搜索
    while iterations < max_iterations and right - left > tol:
        mid = (left + right) / 2
        if power_difference(mid) * power_difference(left) < 0:
            right = mid
        else:
            left = mid
        iterations += 1
    
    optimal_lambda = (left + right) / 2
    
    # 如果熵仍然太高，进一步增加λ
    probs = maxwell_boltzmann_distribution(constellation, optimal_lambda)
    entropy = calculate_entropy(probs)
    
    # 根据SNR区域设定目标熵值
    # 从目标功率推断SNR区域
    uniform_avg_power = np.mean(calculate_energy(constellation))
    power_ratio = target_power / uniform_avg_power
    
    if power_ratio < 0.01:  # 极低SNR区域
        target_entropy = 2.0  # 接近QPSK (2 bits)
    elif power_ratio < 0.05:  # 低SNR区域
        target_entropy = 2.5  # 略高于QPSK
    elif power_ratio < 0.1:  # 中低SNR区域
        target_entropy = 3.0  # 介于QPSK和8-QAM之间
    else:
        target_entropy = 4.0  # 默认目标熵值
    
    # 如果熵大于目标熵且目标是低SNR区域，增加λ值
    if entropy > target_entropy and power_ratio < 0.2:
        # 逐步增加λ直到熵降至目标值以下或达到最大尝试次数
        max_tries = 10
        current_lambda = optimal_lambda
        for _ in range(max_tries):
            current_lambda *= 1.2  # 每次增加20%
            probs = maxwell_boltzmann_distribution(constellation, current_lambda)
            entropy = calculate_entropy(probs)
            if entropy <= target_entropy:
                return current_lambda
        
        # 如果经过多次尝试仍未达到目标，返回最后一个λ值
        return current_lambda
    
    return optimal_lambda

# Main function
def main():
    # Parameter settings
    M = 256  # 256-QAM
    n_symbols = 100000  # Number of symbols
    snr_db_range = np.arange(-20, 21, 2)  # SNR range
    
    # Create constellation points
    constellation = create_qam_constellation(M)
    
    # Uniform probability distribution
    uniform_probs = np.ones(M) / M
    
    # Calculate entropy for uniform distribution
    max_entropy_M_QAM = np.log2(M)
    uniform_entropy = calculate_entropy(uniform_probs)
    
    # Use different methods to solve λ
    # 1. Fixed λ method
    fixed_lambda = 0.7
    fixed_shaped_probs = maxwell_boltzmann_distribution(constellation, fixed_lambda)
    fixed_shaped_entropy = calculate_entropy(fixed_shaped_probs)
    
    # 2. Solve λ based on power constraint
    # 在中等SNR下的目标功率
    mid_snr = 10
    target_power = calculate_target_power_for_snr(constellation, mid_snr)
    power_lambda = find_lambda_for_target_power(constellation, target_power)
    power_shaped_probs = maxwell_boltzmann_distribution(constellation, power_lambda)
    power_shaped_entropy = calculate_entropy(power_shaped_probs)
    
    print(f"对于 {M}-QAM:")
    print(f"  理论最大每符号比特数 (log2(M)): {max_entropy_M_QAM:.4f} bits")
    print(f"  均匀分布下的熵 (等效于 λ=0): {uniform_entropy:.4f} bits")
    print(f"  固定λ={fixed_lambda}时的熵: {fixed_shaped_entropy:.4f} bits")
    print(f"  功率约束下λ={power_lambda:.4f}时的熵: {power_shaped_entropy:.4f} bits")
    
    # Performance comparison
    uniform_ber = []
    fixed_shaped_ber = []
    adaptive_shaped_ber = []
    snr_optimal_lambdas = []  # 存储每个SNR对应的最优λ值
    target_powers = []  # 存储每个SNR对应的目标功率
    adaptive_entropies = []  # 存储每个SNR对应的熵值
    
    for snr_db in snr_db_range:
        # 使用新的方法：直接根据SNR计算λ值，确保平滑变化
        optimal_lambda = calculate_lambda_for_snr(snr_db)
        snr_optimal_lambdas.append(optimal_lambda)
        
        # 使用最优λ计算概率分布
        adaptive_shaped_probs = maxwell_boltzmann_distribution(constellation, optimal_lambda)
        
        # 计算当前分布的熵和平均功率
        current_entropy = calculate_entropy(adaptive_shaped_probs)
        adaptive_entropies.append(current_entropy)
        
        # 计算平均功率
        energy = calculate_energy(constellation)
        current_power = np.sum(energy * adaptive_shaped_probs)
        target_powers.append(current_power)
        
        # Uniform probability distribution performance test
        uniform_symbols, uniform_indices = generate_shaped_symbols(constellation, uniform_probs, n_symbols)
        received_uniform = awgn_channel(uniform_symbols, snr_db)
        detected_uniform = ml_detector(received_uniform, constellation)
        uniform_ber.append(np.mean(detected_uniform != uniform_indices))
        
        # Fixed λ probability shaping performance test
        fixed_shaped_symbols, fixed_shaped_indices = generate_shaped_symbols(constellation, fixed_shaped_probs, n_symbols)
        received_fixed_shaped = awgn_channel(fixed_shaped_symbols, snr_db)
        detected_fixed_shaped = ml_detector(received_fixed_shaped, constellation, fixed_shaped_probs)
        fixed_shaped_ber.append(np.mean(detected_fixed_shaped != fixed_shaped_indices))
        
        # Adaptive λ probability shaping performance test
        adaptive_shaped_symbols, adaptive_shaped_indices = generate_shaped_symbols(constellation, adaptive_shaped_probs, n_symbols)
        received_adaptive_shaped = awgn_channel(adaptive_shaped_symbols, snr_db)
        detected_adaptive_shaped = ml_detector(received_adaptive_shaped, constellation, adaptive_shaped_probs)
        adaptive_shaped_ber.append(np.mean(detected_adaptive_shaped != adaptive_shaped_indices))
        
        # 输出当前SNR的λ值、目标功率和熵
        uniform_avg_power = np.mean(calculate_energy(constellation))
        print(f"SNR = {snr_db} dB, λ = {optimal_lambda:.4f}, 熵 = {current_entropy:.4f} bits, 功率比例 = {current_power/uniform_avg_power:.4f}")
    
    # Plot λ vs SNR relationship
    plt.figure(figsize=(10, 5))
    plt.subplot(121)
    plt.plot(snr_db_range, snr_optimal_lambdas, 'o-', linewidth=2)
    plt.xlabel('SNR (dB)')
    plt.ylabel('最优λ值')
    plt.title('SNR与最优λ的关系')
    plt.grid(True)
    
    # Plot target power vs SNR relationship
    plt.subplot(122)
    uniform_power = np.mean(calculate_energy(constellation))
    plt.plot(snr_db_range, [p/uniform_power for p in target_powers], 'o-', linewidth=2)
    plt.xlabel('SNR (dB)')
    plt.ylabel('平均功率比例')
    plt.title('SNR与平均功率比例的关系')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('lambda_and_power_vs_snr.png')
    plt.close()
    
    # Plot entropy vs SNR relationship
    plt.figure(figsize=(10, 5))
    plt.plot(snr_db_range, adaptive_entropies, 'o-', linewidth=2)
    plt.axhline(y=2.0, color='r', linestyle='--', label='QPSK (2 bits)')
    plt.axhline(y=4.0, color='g', linestyle='--', label='16-QAM (4 bits)')
    plt.axhline(y=6.0, color='b', linestyle='--', label='64-QAM (6 bits)')
    plt.axhline(y=8.0, color='m', linestyle='--', label='256-QAM (8 bits)')
    plt.xlabel('SNR (dB)')
    plt.ylabel('熵 (bits/符号)')
    plt.title('SNR与熵的关系')
    plt.legend()
    plt.grid(True)
    plt.savefig('entropy_vs_snr.png')
    plt.close()
    
    # Plot symbol error rate performance
    plt.figure()
    plt.semilogy(snr_db_range, uniform_ber, 'o-', linewidth=2, label='均匀分布')
    plt.semilogy(snr_db_range, fixed_shaped_ber, 's-', linewidth=2, label=f'固定λ={fixed_lambda}')
    plt.semilogy(snr_db_range, adaptive_shaped_ber, '^-', linewidth=2, label='自适应λ')
    plt.xlabel('SNR (dB)')
    plt.ylabel('符号错误率')
    plt.title('256-QAM 不同概率整形方法比较')
    plt.grid(True)
    plt.legend()
    plt.savefig('error_rate_comparison_adaptive.png')
    plt.close()
    
    # Constellation and probability distribution visualization
    # Select three different SNR values for comparison
    snr_indices = [0, len(snr_db_range)//2, -1]  # Low, medium, high SNR
    
    plt.figure(figsize=(15, 5))
    energy = calculate_energy(constellation)
    indices = np.argsort(energy)
    subset_size = 50  # Only show a subset for readability
    subset_indices = indices[:subset_size]
    
    for i, idx in enumerate(snr_indices):
        snr = snr_db_range[idx]
        lambda_val = snr_optimal_lambdas[idx]
        
        plt.subplot(1, 3, i+1)
        # Calculate probability distribution for current SNR
        shaped_probs = maxwell_boltzmann_distribution(constellation, lambda_val)
        
        plt.bar(np.arange(subset_size), uniform_probs[subset_indices], alpha=0.5, label='均匀分布')
        plt.bar(np.arange(subset_size), shaped_probs[subset_indices], alpha=0.5, label=f'λ={lambda_val:.2f}')
        plt.xlabel(f'前{subset_size}个点(按能量排序)')
        plt.ylabel('概率')
        plt.legend()
        plt.title(f'SNR = {snr} dB 时的概率分布')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('probability_distributions_by_snr.png')
    plt.close()
    
    # Calculate and plot average energy and entropy for each λ value
    lambdas = np.linspace(0, 5, 100)
    avg_energies = []
    entropies = []
    
    for lam in lambdas:
        if lam == 0:
            # 避免λ=0导致的均匀分布
            lam = 0.0001
        probs = maxwell_boltzmann_distribution(constellation, lam)
        avg_energy = np.sum(energy * probs)
        entropy = calculate_entropy(probs)
        avg_energies.append(avg_energy)
        entropies.append(entropy)
    
    plt.figure(figsize=(10, 5))
    plt.subplot(121)
    plt.plot(lambdas, avg_energies, '-', linewidth=2)
    plt.axhline(y=uniform_power, color='r', linestyle='--', label='均匀分布')
    plt.xlabel('λ值')
    plt.ylabel('平均能量')
    plt.title('λ与平均能量的关系')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(122)
    plt.plot(lambdas, entropies, '-', linewidth=2)
    plt.axhline(y=max_entropy_M_QAM, color='r', linestyle='--', label='最大熵')
    plt.axhline(y=4.0, color='g', linestyle='--', label='16-QAM (4 bits)')
    plt.axhline(y=2.0, color='b', linestyle='--', label='QPSK (2 bits)')
    plt.xlabel('λ值')
    plt.ylabel('熵 (bits)')
    plt.title('λ与熵的关系')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('lambda_vs_energy_and_entropy.png')
    plt.close()
    
    # 扩展λ范围，观察更大λ值下的熵变化
    extended_lambdas = np.linspace(0, 50, 200)
    extended_entropies = []
    
    for lam in extended_lambdas:
        if lam == 0:
            # 避免λ=0导致的均匀分布
            lam = 0.0001
        probs = maxwell_boltzmann_distribution(constellation, lam)
        entropy = calculate_entropy(probs)
        extended_entropies.append(entropy)
    
    plt.figure(figsize=(10, 5))
    plt.plot(extended_lambdas, extended_entropies, '-', linewidth=2)
    plt.axhline(y=8.0, color='r', linestyle='--', label='256-QAM (8 bits)')
    plt.axhline(y=6.0, color='m', linestyle='--', label='64-QAM (6 bits)')
    plt.axhline(y=4.0, color='g', linestyle='--', label='16-QAM (4 bits)')
    plt.axhline(y=2.5, color='y', linestyle='--', label='≈ 6-QAM (2.5 bits)')
    plt.axhline(y=2.0, color='b', linestyle='--', label='QPSK (2 bits)')
    plt.axhline(y=1.0, color='k', linestyle='--', label='BPSK (1 bit)')
    plt.xlabel('λ值')
    plt.ylabel('熵 (bits)')
    plt.title('扩展λ范围下的熵变化')
    plt.legend()
    plt.grid(True)
    plt.savefig('extended_lambda_vs_entropy.png')
    plt.close()
    
    # 创建更详细的SNR-λ关系图
    detailed_snr = np.linspace(-20, 20, 100)
    detailed_lambda = [calculate_lambda_for_snr(snr) for snr in detailed_snr]
    
    plt.figure(figsize=(10, 5))
    plt.plot(detailed_snr, detailed_lambda, '-', linewidth=2)
    plt.plot(snr_db_range, snr_optimal_lambdas, 'ro', markersize=6)
    plt.xlabel('SNR (dB)')
    plt.ylabel('λ值')
    plt.title('SNR与λ值的平滑关系')
    plt.grid(True)
    plt.savefig('detailed_lambda_vs_snr.png')
    plt.close()
    
    print("自适应λ计算完成!")
    print(f"SNR范围: {snr_db_range[0]}-{snr_db_range[-1]} dB")
    print(f"对应的最优λ范围: {min(snr_optimal_lambdas):.4f}-{max(snr_optimal_lambdas):.4f}")
    print(f"对应的熵范围: {min(adaptive_entropies):.4f}-{max(adaptive_entropies):.4f} bits")
    
    # Verify λ values and resulting entropies for each SNR point
    print("\n验证λ值与熵的关系:")
    for i, snr_db in enumerate(snr_db_range):
        lambda_val = snr_optimal_lambdas[i]
        probs = maxwell_boltzmann_distribution(constellation, lambda_val)
        entropy = adaptive_entropies[i]
        avg_power = np.sum(energy * probs)
        print(f"SNR = {snr_db} dB, λ = {lambda_val:.4f}, 熵 = {entropy:.4f} bits, 平均功率 = {avg_power:.4f}, 功率比例 = {avg_power/uniform_power:.4f}")
    
    print("\n注意: 星座点已使用功率归一化处理，确保平均功率为1。")

def visualize_continuous_maxwell_boltzmann():
    """Visualize continuous Maxwell-Boltzmann distribution alongside discrete 256-QAM"""
    # Parameter settings
    M = 256  # 256-QAM
    lambda_values = [0.0, 0.3, 0.7, 1.0]  # Different lambda values
    
    # Create constellation points
    constellation = create_qam_constellation(M)
    
    # Calculate energy
    energy = calculate_energy(constellation)
    
    # Create continuous representation
    n = int(np.sqrt(M))
    # 使用归一化后的范围
    max_value = np.max(np.abs(constellation))
    resolution = 200
    x_range = np.linspace(-max_value, max_value, resolution)
    y_range = np.linspace(-max_value, max_value, resolution)
    X, Y = np.meshgrid(x_range, y_range)
    
    # Create figure with subplots for each lambda value
    fig, axes = plt.subplots(2, 2, figsize=(16, 14))
    axes = axes.flatten()
    
    for i, lambda_param in enumerate(lambda_values):
        ax = axes[i]
        
        if lambda_param == 0.0:
            # Uniform distribution for constellation points
            probs = np.ones(M) / M
            title = "Uniform Distribution (λ=0)"
            # For continuous, use very small lambda to avoid division by zero
            Z = continuous_maxwell_boltzmann(X, Y, 0.001)
        else:
            # Maxwell-Boltzmann distribution for constellation points
            probs = np.exp(-lambda_param * energy)
            probs = probs / np.sum(probs)
            title = f"Probability Shaping (λ={lambda_param})"
            # Continuous distribution
            Z = continuous_maxwell_boltzmann(X, Y, lambda_param)
        
        # Normalize Z for visualization
        Z = Z / Z.sum()
        
        # Create contour plot of continuous distribution
        contour = ax.contourf(X, Y, Z, 50, cmap='viridis', alpha=0.6)
        
        # Add discrete constellation points
        sizes = probs * 3000  # Scale for visibility
        scatter = ax.scatter(constellation[:, 0], constellation[:, 1], 
                           c=probs, s=sizes, cmap='hot', alpha=0.8, 
                           edgecolors='white', linewidth=0.2)
        
        # Add colorbar
        plt.colorbar(contour, ax=ax, label='Continuous probability density')
        
        ax.set_title(title)
        ax.set_xlabel('In-phase')
        ax.set_ylabel('Quadrature')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.set_aspect('equal')
    
    plt.tight_layout()
    plt.savefig('continuous_vs_discrete_distribution.png')
    plt.close()
    
    # 3D visualization of continuous distribution
    fig = plt.figure(figsize=(16, 14))
    
    for i, lambda_param in enumerate(lambda_values):
        ax = fig.add_subplot(2, 2, i+1, projection='3d')
        
        if lambda_param == 0.0:
            # Use very small lambda to avoid uniform distribution
            # which would be a flat plane and not informative
            Z = continuous_maxwell_boltzmann(X, Y, 0.001)
            title = "Uniform Distribution (λ=0)"
        else:
            Z = continuous_maxwell_boltzmann(X, Y, lambda_param)
            title = f"Continuous Maxwell-Boltzmann (λ={lambda_param})"
        
        # Normalize Z for visualization
        Z = Z / Z.sum() * 1000  # Scale factor for better visualization
        
        # Create 3D surface
        surf = ax.plot_surface(X, Y, Z, cmap='viridis', edgecolor='none', alpha=0.8)
        
        # Add discrete constellation points at the base
        ax.scatter(constellation[:, 0], constellation[:, 1], np.zeros_like(constellation[:, 0]),
                  c='red', s=10, alpha=0.3)
        
        ax.set_title(title)
        ax.set_xlabel('In-phase')
        ax.set_ylabel('Quadrature')
        ax.set_zlabel('Probability Density')
        
        # Set the same scale for better comparison
        if i > 0:
            ax.set_zlim(0, np.max(Z) * 1.1)
        
        # Adjust viewing angle
        ax.view_init(elev=25, azim=45)
    
    plt.tight_layout()
    plt.savefig('3d_continuous_distribution.png')
    plt.close()
    
    # Cross-section view of continuous distribution
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Take cross-section at y=0
    y_idx = 200 // 2  # Center index
    
    for lambda_param in lambda_values:
        if lambda_param == 0.0:
            # Use very small lambda to avoid uniform distribution
            Z = continuous_maxwell_boltzmann(X, Y, 0.001)
            label = "Uniform (λ=0)"
        else:
            Z = continuous_maxwell_boltzmann(X, Y, lambda_param)
            label = f"λ={lambda_param}"
        
        # Normalize Z
        Z = Z / Z.sum()
        
        # Plot cross-section
        ax.plot(x_range, Z[y_idx, :], label=label, linewidth=2)
    
    ax.set_xlabel('In-phase component')
    ax.set_ylabel('Probability Density')
    ax.set_title('Cross-section of Continuous Maxwell-Boltzmann Distribution (y=0)')
    ax.grid(True)
    ax.legend()
    
    plt.savefig('cross_section_distribution.png')
    plt.close()

    print("Information about Maxwell-Boltzmann Distribution in 256-QAM:")
    print("1. Continuous vs. Discrete:")
    print("   - Discrete: Assigns probability to specific constellation points")
    print("   - Continuous: Defines probability density over entire complex plane")
    print("2. Mathematical form:")
    print("   - Discrete: P(x) ∝ exp(-λ·E(x)) where E(x) is energy of point x")
    print("   - Continuous: p(x,y) ∝ exp(-λ(x²+y²)) for any point (x,y) in I-Q plane")
    print("3. Key properties:")
    print("   - Approximates Gaussian distribution (optimal for capacity)")
    print("   - Concentrates probability near origin (low-energy points)")
    print("   - λ controls shaping strength (higher λ = more concentration)")
    print("4. 256-QAM vs. 64-QAM:")
    print("   - 256-QAM benefits more from shaping (typical gain: 0.8-1.2 dB)")
    print("   - Higher λ values (0.7-1.0) typically used for 256-QAM")
    print("   - More concentrated distribution due to wider dynamic range of point energies")

if __name__ == "__main__":
    main()
    visualize_continuous_maxwell_boltzmann()
    print("所有图像已保存为当前目录中的PNG文件。")