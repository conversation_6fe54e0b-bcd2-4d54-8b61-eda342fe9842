{"WebP+LDPC+16QAM": {"method": "WebP-80+LDPC+16QAM", "psnr": 37.7340647859075, "compress_time": 0.1111149787902832, "decompress_time": 0.02295398712158203, "ldpc_qam_time": 16.877659797668457, "total_time": 16.876677989959717, "compressed_size": 32992, "transmission_size": 66000, "quality": 80, "snr_db": 20.0, "compression_ratio": 17.873454545454546, "compression_rate": 94.40511067708334, "bpp": 1.3427734375, "original_size_bytes": 1179648, "compressed_size_bytes": 66000, "space_saved_bytes": 1113648, "original_bits": 263936, "encoded_bits": 528000, "symbols": 132000, "bit_errors": 0, "ber": 0.0, "encode_time": 2.9694955348968506, "modulate_time": 1.3613805770874023, "channel_time": 0.03275489807128906, "demodulate_time": 7.243683099746704, "decode_time": 5.269363880157471, "code_rate": 0.5}, "JPEG-50+LDPC+16QAM": {"method": "JPEG-50+LDPC+16QAM", "psnr": 34.52806563301249, "compress_time": 0.005568265914916992, "decompress_time": 0.007462024688720703, "ldpc_qam_time": 1.6116704940795898, "total_time": 1.6104967594146729, "jpeg_size": 28920, "transmission_size": 58000, "jpeg_quality": 50, "snr_db": 20.0, "compression_ratio": 20.338758620689656, "compression_rate": 95.08327907986111, "bpp": 1.1800130208333333, "original_size_bytes": 1179648, "compressed_size_bytes": 58000, "space_saved_bytes": 1121648, "original_bits": 231360, "encoded_bits": 464000, "symbols": 116000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.014665842056274414, "modulate_time": 0.013291597366333008, "channel_time": 0.026694059371948242, "demodulate_time": 0.020765066146850586, "decode_time": 1.5350801944732666, "code_rate": 0.5}, "JPEG-75+LDPC+16QAM": {"method": "JPEG-75+LDPC+16QAM", "psnr": 36.70149534784471, "compress_time": 0.0158231258392334, "decompress_time": 0.009020328521728516, "ldpc_qam_time": 1.818042516708374, "total_time": 1.8164193630218506, "jpeg_size": 45639, "transmission_size": 91500, "jpeg_quality": 75, "snr_db": 20.0, "compression_ratio": 12.892327868852458, "compression_rate": 92.24344889322916, "bpp": 1.861572265625, "original_size_bytes": 1179648, "compressed_size_bytes": 91500, "space_saved_bytes": 1088148, "original_bits": 365112, "encoded_bits": 732000, "symbols": 183000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.04809212684631348, "modulate_time": 0.02316904067993164, "channel_time": 0.05070662498474121, "demodulate_time": 0.024283647537231445, "decode_time": 1.6701679229736328, "code_rate": 0.5}, "JPEG-85+LDPC+16QAM": {"method": "JPEG-85+LDPC+16QAM", "psnr": 38.38487378462513, "compress_time": 0.01980757713317871, "decompress_time": 0.013315916061401367, "ldpc_qam_time": 1.7728030681610107, "total_time": 1.7669880390167236, "jpeg_size": 65004, "transmission_size": 130250, "jpeg_quality": 85, "snr_db": 20.0, "compression_ratio": 9.056798464491363, "compression_rate": 88.95857069227431, "bpp": 2.6499430338541665, "original_size_bytes": 1179648, "compressed_size_bytes": 130250, "space_saved_bytes": 1049398, "original_bits": 520032, "encoded_bits": 1042000, "symbols": 260500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.04926156997680664, "modulate_time": 0.02593374252319336, "channel_time": 0.06544971466064453, "demodulate_time": 0.018987417221069336, "decode_time": 1.6073555946350098, "code_rate": 0.5}, "BPG-20+LDPC+16QAM": {"method": "BPG-20+LDPC+16QAM", "psnr": 41.711893113334924, "compress_time": 1.6591076850891113, "decompress_time": 0.847261905670166, "ldpc_qam_time": 1.8026225566864014, "total_time": 1.8005378246307373, "compressed_size": 64346, "transmission_size": 128750, "quality": 20, "snr_db": 20.0, "compression_ratio": 9.162314563106795, "compression_rate": 89.08572726779514, "bpp": 2.6194254557291665, "original_size_bytes": 1179648, "compressed_size_bytes": 128750, "space_saved_bytes": 1050898, "original_bits": 514768, "encoded_bits": 1030000, "symbols": 257500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.036249399185180664, "modulate_time": 0.018023967742919922, "channel_time": 0.05893087387084961, "demodulate_time": 0.023195505142211914, "decode_time": 1.6641380786895752, "code_rate": 0.5}, "BPG-30+LDPC+16QAM": {"method": "BPG-30+LDPC+16QAM", "psnr": 37.049052677577876, "compress_time": 1.178663730621338, "decompress_time": 0.5501086711883545, "ldpc_qam_time": 1.7732369899749756, "total_time": 1.7726125717163086, "compressed_size": 20097, "transmission_size": 40250, "quality": 30, "snr_db": 20.0, "compression_ratio": 29.308024844720496, "compression_rate": 96.58796522352431, "bpp": 0.8188883463541666, "original_size_bytes": 1179648, "compressed_size_bytes": 40250, "space_saved_bytes": 1139398, "original_bits": 160776, "encoded_bits": 322000, "symbols": 80500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.025571107864379883, "modulate_time": 0.01434183120727539, "channel_time": 0.019828319549560547, "demodulate_time": 0.015726804733276367, "decode_time": 1.6971445083618164, "code_rate": 0.5}, "BPG-40+LDPC+16QAM": {"method": "BPG-40+LDPC+16QAM", "psnr": 31.746750261977894, "compress_time": 0.7750449180603027, "decompress_time": 0.46177124977111816, "ldpc_qam_time": 1.6996335983276367, "total_time": 1.6992170810699463, "compressed_size": 6088, "transmission_size": 12250, "quality": 40, "snr_db": 20.0, "compression_ratio": 96.29779591836734, "compression_rate": 98.96155463324654, "bpp": 0.24922688802083334, "original_size_bytes": 1179648, "compressed_size_bytes": 12250, "space_saved_bytes": 1167398, "original_bits": 48704, "encoded_bits": 98000, "symbols": 24500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.024386882781982422, "modulate_time": 0.01602935791015625, "channel_time": 0.006363630294799805, "demodulate_time": 0.010975837707519531, "decode_time": 1.6414613723754883, "code_rate": 0.5}, "AVIF-20+LDPC+16QAM": {"method": "AVIF-20+LDPC+16QAM", "psnr": 30.439659978024135, "compress_time": 0.5088865756988525, "decompress_time": 0.02849745750427246, "ldpc_qam_time": 1.7014992237091064, "total_time": 1.7011241912841797, "compressed_size": 5126, "transmission_size": 10500, "quality": 20, "format_used": "AVIF", "snr_db": 20.0, "compression_ratio": 112.34742857142857, "compression_rate": 99.10990397135416, "bpp": 0.213623046875, "original_size_bytes": 1179648, "compressed_size_bytes": 10500, "space_saved_bytes": 1169148, "original_bits": 41008, "encoded_bits": 84000, "symbols": 21000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.01947331428527832, "modulate_time": 0.012380361557006836, "channel_time": 0.005144357681274414, "demodulate_time": 0.010123491287231445, "decode_time": 1.6540026664733887, "code_rate": 0.5}, "AVIF-30+LDPC+16QAM": {"method": "AVIF-30+LDPC+16QAM", "psnr": 32.24851789799001, "compress_time": 0.5051124095916748, "decompress_time": 0.030333280563354492, "ldpc_qam_time": 1.6270701885223389, "total_time": 1.6265795230865479, "compressed_size": 7852, "transmission_size": 15750, "quality": 30, "format_used": "AVIF", "snr_db": 20.0, "compression_ratio": 74.89828571428572, "compression_rate": 98.66485595703125, "bpp": 0.3204345703125, "original_size_bytes": 1179648, "compressed_size_bytes": 15750, "space_saved_bytes": 1163898, "original_bits": 62816, "encoded_bits": 126000, "symbols": 31500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.021408796310424805, "modulate_time": 0.013393402099609375, "channel_time": 0.007858037948608398, "demodulate_time": 0.01099538803100586, "decode_time": 1.5729238986968994, "code_rate": 0.5}, "AVIF-40+LDPC+16QAM": {"method": "AVIF-40+LDPC+16QAM", "psnr": 34.11285744767588, "compress_time": 0.6667044162750244, "decompress_time": 0.024171113967895508, "ldpc_qam_time": 1.7147445678710938, "total_time": 1.7141432762145996, "compressed_size": 11729, "transmission_size": 23500, "quality": 40, "format_used": "AVIF", "snr_db": 20.0, "compression_ratio": 50.19778723404255, "compression_rate": 98.00788031684029, "bpp": 0.4781087239583333, "original_size_bytes": 1179648, "compressed_size_bytes": 23500, "space_saved_bytes": 1156148, "original_bits": 93832, "encoded_bits": 188000, "symbols": 47000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.021584033966064453, "modulate_time": 0.014597892761230469, "channel_time": 0.011485815048217773, "demodulate_time": 0.012079477310180664, "decode_time": 1.6543960571289062, "code_rate": 0.5}, "VAE+QAM": {"method": "VAE+QAM", "psnr": 22.767333844211585, "compress_time": 2.894664764404297, "decompress_time": 1.0455894470214844, "total_time": 3.9402542114257812, "snr_db": 20.0, "compression_ratio": 9.04860088365243, "bpp": 2.65234375, "compression_rate": 88.94856770833334, "total_symbols": 226560, "total_bits": 1042944.0, "transmission_size": 130368.0}}