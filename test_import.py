#!/usr/bin/env python3
"""测试模块导入是否成功"""

import sys
import os

# 添加项目路径到Python路径
project_root = os.path.join(os.path.dirname(__file__), 'lossy-vae-main')
sys.path.insert(0, project_root)

def test_channel_import():
    """测试Channel类的导入"""
    try:
        # 测试直接导入
        from lvae.models.qrvesvae_sc.channel import Channel
        print("✅ 直接导入Channel类成功")
        
        # 测试创建实例
        channel = Channel(snr=20.0, verbose=False)
        print("✅ 创建Channel实例成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入Channel类失败: {e}")
        return False

def test_model_import():
    """测试model.py中的导入"""
    try:
        # 测试导入model模块
        from lvae.models.qrvesvae_sc import model
        print("✅ 导入model模块成功")
        
        # 测试get_channel_class函数
        get_channel_class = getattr(model, 'get_channel_class', None)
        if get_channel_class:
            Channel = get_channel_class()
            print("✅ get_channel_class函数工作正常")
            
            # 测试创建实例
            channel = Channel(snr=20.0, verbose=False)
            print("✅ 通过get_channel_class创建Channel实例成功")
        else:
            print("❌ get_channel_class函数不存在")
            
        return True
    except Exception as e:
        print(f"❌ 导入model模块失败: {e}")
        return False

def test_relative_import():
    """测试相对导入"""
    try:
        # 切换到正确的目录
        original_cwd = os.getcwd()
        os.chdir(os.path.join(project_root, 'lvae', 'models', 'qrvesvae_sc'))
        
        # 在Python中执行相对导入测试
        import importlib.util
        
        # 加载channel模块
        channel_spec = importlib.util.spec_from_file_location("channel", "channel.py")
        channel_module = importlib.util.module_from_spec(channel_spec)
        channel_spec.loader.exec_module(channel_module)
        
        Channel = channel_module.Channel
        channel = Channel(snr=20.0, verbose=False)
        print("✅ 相对导入测试成功")
        
        # 恢复原始目录
        os.chdir(original_cwd)
        return True
    except Exception as e:
        print(f"❌ 相对导入测试失败: {e}")
        # 恢复原始目录
        os.chdir(original_cwd)
        return False

if __name__ == "__main__":
    print("🔍 开始测试模块导入...")
    print(f"项目根目录: {project_root}")
    print(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径
    
    success_count = 0
    total_tests = 3
    
    print("\n1. 测试直接导入Channel类:")
    if test_channel_import():
        success_count += 1
    
    print("\n2. 测试model模块导入:")
    if test_model_import():
        success_count += 1
    
    print("\n3. 测试相对导入:")
    if test_relative_import():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有导入测试都成功！模块导入没有问题。")
    else:
        print("⚠️  部分导入测试失败，可能存在导入问题。")
