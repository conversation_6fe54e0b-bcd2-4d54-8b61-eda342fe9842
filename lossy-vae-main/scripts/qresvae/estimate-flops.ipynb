{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'l<PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtorch\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# torch.set_grad_enabled(False)\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlvae\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_model\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'lvae'"]}], "source": ["import sys\n", "from pathlib import Path # 确保 Path 也被导入了\n", "import os\n", "# 下面的代码会把项目根目录 (lossy-vae-main) 添加到 Python 的搜索路径中\n", "# os.getcwd() 在 notebook 中运行时，通常是 notebook 文件所在的目录\n", "notebook_dir = Path(os.getcwd()).resolve()\n", "project_root = notebook_dir.parent.parent # 从 scripts/qresvae 退两级到 lossy-vae-main\n", "sys.path.insert(0, str(project_root))\n", "\n", "print(os.getcwd())\n", "import torch\n", "# torch.set_grad_enabled(False)\n", "\n", "from lvae import get_model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Initialize model.\n", "\n", "For FLOPs computation, $\\lambda$ is arbitrary since it does not affect FLOPs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# initialize model\n", "model = get_model('qres34m', lmb=2048, pretrained=True)\n", "\n", "model.eval()\n", "model._flops_mode = True\n", "\n", "input_shape = (3, 256, 256)\n", "inputs = (torch.randn(1, *input_shape), )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pytorch profiler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> estimated FLOPs (MACs) = 23.2B, parameters = 34.037M\n"]}], "source": ["import torch.profiler as tp\n", "\n", "with tp.profile(activities=[tp.ProfilerActivity.CPU], with_flops=True) as prof:\n", "    model(*inputs)\n", "torch_flops = sum([event.flops for event in prof.events()]) / 2\n", "torch_param = sum([p.numel() for p in model.parameters()])\n", "\n", "print(f'torch estimated FLOPs (MACs) = {torch_flops/1e9:.3g}B, parameters = {torch_param/1e6:.3f}M')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["THOP: PyTorch-OpCounter\n", "\n", "https://github.com/Lyken17/pytorch-OpCounter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["thop estimated FLOPs (MACs) = 23.2B, parameters = 33.984M\n"]}], "source": ["from thop import profile, clever_format\n", "thop_macs, thop_params = profile(model, inputs=inputs, verbose=False)\n", "\n", "print(f'thop estimated FLOPs (MACs) = {thop_macs/1e9:.3g}B, parameters = {thop_params/1e6:.3f}M')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["flops-counter.pytorch\n", "\n", "https://github.com/sovrasov/flops-counter.pytorch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HierarchicalVAE(\n", "  33.98 M, 99.845% Params, 23.25 GMac, 100.000% MACs, \n", "  (encoder): BottomUpEncoder(\n", "    15.84 M, 46.535% Params, 10.69 GMac, 45.997% MACs, \n", "    (enc_blocks): ModuleList(\n", "      15.84 M, 46.535% Params, 10.69 GMac, 45.997% MACs, \n", "      (0): Conv2d(9.41 k, 0.028% Params, 38.54 MMac, 0.166% MACs, 3, 192, kernel_size=(4, 4), stride=(4, 4))\n", "      (1): MyConvNeXtBlock(\n", "        157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (2): MyConvNeXtBlock(\n", "        157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (3): MyConvNeXtBlock(\n", "        157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (4): MyConvNeXtBlock(\n", "        157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (5): MyConvNeXtBlock(\n", "        157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (6): MyConvNeXtBlock(\n", "        157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (7): MyConvNeXtPatchDown(\n", "        452.93 k, 1.331% Params, 945.69 MMac, 4.067% MACs, \n", "        (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        (downsapmle): Conv2d(295.3 k, 0.868% Params, 302.38 MMac, 1.300% MACs, 192, 384, kernel_size=(2, 2), stride=(2, 2))\n", "      )\n", "      (8): MyConvNeXtBlock(\n", "        610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (9): MyConvNeXtBlock(\n", "        610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (10): MyConvNeXtBlock(\n", "        610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (11): MyConvNeXtBlock(\n", "        610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (12): MyConvNeXtBlock(\n", "        610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (13): MyConvNeXtBlock(\n", "        610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (14): MyConvNeXtPatchDown(\n", "        1.2 M, 3.527% Params, 774.73 MMac, 3.332% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        (downsapmle): Conv2d(590.21 k, 1.734% Params, 151.09 MMac, 0.650% MACs, 384, 384, kernel_size=(2, 2), stride=(2, 2))\n", "      )\n", "      (15): MyConvNeXtBlock(\n", "        600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "        (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (16): MyConvNeXtBlock(\n", "        600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "        (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (17): MyConvNeXtBlock(\n", "        600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "        (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (18): MyConvNeXtBlock(\n", "        600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "        (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (19): MyConvNeXtBlock(\n", "        600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "        (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (20): MyConvNeXtBlock(\n", "        600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "        (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (21): MyConvNeXtPatchDown(\n", "        1.2 M, 3.527% Params, 193.68 MMac, 0.833% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 4.92 MMac, 0.021% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        (downsapmle): Conv2d(590.21 k, 1.734% Params, 37.77 MMac, 0.162% MACs, 384, 384, kernel_size=(2, 2), stride=(2, 2))\n", "      )\n", "      (22): MyConvNeXtBlock(\n", "        594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "        (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (23): MyConvNeXtBlock(\n", "        594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "        (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (24): MyConvNeXtBlock(\n", "        594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "        (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (25): MyConvNeXtBlock(\n", "        594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "        (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (26): MyConvNeXtPatchDown(\n", "        1.2 M, 3.527% Params, 48.42 MMac, 0.208% MACs, \n", "        (conv_dw): Conv2d(19.2 k, 0.056% Params, 1.23 MMac, 0.005% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        (downsapmle): Conv2d(590.21 k, 1.734% Params, 9.44 MMac, 0.041% MACs, 384, 384, kernel_size=(2, 2), stride=(2, 2))\n", "      )\n", "      (27): MyConvNeXtBlock(\n", "        591.74 k, 1.739% Params, 9.45 MMac, 0.041% MACs, \n", "        (conv_dw): Conv2d(768, 0.002% Params, 12.29 KMac, 0.000% MACs, 384, 384, kernel_size=(1, 1), stride=(1, 1), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 9.44 MMac, 0.041% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 4.72 MMac, 0.020% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 4.72 MMac, 0.020% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "      (28): MyConvNeXtBlock(\n", "        591.74 k, 1.739% Params, 9.45 MMac, 0.041% MACs, \n", "        (conv_dw): Conv2d(768, 0.002% Params, 12.29 KMac, 0.000% MACs, 384, 384, kernel_size=(1, 1), stride=(1, 1), groups=384)\n", "        (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): Mlp(\n", "          590.98 k, 1.736% Params, 9.44 MMac, 0.041% MACs, \n", "          (fc1): Linear(295.68 k, 0.869% Params, 4.72 MMac, 0.020% MACs, in_features=384, out_features=768, bias=True)\n", "          (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          (fc2): Linear(295.3 k, 0.868% Params, 4.72 MMac, 0.020% MACs, in_features=768, out_features=384, bias=True)\n", "          (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "      )\n", "    )\n", "  )\n", "  (decoder): TopDownDecoder(\n", "    18.14 M, 53.310% Params, 12.56 GMac, 54.003% MACs, \n", "    (dec_blocks): ModuleList(\n", "      18.14 M, 53.310% Params, 12.56 GMac, 54.003% MACs, \n", "      (0): QLatentBlockX(\n", "        1.36 M, 3.983% Params, 21.66 MMac, 0.093% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          591.74 k, 1.739% Params, 9.45 MMac, 0.041% MACs, \n", "          (conv_dw): Conv2d(768, 0.002% Params, 12.29 KMac, 0.000% MACs, 384, 384, kernel_size=(1, 1), stride=(1, 1), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 9.44 MMac, 0.041% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 4.72 MMac, 0.020% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 4.72 MMac, 0.020% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          591.74 k, 1.739% Params, 9.45 MMac, 0.041% MACs, \n", "          (conv_dw): Conv2d(768, 0.002% Params, 12.29 KMac, 0.000% MACs, 384, 384, kernel_size=(1, 1), stride=(1, 1), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 9.44 MMac, 0.041% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 4.72 MMac, 0.020% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 4.72 MMac, 0.020% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          94.0 k, 0.276% Params, 1.5 MMac, 0.006% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 1.18 MMac, 0.005% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(9.31 k, 0.027% Params, 148.99 KMac, 0.001% MACs, 96, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c3): Conv2d(9.31 k, 0.027% Params, 148.99 KMac, 0.001% MACs, 96, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c4): Conv2d(1.55 k, 0.005% Params, 24.83 KMac, 0.000% MACs, 96, 16, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          58.69 k, 0.172% Params, 939.01 KMac, 0.004% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 591.36 KMac, 0.003% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(9.31 k, 0.027% Params, 148.99 KMac, 0.001% MACs, 96, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c3): Conv2d(9.31 k, 0.027% Params, 148.99 KMac, 0.001% MACs, 96, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c4): Conv2d(3.1 k, 0.009% Params, 49.66 KMac, 0.000% MACs, 96, 32, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          19.63 k, 0.058% Params, 314.88 KMac, 0.001% MACs, \n", "          (0): Conv2d(816, 0.002% Params, 13.06 KMac, 0.000% MACs, 16, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 768.0 Mac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 301.06 KMac, 0.001% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (1): Sequential(\n", "        591.36 k, 1.737% Params, 9.46 MMac, 0.041% MACs, \n", "        (0): Conv2d(591.36 k, 1.737% Params, 9.46 MMac, 0.041% MACs, 384, 1536, kernel_size=(1, 1), stride=(1, 1))\n", "        (1): PixelShuffle(0, 0.000% Params, 0.0 Mac, 0.000% MACs, upscale_factor=2)\n", "      )\n", "      (2): QLatentBlockX(\n", "        1.66 M, 4.882% Params, 106.2 MMac, 0.457% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "          (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "          (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          241.26 k, 0.709% Params, 15.44 MMac, 0.066% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 4.72 MMac, 0.020% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.36 k, 0.004% Params, 86.91 KMac, 0.000% MACs, 96, 14, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          205.76 k, 0.605% Params, 13.17 MMac, 0.057% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 2.37 MMac, 0.010% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(2.72 k, 0.008% Params, 173.82 KMac, 0.001% MACs, 96, 28, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          24.91 k, 0.073% Params, 1.6 MMac, 0.007% MACs, \n", "          (0): Conv2d(6.1 k, 0.018% Params, 390.14 KMac, 0.002% MACs, 14, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 3.07 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 1.2 MMac, 0.005% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (3): QLatentBlockX(\n", "        1.66 M, 4.882% Params, 106.2 MMac, 0.457% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "          (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          594.82 k, 1.748% Params, 38.0 MMac, 0.163% MACs, \n", "          (conv_dw): Conv2d(3.84 k, 0.011% Params, 245.76 KMac, 0.001% MACs, 384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 37.75 MMac, 0.162% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 18.88 MMac, 0.081% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 18.87 MMac, 0.081% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          241.26 k, 0.709% Params, 15.44 MMac, 0.066% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 4.72 MMac, 0.020% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.36 k, 0.004% Params, 86.91 KMac, 0.000% MACs, 96, 14, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          205.76 k, 0.605% Params, 13.17 MMac, 0.057% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 2.37 MMac, 0.010% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 5.31 MMac, 0.023% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(2.72 k, 0.008% Params, 173.82 KMac, 0.001% MACs, 96, 28, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          24.91 k, 0.073% Params, 1.6 MMac, 0.007% MACs, \n", "          (0): Conv2d(6.1 k, 0.018% Params, 390.14 KMac, 0.002% MACs, 14, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 3.07 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 1.2 MMac, 0.005% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (4): Sequential(\n", "        591.36 k, 1.737% Params, 37.85 MMac, 0.163% MACs, \n", "        (0): Conv2d(591.36 k, 1.737% Params, 37.85 MMac, 0.163% MACs, 384, 1536, kernel_size=(1, 1), stride=(1, 1))\n", "        (1): PixelShuffle(0, 0.000% Params, 0.0 Mac, 0.000% MACs, upscale_factor=2)\n", "      )\n", "      (5): QLatentBlockX(\n", "        1.67 M, 4.914% Params, 427.56 MMac, 1.839% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "          (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "          (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          241.07 k, 0.708% Params, 61.71 MMac, 0.265% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 18.9 MMac, 0.081% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.16 k, 0.003% Params, 297.98 KMac, 0.001% MACs, 96, 12, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          205.37 k, 0.603% Params, 52.57 MMac, 0.226% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 9.46 MMac, 0.041% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(2.33 k, 0.007% Params, 595.97 KMac, 0.003% MACs, 96, 24, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          24.05 k, 0.071% Params, 6.17 MMac, 0.027% MACs, \n", "          (0): Conv2d(5.23 k, 0.015% Params, 1.34 MMac, 0.006% MACs, 12, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 12.29 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 4.82 MMac, 0.021% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (6): QLatentBlockX(\n", "        1.67 M, 4.914% Params, 427.56 MMac, 1.839% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "          (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "          (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          241.07 k, 0.708% Params, 61.71 MMac, 0.265% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 18.9 MMac, 0.081% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.16 k, 0.003% Params, 297.98 KMac, 0.001% MACs, 96, 12, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          205.37 k, 0.603% Params, 52.57 MMac, 0.226% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 9.46 MMac, 0.041% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(2.33 k, 0.007% Params, 595.97 KMac, 0.003% MACs, 96, 24, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          24.05 k, 0.071% Params, 6.17 MMac, 0.027% MACs, \n", "          (0): Conv2d(5.23 k, 0.015% Params, 1.34 MMac, 0.006% MACs, 12, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 12.29 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 4.82 MMac, 0.021% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (7): QLatentBlockX(\n", "        1.67 M, 4.914% Params, 427.56 MMac, 1.839% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "          (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          600.96 k, 1.766% Params, 153.55 MMac, 0.660% MACs, \n", "          (conv_dw): Conv2d(9.98 k, 0.029% Params, 2.56 MMac, 0.011% MACs, 384, 384, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 151.0 MMac, 0.649% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 75.5 MMac, 0.325% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 75.5 MMac, 0.325% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          241.07 k, 0.708% Params, 61.71 MMac, 0.265% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 18.9 MMac, 0.081% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.16 k, 0.003% Params, 297.98 KMac, 0.001% MACs, 96, 12, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          205.37 k, 0.603% Params, 52.57 MMac, 0.226% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 9.46 MMac, 0.041% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 21.26 MMac, 0.091% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(2.33 k, 0.007% Params, 595.97 KMac, 0.003% MACs, 96, 24, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          24.05 k, 0.071% Params, 6.17 MMac, 0.027% MACs, \n", "          (0): Conv2d(5.23 k, 0.015% Params, 1.34 MMac, 0.006% MACs, 12, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 12.29 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 4.82 MMac, 0.021% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (8): Sequential(\n", "        591.36 k, 1.737% Params, 151.39 MMac, 0.651% MACs, \n", "        (0): Conv2d(591.36 k, 1.737% Params, 151.39 MMac, 0.651% MACs, 384, 1536, kernel_size=(1, 1), stride=(1, 1))\n", "        (1): PixelShuffle(0, 0.000% Params, 0.0 Mac, 0.000% MACs, upscale_factor=2)\n", "      )\n", "      (9): QLatentBlockX(\n", "        1.69 M, 4.963% Params, 1.73 GMac, 7.430% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "          (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "          (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          240.87 k, 0.708% Params, 246.65 MMac, 1.061% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 75.6 MMac, 0.325% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(970, 0.003% Params, 993.28 KMac, 0.004% MACs, 96, 10, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          204.98 k, 0.602% Params, 209.9 MMac, 0.903% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 37.85 MMac, 0.163% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.94 k, 0.006% Params, 1.99 MMac, 0.009% MACs, 96, 20, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          23.18 k, 0.068% Params, 23.79 MMac, 0.102% MACs, \n", "          (0): Conv2d(4.37 k, 0.013% Params, 4.47 MMac, 0.019% MACs, 10, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 49.15 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 19.27 MMac, 0.083% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (10): QLatentBlockX(\n", "        1.69 M, 4.963% Params, 1.73 GMac, 7.430% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "          (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "          (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          240.87 k, 0.708% Params, 246.65 MMac, 1.061% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 75.6 MMac, 0.325% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(970, 0.003% Params, 993.28 KMac, 0.004% MACs, 96, 10, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          204.98 k, 0.602% Params, 209.9 MMac, 0.903% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 37.85 MMac, 0.163% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.94 k, 0.006% Params, 1.99 MMac, 0.009% MACs, 96, 20, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          23.18 k, 0.068% Params, 23.79 MMac, 0.102% MACs, \n", "          (0): Conv2d(4.37 k, 0.013% Params, 4.47 MMac, 0.019% MACs, 10, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 49.15 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 19.27 MMac, 0.083% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (11): QLatentBlockX(\n", "        1.69 M, 4.963% Params, 1.73 GMac, 7.430% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "          (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          610.18 k, 1.793% Params, 623.64 MMac, 2.682% MACs, \n", "          (conv_dw): Conv2d(19.2 k, 0.056% Params, 19.66 MMac, 0.085% MACs, 384, 384, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=384)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (384,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            590.98 k, 1.736% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(295.68 k, 0.869% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=768, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(295.3 k, 0.868% Params, 301.99 MMac, 1.299% MACs, in_features=768, out_features=384, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          240.87 k, 0.708% Params, 246.65 MMac, 1.061% MACs, \n", "          (c1): Conv2d(73.82 k, 0.217% Params, 75.6 MMac, 0.325% MACs, 768, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(970, 0.003% Params, 993.28 KMac, 0.004% MACs, 96, 10, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          204.98 k, 0.602% Params, 209.9 MMac, 0.903% MACs, \n", "          (c1): Conv2d(36.96 k, 0.109% Params, 37.85 MMac, 0.163% MACs, 384, 96, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(83.04 k, 0.244% Params, 85.03 MMac, 0.366% MACs, 96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(1.94 k, 0.006% Params, 1.99 MMac, 0.009% MACs, 96, 20, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          23.18 k, 0.068% Params, 23.79 MMac, 0.102% MACs, \n", "          (0): Conv2d(4.37 k, 0.013% Params, 4.47 MMac, 0.019% MACs, 10, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 49.15 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(18.82 k, 0.055% Params, 19.27 MMac, 0.083% MACs, 48, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (12): Sequential(\n", "        295.68 k, 0.869% Params, 302.78 MMac, 1.302% MACs, \n", "        (0): Conv2d(295.68 k, 0.869% Params, 302.78 MMac, 1.302% MACs, 384, 768, kernel_size=(1, 1), stride=(1, 1))\n", "        (1): PixelShuffle(0, 0.000% Params, 0.0 Mac, 0.000% MACs, upscale_factor=2)\n", "      )\n", "      (13): QLatentBlockX(\n", "        433.87 k, 1.275% Params, 1.77 GMac, 7.623% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "          (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "          (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          60.44 k, 0.178% Params, 247.56 MMac, 1.065% MACs, \n", "          (c1): Conv2d(18.48 k, 0.054% Params, 75.69 MMac, 0.326% MACs, 384, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(392, 0.001% Params, 1.61 MMac, 0.007% MACs, 48, 8, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          51.62 k, 0.152% Params, 211.42 MMac, 0.909% MACs, \n", "          (c1): Conv2d(9.26 k, 0.027% Params, 37.95 MMac, 0.163% MACs, 192, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(784, 0.002% Params, 3.21 MMac, 0.014% MACs, 48, 16, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          6.55 k, 0.019% Params, 26.94 MMac, 0.116% MACs, \n", "          (0): Conv2d(1.75 k, 0.005% Params, 7.18 MMac, 0.031% MACs, 8, 24, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 98.3 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(4.8 k, 0.014% Params, 19.66 MMac, 0.085% MACs, 24, 192, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (14): QLatentBlockX(\n", "        433.87 k, 1.275% Params, 1.77 GMac, 7.623% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "          (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "          (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          60.44 k, 0.178% Params, 247.56 MMac, 1.065% MACs, \n", "          (c1): Conv2d(18.48 k, 0.054% Params, 75.69 MMac, 0.326% MACs, 384, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(392, 0.001% Params, 1.61 MMac, 0.007% MACs, 48, 8, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          51.62 k, 0.152% Params, 211.42 MMac, 0.909% MACs, \n", "          (c1): Conv2d(9.26 k, 0.027% Params, 37.95 MMac, 0.163% MACs, 192, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(784, 0.002% Params, 3.21 MMac, 0.014% MACs, 48, 16, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          6.55 k, 0.019% Params, 26.94 MMac, 0.116% MACs, \n", "          (0): Conv2d(1.75 k, 0.005% Params, 7.18 MMac, 0.031% MACs, 8, 24, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 98.3 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(4.8 k, 0.014% Params, 19.66 MMac, 0.085% MACs, 24, 192, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (15): QLatentBlockX(\n", "        433.87 k, 1.275% Params, 1.77 GMac, 7.623% MACs, \n", "        (resnet_front): MyConvNeXtBlock(\n", "          157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "          (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (resnet_end): MyConvNeXtBlock(\n", "          157.63 k, 0.463% Params, 643.3 MMac, 2.767% MACs, \n", "          (conv_dw): Conv2d(9.6 k, 0.028% Params, 39.32 MMac, 0.169% MACs, 192, 192, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=192)\n", "          (norm): LayerNorm(0, 0.000% Params, 0.0 Mac, 0.000% MACs, (192,), eps=1e-06, elementwise_affine=True)\n", "          (mlp): Mlp(\n", "            148.03 k, 0.435% Params, 603.98 MMac, 2.598% MACs, \n", "            (fc1): Linear(74.11 k, 0.218% Params, 301.99 MMac, 1.299% MACs, in_features=192, out_features=384, bias=True)\n", "            (act): GELU(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "            (drop1): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "            (fc2): Linear(73.92 k, 0.217% Params, 301.99 MMac, 1.299% MACs, in_features=384, out_features=192, bias=True)\n", "            (drop2): Dropout(0, 0.000% Params, 0.0 Mac, 0.000% MACs, p=0.0, inplace=False)\n", "          )\n", "          (drop_path): Identity(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "        (posterior): <PERSON><PERSON><PERSON><PERSON>(\n", "          60.44 k, 0.178% Params, 247.56 MMac, 1.065% MACs, \n", "          (c1): Conv2d(18.48 k, 0.054% Params, 75.69 MMac, 0.326% MACs, 384, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(392, 0.001% Params, 1.61 MMac, 0.007% MACs, 48, 8, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (prior): V<PERSON><PERSON>lock(\n", "          51.62 k, 0.152% Params, 211.42 MMac, 0.909% MACs, \n", "          (c1): Conv2d(9.26 k, 0.027% Params, 37.95 MMac, 0.163% MACs, 192, 48, kernel_size=(1, 1), stride=(1, 1))\n", "          (c2): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c3): Conv2d(20.78 k, 0.061% Params, 85.13 MMac, 0.366% MACs, 48, 48, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (c4): Conv2d(784, 0.002% Params, 3.21 MMac, 0.014% MACs, 48, 16, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (z_proj): Sequential(\n", "          6.55 k, 0.019% Params, 26.94 MMac, 0.116% MACs, \n", "          (0): Conv2d(1.75 k, 0.005% Params, 7.18 MMac, 0.031% MACs, 8, 24, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (1): GELU(0, 0.000% Params, 98.3 KMac, 0.000% MACs, approximate='none')\n", "          (2): Conv2d(4.8 k, 0.014% Params, 19.66 MMac, 0.085% MACs, 24, 192, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (discrete_gaussian): GaussianConditional(\n", "          0, 0.000% Params, 0.0 Mac, 0.000% MACs, \n", "          (likelihood_lower_bound): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "          (lower_bound_scale): LowerBound(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", "        )\n", "      )\n", "      (16): Sequential(\n", "        9.26 k, 0.027% Params, 37.95 MMac, 0.163% MACs, \n", "        (0): Conv2d(9.26 k, 0.027% Params, 37.95 MMac, 0.163% MACs, 192, 48, kernel_size=(1, 1), stride=(1, 1))\n", "        (1): PixelShuffle(0, 0.000% Params, 0.0 Mac, 0.000% MACs, upscale_factor=4)\n", "      )\n", "    )\n", "  )\n", "  (out_net): MSEOutputNet(0, 0.000% Params, 0.0 Mac, 0.000% MACs, )\n", ")\n", "ptflops estimated FLOPs (MACs) = 23.3B, parameters = 34.037M\n"]}], "source": ["from ptflops import get_model_complexity_info\n", "\n", "ptfl_macs, ptfl_params = get_model_complexity_info(model, input_shape, as_strings=False, verbose=False)\n", "\n", "print(f'ptflops estimated FLOPs (MACs) = {ptfl_macs/1e9:.3g}B, parameters = {ptfl_params/1e6:.3f}M')"]}], "metadata": {"kernelspec": {"display_name": "lwm_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}