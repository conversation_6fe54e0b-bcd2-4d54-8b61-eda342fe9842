#======== File I/O =====================
BitstreamFile                 : str.bin
ReconFile                     : rec.yuv

#======== Profile ================
Profile                       : auto

#======== Unit definition ================
MaxCUWidth                    : 64          # Maximum coding unit width in pixel
MaxCUHeight                   : 64          # Maximum coding unit height in pixel

#======== Coding Structure =============
IntraPeriod                   : -1          # Period of I-Frame ( -1 = only first)
DecodingRefreshType           : 0           # Random Accesss 0:none, 1:CRA, 2:IDR, 3:Recovery Point SEI
GOPSize                       : 8           # GOP Size (number of B slice = GOPSize-1)

IntraQPOffset                 : -1
LambdaFromQpEnable            : 1           # see JCTVC-X0038 for suitable parameters for IntraQPOffset, QPoffset, QPOffsetModelOff, QPOffsetModelScale when enabled
#        Type POC QPoffset QPOffsetModelOff QPOffsetModelScale CbQPoffset CrQPoffset QPfactor tcOffsetDiv2 betaOffsetDiv2 CbTcOffsetDiv2 CbBetaOffsetDiv2 CrTcOffsetDiv2 CrBetaOffsetDiv2 temporal_id #ref_pics_active_L0 #ref_pics_L0   reference_pictures_L0 #ref_pics_active_L1 #ref_pics_L1   reference_pictures_L1
Frame1:    B   1   6       -6.5                      0.2450         0          0          1.0      0            0               0              0                0               0               0             4                4         1 9 17 25                    4                   4      1 3 5 33
Frame2:    B   2   4       -6.5                      0.2590         0          0          1.0      0            0               0              0                0               0               0             4                4         1 2 10 18                    4                   4      1 2 4 26
Frame3:    B   3   6       -6.5                      0.2450         0          0          1.0      0            0               0              0                0               0               0             4                4         1 3 11 19                    4                   4      1 3 5 27
Frame4:    B   4   4       -6.5                      0.2590         0          0          1.0      0            0               0              0                0               0               0             4                4         1 4 12 20                    4                   4      1 2 4 28
Frame5:    B   5   6       -6.5                      0.2450         0          0          1.0      0            0               0              0                0               0               0             4                4         1 5 13 21                    4                   4      1 3 5 29
Frame6:    B   6   4       -6.5                      0.2590         0          0          1.0      0            0               0              0                0               0               0             4                4         1 6 14 22                    4                   4      1 2 6 30
Frame7:    B   7   6       -6.5                      0.2450         0          0          1.0      0            0               0              0                0               0               0             4                4         1 7 15 23                    4                   4      1 3 7 31
Frame8:    B   8   1        0.0                      0.0            0          0          1.0      0            0               0              0                0               0               0             4                4         1 8 16 24                    4                   4      1 2 4 32

#=========== Motion Search =============
FastSearch                    : 1           # 0:Full search  1:TZ search
SearchRange                   : 64          # (0: Search range is a Full frame)
BipredSearchRange             : 4           # Search range for bi-prediction refinement
HadamardME                    : 1           # Use of hadamard measure for fractional ME
FEN                           : 1           # Fast encoder decision
FDM                           : 1           # Fast Decision for Merge RD cost

#======== Quantization =============
QP                            : 32          # Quantization parameter(0-51)
MaxDeltaQP                    : 0           # CU-based multi-QP optimization
MaxCuDQPSubdiv                : 0           # Maximum subdiv for CU luma Qp adjustment
DeltaQpRD                     : 0           # Slice-based multi-QP optimization
RDOQ                          : 1           # RDOQ
RDOQTS                        : 1           # RDOQ for transform skip

#=========== Deblock Filter ============
DeblockingFilterOffsetInPPS         : 1           # Dbl params: 0=varying params in SliceHeader, param = base_param + GOP_offset_param; 1 (default) =constant params in PPS, param = base_param)
DeblockingFilterDisable             : 0           # Disable deblocking filter (0=Filter, 1=No Filter)
DeblockingFilterBetaOffset_div2     : -2           # base_param: -12 ~ 12
DeblockingFilterTcOffset_div2       : 0           # base_param: -12 ~ 12
DeblockingFilterCbBetaOffset_div2   : -2           # base_param: -12 ~ 12
DeblockingFilterCbTcOffset_div2     : 0           # base_param: -12 ~ 12
DeblockingFilterCrBetaOffset_div2   : -2           # base_param: -12 ~ 12
DeblockingFilterCrTcOffset_div2     : 0           # base_param: -12 ~ 12
DeblockingFilterMetric        : 0           # blockiness metric (automatically configures deblocking parameters in bitstream). Applies slice-level loop filter offsets (DeblockingFilterOffsetInPPS and DeblockingFilterDisable must be 0)

#=========== Misc. ============
InternalBitDepth              : 10          # codec operating bit-depth

#=========== Coding Tools =================
SAO                           : 1           # Sample adaptive offset  (0: OFF, 1: ON)
TransformSkip                 : 1           # Transform skipping (0: OFF, 1: ON)
TransformSkipFast             : 1           # Fast Transform skipping (0: OFF, 1: ON)
TransformSkipLog2MaxSize      : 5
SAOLcuBoundary                : 0           # SAOLcuBoundary using non-deblocked pixels (0: OFF, 1: ON)

#=========== TemporalFilter =================
TemporalFilter                : 1
TemporalFilterPastRefs        : 4           # Number of past references for temporal prefilter
TemporalFilterFutureRefs      : 0           # Number of future references for temporal prefilter
TemporalFilterStrengthFrame8  : 0.2         # Enable filter at every 8th frame with strength

#============ Rate Control ======================
RateControl                         : 0                # Rate control: enable rate control
TargetBitrate                       : 1000000          # Rate control: target bitrate, in bps
KeepHierarchicalBit                 : 2                # Rate control: 0: equal bit allocation; 1: fixed ratio bit allocation; 2: adaptive ratio bit allocation
LCULevelRateControl                 : 1                # Rate control: 1: LCU level RC; 0: picture level RC
RCLCUSeparateModel                  : 1                # Rate control: use LCU level separate R-lambda model
InitialQP                           : 0                # Rate control: initial QP
RCForceIntraQP                      : 0                # Rate control: force intra QP to be equal to initial QP

#============ VTM settings ======================
SEIDecodedPictureHash               : 0
CbQpOffset                          : 0
CrQpOffset                          : 0
SameCQPTablesForAllChroma           : 1
QpInValCb                           : 17 22 34 42
QpOutValCb                          : 17 23 35 39
ReWriteParamSets                    : 1
#============ NEXT ====================

# General
CTUSize                      : 128
LCTUFast                     : 1

DualITree                    : 1      # separate partitioning of luma and chroma channels for I-slices
MinQTLumaISlice              : 8
MinQTChromaISliceInChromaSamples: 4      # minimum QT size in chroma samples for chroma separate tree
MinQTNonISlice               : 8
MaxMTTHierarchyDepth         : 3
MaxMTTHierarchyDepthISliceL  : 3
MaxMTTHierarchyDepthISliceC  : 3

MTS                          : 1
MTSIntraMaxCand              : 3
MTSInterMaxCand              : 4
SBT                          : 1
ISP                          : 1
MMVD                         : 1
Affine                       : 1
SbTMVP                       : 1
MaxNumMergeCand              : 6
LMChroma                     : 1      # use CCLM only
DepQuant                     : 1
IMV                          : 1
ALF                          : 1
BCW                          : 1
BcwFast                      : 1
CIIP                         : 1
Geo                          : 1
IBC                          : 0      # turned off in CTC
AllowDisFracMMVD             : 1
AffineAmvr                   : 0
LMCSEnable                   : 1      # LMCS: 0: disable, 1:enable
LMCSSignalType               : 0      # Input signal type: 0:SDR, 1:HDR-PQ, 2:HDR-HLG
LMCSUpdateCtrl               : 2      # LMCS model update control: 0:RA, 1:AI, 2:LDB/LDP
LMCSOffset                   : 1      # chroma residual scaling offset
MRL                          : 1
MIP                          : 0
JointCbCr                    : 1      # joint coding of chroma residuals (if available): 0: disable, 1: enable
PROF                         : 1
ChromaTS                     : 1

# Fast tools
PBIntraFast                  : 1
ISPFast                      : 0
FastMrg                      : 1
AMaxBT                       : 1
FastMIP                      : 0
FastLocalDualTreeMode        : 2

# Encoder optimization tools
AffineAmvrEncOpt             : 0
MmvdDisNum                   : 6
ALFAllowPredefinedFilters    : 1
ALFStrengthTargetLuma        : 1.0
ALFStrengthTargetChroma      : 1.0
CCALFStrengthTarget          : 1.0
EncDbOpt                     : 1      # apply deblocking in RDO
### DO NOT ADD ANYTHING BELOW THIS LINE ###
### DO NOT DELETE THE EMPTY LINE BELOW ###



