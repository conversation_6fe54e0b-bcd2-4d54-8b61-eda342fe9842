import pickle
from collections import OrderedDict
from PIL import Image
import math
import torch
import torch.nn as nn
import torch.nn.functional as tnf
import torch.distributions as td
import torchvision.transforms.functional as tvf
from compressai.entropy_models import GaussianConditional

import lvae.models.common as common
from lvae.models.entropy_coding import gaussian_log_prob_mass


class SimpleModulator(nn.Module):
    """简化的QAM调制器，用于在熵编码后添加调制"""

    def __init__(self, qam_order=256):
        super().__init__()
        self.qam_order = qam_order
        self.register_buffer('constellation', self._create_qam_constellation(qam_order))

    def _create_qam_constellation(self, qam_order):
        """创建QAM星座图"""
        # 计算每个维度的点数
        sqrt_order = int(math.sqrt(qam_order))
        assert sqrt_order * sqrt_order == qam_order, f"QAM order {qam_order} must be a perfect square"

        # 创建PAM点
        pam_points = torch.linspace(-sqrt_order + 1, sqrt_order - 1, sqrt_order, dtype=torch.float32)

        # 创建QAM星座图 (I, Q)
        I, Q = torch.meshgrid(pam_points, pam_points, indexing='ij')
        constellation = torch.stack([I.flatten(), Q.flatten()], dim=1)  # [qam_order, 2]

        # 能量归一化
        avg_power = torch.mean(constellation.pow(2).sum(dim=1))
        constellation = constellation / torch.sqrt(avg_power)

        return constellation

    def modulate(self, quantized_symbols, temperature=1.0):
        """可微分的QAM调制

        Args:
            quantized_symbols (torch.Tensor): 量化后的符号，形状为 [B, C, H, W]
            temperature (float): 软调制的温度参数，越小越接近硬判决

        Returns:
            torch.Tensor: 调制后的复数信号，形状为 [B, C, H, W, 2] (最后一维为I/Q)
        """
        # 使用可微分的归一化方法
        # 假设quantized_symbols大致在合理范围内，使用tanh进行软限制
        # 这样既保持可微分性，又将值映射到合适的范围
        normalized = torch.tanh(quantized_symbols) * (self.qam_order - 1) / 2 + (self.qam_order - 1) / 2

        # 使用软分配而不是硬量化
        # 计算到每个星座点的距离（在索引空间中）
        indices_expanded = normalized.unsqueeze(-1)  # [B, C, H, W, 1]
        constellation_indices = torch.arange(self.qam_order, device=normalized.device, dtype=normalized.dtype)  # [qam_order]

        # 计算软权重（使用负距离的softmax）
        distances = torch.abs(indices_expanded - constellation_indices)  # [B, C, H, W, qam_order]
        soft_weights = torch.softmax(-distances / temperature, dim=-1)  # [B, C, H, W, qam_order]

        # 软调制：加权平均星座点
        constellation_expanded = self.constellation.unsqueeze(0).unsqueeze(0).unsqueeze(0)  # [1, 1, 1, qam_order, 2]
        modulated = torch.sum(soft_weights.unsqueeze(-1) * constellation_expanded, dim=-2)  # [B, C, H, W, 2]

        # 为了兼容性，也返回硬判决的索引（用于调试）
        hard_indices = torch.clamp(torch.round(normalized).long(), 0, self.qam_order - 1)

        return modulated, hard_indices

    def demodulate(self, received_signal, temperature=1.0):
        """可微分的QAM解调

        Args:
            received_signal (torch.Tensor): 接收到的信号，形状为 [B, C, H, W, 2]
            temperature (float): 软解调的温度参数，越小越接近硬判决

        Returns:
            torch.Tensor: 解调后的符号，形状为 [B, C, H, W]
        """
        # 计算到每个星座点的距离
        original_shape = received_signal.shape[:-1]  # [B, C, H, W]
        received_flat = received_signal.view(-1, 2)  # [B*C*H*W, 2]

        # 计算欧几里得距离
        distances = torch.cdist(received_flat, self.constellation)  # [B*C*H*W, qam_order]

        # 软判决：使用负距离的softmax作为权重
        soft_weights = torch.softmax(-distances / temperature, dim=-1)  # [B*C*H*W, qam_order]

        # 软解调：加权平均索引值
        constellation_indices = torch.arange(self.qam_order, device=received_signal.device, dtype=received_signal.dtype)
        soft_indices = torch.sum(soft_weights * constellation_indices, dim=-1)  # [B*C*H*W]

        # 重新整形并归一化到 [0, 1]
        soft_indices = soft_indices.view(original_shape)  # [B, C, H, W]
        demodulated = soft_indices / (self.qam_order - 1)

        return demodulated


class BitstreamModulator(nn.Module):
    """比特流调制器 - 对熵编码后的比特流进行QAM调制"""

    def __init__(self, qam_order=256):
        super().__init__()
        self.qam_order = qam_order
        self.bits_per_symbol = int(math.log2(qam_order))
        self.register_buffer('constellation', self._create_qam_constellation(qam_order))

    def _create_qam_constellation(self, qam_order):
        """创建QAM星座图"""
        # 计算每个维度的点数
        sqrt_order = int(math.sqrt(qam_order))
        assert sqrt_order * sqrt_order == qam_order, f"QAM order {qam_order} must be a perfect square"

        # 创建PAM点
        pam_points = torch.linspace(-sqrt_order + 1, sqrt_order - 1, sqrt_order, dtype=torch.float32)

        # 创建QAM星座图 (I, Q)
        I, Q = torch.meshgrid(pam_points, pam_points, indexing='ij')
        constellation = torch.stack([I.flatten(), Q.flatten()], dim=1)  # [qam_order, 2]

        # 能量归一化
        avg_power = torch.mean(constellation.pow(2).sum(dim=1))
        constellation = constellation / torch.sqrt(avg_power)

        return constellation

    def strings_to_bits(self, strings):
        """将字节串列表转换为比特序列

        Args:
            strings (list): 字节串列表

        Returns:
            torch.Tensor: 比特序列，形状为 [total_bits]
        """
        all_bits = []
        for string in strings:
            # 将字节串转换为比特
            byte_array = bytearray(string)
            for byte in byte_array:
                # 将每个字节转换为8个比特
                bits = [(byte >> i) & 1 for i in range(8)]
                all_bits.extend(bits)

        return torch.tensor(all_bits, dtype=torch.uint8)

    def bits_to_strings(self, bits, original_lengths):
        """将比特序列转换回字节串列表

        Args:
            bits (torch.Tensor): 比特序列
            original_lengths (list): 原始字节串的长度列表

        Returns:
            list: 恢复的字节串列表
        """
        strings = []
        bit_idx = 0

        for length in original_lengths:
            # 每个字节串需要 length * 8 个比特
            string_bits = bits[bit_idx:bit_idx + length * 8]
            bit_idx += length * 8

            # 将比特转换回字节
            byte_array = bytearray()
            for i in range(0, len(string_bits), 8):
                byte_bits = string_bits[i:i+8]
                if len(byte_bits) == 8:
                    byte_val = sum(bit.item() * (2 ** j) for j, bit in enumerate(byte_bits))
                    byte_array.append(byte_val)

            strings.append(bytes(byte_array))

        return strings

    def modulate_bitstream(self, strings):
        """对比特流进行QAM调制

        Args:
            strings (list): 字节串列表

        Returns:
            tuple: (调制信号, 元数据)
        """
        # 记录原始长度信息
        original_lengths = [len(s) for s in strings]

        # 转换为比特序列
        bits = self.strings_to_bits(strings)

        # 填充比特以适应符号边界
        total_bits = len(bits)
        symbols_needed = (total_bits + self.bits_per_symbol - 1) // self.bits_per_symbol
        padded_bits_needed = symbols_needed * self.bits_per_symbol
        padding_bits = padded_bits_needed - total_bits

        if padding_bits > 0:
            padding = torch.zeros(padding_bits, dtype=torch.uint8)
            bits = torch.cat([bits, padding])

        # 将比特分组为符号
        bits_reshaped = bits.view(-1, self.bits_per_symbol)  # [num_symbols, bits_per_symbol]

        # 将比特组合转换为符号索引
        symbol_indices = torch.zeros(bits_reshaped.shape[0], dtype=torch.long)
        for i in range(self.bits_per_symbol):
            symbol_indices += bits_reshaped[:, i] * (2 ** i)

        # 映射到星座点
        modulated_symbols = self.constellation[symbol_indices]  # [num_symbols, 2]

        # 元数据
        metadata = {
            'original_lengths': original_lengths,
            'total_bits': total_bits,
            'padding_bits': padding_bits,
            'num_symbols': len(symbol_indices)
        }

        return modulated_symbols, metadata

    def demodulate_bitstream(self, received_signal, metadata):
        """解调QAM信号恢复比特流

        Args:
            received_signal (torch.Tensor): 接收信号，形状为 [num_symbols, 2]
            metadata (dict): 调制时的元数据

        Returns:
            list: 恢复的字节串列表
        """
        # 硬判决：找到最近的星座点
        distances = torch.cdist(received_signal, self.constellation)
        symbol_indices = torch.argmin(distances, dim=1)

        # 将符号索引转换回比特
        recovered_bits = []
        for idx in symbol_indices:
            # 将索引转换为比特
            bits = [(idx.item() >> i) & 1 for i in range(self.bits_per_symbol)]
            recovered_bits.extend(bits)

        recovered_bits = torch.tensor(recovered_bits, dtype=torch.uint8)

        # 移除填充比特
        if metadata['padding_bits'] > 0:
            recovered_bits = recovered_bits[:-metadata['padding_bits']]

        # 转换回字节串
        strings = self.bits_to_strings(recovered_bits, metadata['original_lengths'])

        return strings


class EnhancedHierarchicalModulator(nn.Module):
    """增强型分层调制器 - 结合传统容错思想的智能分层保护系统"""

    def __init__(self, qam_orders=[4, 16, 64, 256], enable_error_detection=True,
                 enable_adaptive_protection=True, enable_error_concealment=True):
        """
        Args:
            qam_orders (list): 不同保护等级的QAM阶数，从高保护到低保护
            enable_error_detection (bool): 是否启用错误检测
            enable_adaptive_protection (bool): 是否启用基于熵值的自适应保护
            enable_error_concealment (bool): 是否启用错误隐藏/修复
        """
        super().__init__()
        self.qam_orders = qam_orders
        self.num_levels = len(qam_orders)
        self.enable_error_detection = enable_error_detection
        self.enable_adaptive_protection = enable_adaptive_protection
        self.enable_error_concealment = enable_error_concealment

        # 为每个保护等级创建星座图和比特数
        self.constellations = {}
        self.bits_per_symbol = {}

        for i, qam_order in enumerate(qam_orders):
            constellation = self._create_qam_constellation(qam_order)
            self.register_buffer(f'constellation_{i}', constellation)
            self.constellations[i] = constellation
            self.bits_per_symbol[i] = int(math.log2(qam_order))

        # 错误检测相关参数
        if self.enable_error_detection:
            self.crc_poly = 0x1021  # CRC-16-CCITT多项式

        # 自适应保护的熵阈值
        if self.enable_adaptive_protection:
            self.entropy_thresholds = [2.0, 4.0, 6.0]  # 熵值阈值，用于动态分层

    def _compute_crc16(self, data):
        """计算CRC-16校验码"""
        if not isinstance(data, bytes):
            data = bytes(data)
        crc = 0xFFFF
        for byte in data:
            crc ^= byte << 8
            for _ in range(8):
                if crc & 0x8000:
                    crc = (crc << 1) ^ self.crc_poly
                else:
                    crc <<= 1
                crc &= 0xFFFF
        return crc

    def _estimate_entropy(self, string_data):
        """估算字节串的熵值"""
        if not string_data:
            return 0.0

        # 统计字节频率
        byte_counts = {}
        for byte in string_data:
            byte_counts[byte] = byte_counts.get(byte, 0) + 1

        # 计算熵
        total_bytes = len(string_data)
        entropy = 0.0
        for count in byte_counts.values():
            prob = count / total_bytes
            if prob > 0:
                entropy -= prob * math.log2(prob)

        return entropy

    def _adaptive_protection_assignment(self, strings, entropies=None):
        """基于熵值的自适应保护等级分配"""
        if not self.enable_adaptive_protection or entropies is None:
            return self._assign_protection_levels(strings)

        protection_levels = []
        for i, (string, entropy) in enumerate(zip(strings, entropies)):
            # 根据熵值动态分配保护等级
            if entropy <= self.entropy_thresholds[0]:
                level = 0  # 低熵，高保护
            elif entropy <= self.entropy_thresholds[1]:
                level = min(1, self.num_levels - 1)
            elif entropy <= self.entropy_thresholds[2]:
                level = min(2, self.num_levels - 1)
            else:
                level = self.num_levels - 1  # 高熵，低保护

            protection_levels.append(level)

        return protection_levels

    def _create_qam_constellation(self, qam_order):
        """创建QAM星座图"""
        sqrt_order = int(math.sqrt(qam_order))
        assert sqrt_order * sqrt_order == qam_order, f"QAM order {qam_order} must be a perfect square"

        # 创建PAM点
        pam_points = torch.linspace(-sqrt_order + 1, sqrt_order - 1, sqrt_order, dtype=torch.float32)

        # 创建QAM星座图 (I, Q)
        I, Q = torch.meshgrid(pam_points, pam_points, indexing='ij')
        constellation = torch.stack([I.flatten(), Q.flatten()], dim=1)  # [qam_order, 2]

        # 能量归一化
        avg_power = torch.mean(constellation.pow(2).sum(dim=1))
        constellation = constellation / torch.sqrt(avg_power)

        return constellation

    def _assign_protection_levels(self, strings):
        """根据strings的重要性分配保护等级

        Args:
            strings (list): 熵编码输出的字节串列表

        Returns:
            list: 每个string对应的保护等级 (0=最高保护, num_levels-1=最低保护)
        """
        num_strings = len(strings)
        protection_levels = []

        # 简单策略：根据strings的索引分配保护等级
        # 前面的strings通常对应低分辨率，更重要
        for i, string in enumerate(strings):
            if i < num_strings // 4:  # 前25%最重要
                level = 0  # 最高保护 (4-QAM)
            elif i < num_strings // 2:  # 25%-50%重要
                level = min(1, self.num_levels - 1)  # 高保护 (16-QAM)
            elif i < 3 * num_strings // 4:  # 50%-75%一般
                level = min(2, self.num_levels - 1)  # 中保护 (64-QAM)
            else:  # 后25%不重要
                level = self.num_levels - 1  # 低保护 (256-QAM)

            protection_levels.append(level)

        return protection_levels

    def modulate_bitstream(self, strings, entropies=None):
        """增强型分层调制 - 支持自适应保护和错误检测

        Args:
            strings (list): 熵编码输出的字节串列表
            entropies (list, optional): 每个string对应的熵值，用于自适应保护

        Returns:
            tuple: (modulated_signal, metadata)
        """
        if not strings:
            return torch.empty(0, 2), {'original_lengths': [], 'protection_levels': [],
                                     'level_boundaries': [], 'crc_codes': [], 'entropies': []}

        # 计算熵值（如果未提供）
        if entropies is None and self.enable_adaptive_protection:
            entropies = [self._estimate_entropy(bytearray(s)) for s in strings]

        # 自适应保护等级分配
        protection_levels = self._adaptive_protection_assignment(strings, entropies)

        # 添加错误检测码
        crc_codes = []
        if self.enable_error_detection:
            crc_codes = [self._compute_crc16(s) for s in strings]

        # 按保护等级分组处理
        all_symbols = []
        level_boundaries = []  # 记录每个等级的符号边界
        enhanced_strings = []

        # 为每个string添加CRC校验码
        for i, string in enumerate(strings):
            enhanced_string = string
            if self.enable_error_detection:
                # 将CRC码附加到字节串末尾
                crc_bytes = crc_codes[i].to_bytes(2, byteorder='big')
                enhanced_string = string + crc_bytes
            enhanced_strings.append(enhanced_string)

        for level in range(self.num_levels):
            # 找到属于当前保护等级的strings
            level_strings = [enhanced_strings[i] for i, pl in enumerate(protection_levels) if pl == level]

            if not level_strings:
                level_boundaries.append(len(all_symbols))
                continue

            # 转换为比特
            level_bits = self._strings_to_bits(level_strings)

            # 调制
            level_symbols = self._modulate_bits(level_bits, level)
            all_symbols.append(level_symbols)
            level_boundaries.append(len(all_symbols) if level_symbols.numel() > 0 else len(all_symbols))

        # 合并所有符号
        if all_symbols:
            modulated_signal = torch.cat([s for s in all_symbols if s.numel() > 0], dim=0)
        else:
            modulated_signal = torch.empty(0, 2)

        metadata = {
            'original_lengths': [len(s) for s in strings],
            'protection_levels': protection_levels,
            'level_boundaries': level_boundaries,
            'crc_codes': crc_codes,
            'entropies': entropies or [],
            'enhanced_lengths': [len(s) for s in enhanced_strings]
        }

        return modulated_signal, metadata

    def _strings_to_bits(self, strings):
        """将字节串列表转换为比特序列"""
        all_bits = []
        for string in strings:
            byte_array = bytearray(string)
            for byte in byte_array:
                bits = [(byte >> i) & 1 for i in range(8)]
                all_bits.extend(bits)
        return torch.tensor(all_bits, dtype=torch.uint8)

    def _modulate_bits(self, bits, protection_level):
        """使用指定保护等级调制比特"""
        if len(bits) == 0:
            return torch.empty(0, 2)

        bits_per_sym = self.bits_per_symbol[protection_level]
        constellation = getattr(self, f'constellation_{protection_level}')

        # 填充比特以适应符号边界
        symbols_needed = (len(bits) + bits_per_sym - 1) // bits_per_sym
        padded_bits_needed = symbols_needed * bits_per_sym
        padding_bits = padded_bits_needed - len(bits)

        if padding_bits > 0:
            padding = torch.zeros(padding_bits, dtype=torch.uint8)
            bits = torch.cat([bits, padding])

        # 将比特分组为符号
        bits_reshaped = bits.view(-1, bits_per_sym)

        # 转换为符号索引
        symbol_indices = torch.zeros(bits_reshaped.shape[0], dtype=torch.long)
        for i in range(bits_per_sym):
            symbol_indices += bits_reshaped[:, i].long() * (2 ** i)

        # 映射到星座点
        modulated = constellation[symbol_indices]  # [num_symbols, 2]

        return modulated

    def demodulate_bitstream(self, received_signal, metadata):
        """增强型解调 - 支持错误检测和智能修复"""
        if received_signal.numel() == 0:
            return []

        protection_levels = metadata['protection_levels']
        level_boundaries = metadata['level_boundaries']
        original_lengths = metadata['original_lengths']
        crc_codes = metadata.get('crc_codes', [])
        enhanced_lengths = metadata.get('enhanced_lengths', original_lengths)

        # 按保护等级分离符号
        all_recovered_bits = []
        symbol_start = 0

        for level in range(self.num_levels):
            if level < len(level_boundaries):
                symbol_end = level_boundaries[level]
            else:
                symbol_end = received_signal.shape[0]

            if symbol_end > symbol_start:
                level_symbols = received_signal[symbol_start:symbol_end]
                level_bits = self._demodulate_symbols(level_symbols, level)
                all_recovered_bits.append(level_bits)
                symbol_start = symbol_end

        # 重新组织比特回到原始strings的顺序
        recovered_strings = self._reconstruct_strings_enhanced(
            all_recovered_bits, protection_levels, original_lengths,
            enhanced_lengths, crc_codes
        )

        return recovered_strings

    def _demodulate_symbols(self, symbols, protection_level):
        """解调指定保护等级的符号"""
        if symbols.numel() == 0:
            return torch.empty(0, dtype=torch.uint8)

        constellation = getattr(self, f'constellation_{protection_level}')
        bits_per_sym = self.bits_per_symbol[protection_level]

        # 硬判决解调
        distances = torch.cdist(symbols, constellation)
        indices = torch.argmin(distances, dim=1)

        # 将索引转换为比特
        bits = []
        for idx in indices:
            for i in range(bits_per_sym):
                bits.append((idx.item() >> i) & 1)

        return torch.tensor(bits, dtype=torch.uint8)

    def _reconstruct_strings(self, level_bits_list, protection_levels, original_lengths):
        """重新构造原始strings（简化版本）"""
        # 这是一个简化实现，实际需要更复杂的逻辑来正确重组
        # 目前返回空strings作为占位符
        return [b''] * len(original_lengths)

    def _reconstruct_strings_enhanced(self, level_bits_list, protection_levels,
                                    original_lengths, enhanced_lengths, crc_codes):
        """增强型字符串重构 - 支持错误检测和修复"""
        recovered_strings = []
        error_detected = []

        # 简化实现：假设我们能够正确重组比特流
        # 实际实现需要根据protection_levels重新排列比特
        for i, orig_len in enumerate(original_lengths):
            try:
                # 模拟重构过程（实际需要复杂的比特重组逻辑）
                if i < len(level_bits_list) and len(level_bits_list) > 0:
                    # 这里应该有复杂的比特重组逻辑
                    # 目前使用简化的占位符
                    reconstructed_data = b'\x00' * orig_len
                else:
                    reconstructed_data = b''

                # 错误检测
                is_corrupted = False
                if self.enable_error_detection and i < len(crc_codes):
                    # 计算重构数据的CRC
                    computed_crc = self._compute_crc16(reconstructed_data)
                    expected_crc = crc_codes[i]
                    is_corrupted = (computed_crc != expected_crc)

                error_detected.append(is_corrupted)

                # 如果检测到错误且启用了错误隐藏
                if is_corrupted and self.enable_error_concealment:
                    # 错误隐藏：使用邻近块的信息进行修复
                    repaired_data = self._error_concealment(
                        reconstructed_data, i, recovered_strings, protection_levels
                    )
                    recovered_strings.append(repaired_data)
                else:
                    recovered_strings.append(reconstructed_data)

            except Exception as e:
                # 如果重构失败，使用错误隐藏
                if self.enable_error_concealment:
                    repaired_data = self._error_concealment(
                        b'', i, recovered_strings, protection_levels
                    )
                    recovered_strings.append(repaired_data)
                else:
                    recovered_strings.append(b'')
                error_detected.append(True)

        # 记录错误统计信息（可用于调试和优化）
        if hasattr(self, 'error_stats'):
            self.error_stats = {
                'total_blocks': len(original_lengths),
                'corrupted_blocks': sum(error_detected),
                'error_rate': sum(error_detected) / len(original_lengths) if original_lengths else 0
            }

        return recovered_strings

    def _error_concealment(self, corrupted_data, block_index, recovered_blocks, protection_levels):
        """错误隐藏/修复机制"""
        if not self.enable_error_concealment:
            return corrupted_data

        # 简单的错误隐藏策略：使用邻近块的平均值
        if len(recovered_blocks) == 0:
            # 如果没有可用的邻近块，返回零填充
            return b'\x00' * len(corrupted_data) if corrupted_data else b'\x00' * 64

        # 寻找相同保护等级的邻近块
        current_protection = protection_levels[block_index] if block_index < len(protection_levels) else 0
        similar_blocks = []

        for i, block in enumerate(recovered_blocks):
            if i < len(protection_levels) and protection_levels[i] == current_protection:
                similar_blocks.append(block)

        if similar_blocks:
            # 使用相似保护等级块的模式
            reference_block = similar_blocks[-1]  # 使用最近的相似块
            target_length = len(corrupted_data) if corrupted_data else len(reference_block)

            if len(reference_block) >= target_length:
                return reference_block[:target_length]
            else:
                # 如果参考块太短，进行填充
                padding_needed = target_length - len(reference_block)
                return reference_block + b'\x00' * padding_needed
        else:
            # 如果没有相似块，使用最后一个可用块
            reference_block = recovered_blocks[-1]
            target_length = len(corrupted_data) if corrupted_data else len(reference_block)

            if len(reference_block) >= target_length:
                return reference_block[:target_length]
            else:
                padding_needed = target_length - len(reference_block)
                return reference_block + b'\x00' * padding_needed


class SimpleAWGNChannel(nn.Module):
    """简化的AWGN信道"""

    def __init__(self, snr_db=20.0):
        super().__init__()
        self.snr_db = snr_db

    def forward(self, modulated_signal):
        """通过AWGN信道传输信号

        Args:
            modulated_signal (torch.Tensor): 调制信号，形状为 [B, C, H, W, 2]

        Returns:
            torch.Tensor: 加噪后的信号，形状为 [B, C, H, W, 2]
        """
        if not self.training:
            # 推理时不添加噪声
            return modulated_signal

        # 计算信号功率
        signal_power = torch.mean(modulated_signal.pow(2))
        signal_power = torch.clamp(signal_power, min=1e-10)

        # 计算噪声功率
        snr_linear = 10.0 ** (self.snr_db / 10.0)
        noise_power = signal_power / snr_linear

        # 生成AWGN噪声
        noise = torch.sqrt(noise_power / 2) * torch.randn_like(modulated_signal)

        # 添加噪声
        received_signal = modulated_signal + noise

        return received_signal

    def set_snr(self, snr_db):
        """设置信噪比"""
        self.snr_db = snr_db


class GaussianNLLOutputNet(nn.Module):
    def __init__(self, conv_mean, conv_scale, bin_size=1/127.5):
        super().__init__()
        self.conv_mean  = conv_mean
        self.conv_scale = conv_scale
        self.bin_size = bin_size
        self.loss_name = 'nll'

    def forward_loss(self, feature, x_tgt):
        """ compute negative log-likelihood loss

        Args:
            feature (torch.Tensor): feature given by the top-down decoder
            x_tgt (torch.Tensor): original image
        """
        feature = feature.float()
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_logscale = tnf.softplus(p_logscale + 16) - 16 # logscale lowerbound
        log_prob = gaussian_log_prob_mass(p_mean, torch.exp(p_logscale), x_tgt, bin_size=self.bin_size)
        assert log_prob.shape == x_tgt.shape
        nll = -log_prob.mean(dim=(1,2,3)) # BCHW -> (B,)
        return nll, p_mean

    def mean(self, feature):
        p_mean = self.conv_mean(feature)
        return p_mean

    def sample(self, feature, mode='continuous', temprature=None):
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_scale = torch.exp(p_logscale)
        if temprature is not None:
            p_scale = p_scale * temprature

        if mode == 'continuous':
            samples = p_mean + p_scale * torch.randn_like(p_mean)
        elif mode == 'discrete':
            raise NotImplementedError()
        else:
            raise ValueError()
        return samples

    def update(self):
        self.discrete_gaussian = GaussianConditional(None, scale_bound=0.11)
        device = next(self.parameters()).device
        self.discrete_gaussian = self.discrete_gaussian.to(device=device)
        lower = self.discrete_gaussian.lower_bound_scale.bound.item()
        max_scale = 20
        scale_table = torch.exp(torch.linspace(math.log(lower), math.log(max_scale), steps=128))
        updated = self.discrete_gaussian.update_scale_table(scale_table)
        self.discrete_gaussian.update()

    def _preapre_codec(self, feature, x=None):
        assert not feature.requires_grad
        pm = self.conv_mean(feature)
        pm = torch.round(pm * 127.5 + 127.5) / 127.5 - 1 # workaround to make sure lossless
        plogv = self.conv_scale(feature)
        # scale (-1,1) range to (-127.5, 127.5) range
        pm = pm / self.bin_size
        plogv = plogv - math.log(self.bin_size)
        if x is not None:
            x = x / self.bin_size
        return pm, plogv, x

    def compress(self, feature, x):
        pm, plogv, x = self._preapre_codec(feature, x)
        # compress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        strings = self.discrete_gaussian.compress(x, indexes, means=pm)
        return strings

    def decompress(self, feature, strings):
        pm, plogv, _ = self._preapre_codec(feature)
        # decompress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        x_hat = self.discrete_gaussian.decompress(strings, indexes, means=pm)
        x_hat = x_hat * self.bin_size
        return x_hat


class MSEOutputNet(nn.Module):
    def __init__(self, mse_lmb):
        super().__init__()
        self.mse_lmb = float(mse_lmb)
        self.loss_name = 'mse'

    def forward_loss(self, x_hat, x_tgt):
        """ compute MSE loss

        Args:
            x_hat (torch.Tensor): reconstructed image
            x_tgt (torch.Tensor): original image
        """
        assert x_hat.shape == x_tgt.shape
        mse = tnf.mse_loss(x_hat, x_tgt, reduction='none').mean(dim=(1,2,3)) # (B,3,H,W) -> (B,)
        loss = mse * self.mse_lmb
        return loss, x_hat

    def mean(self, x_hat, temprature=None):
        return x_hat
    sample = mean


class VDBlock(nn.Module):
    """ Adapted from VDVAE (https://github.com/openai/vdvae)
    - Paper: Very Deep VAEs Generalize Autoregressive Models and Can Outperform Them on Images
    - arxiv: https://arxiv.org/abs/2011.10650
    """
    def __init__(self, in_ch, hidden_ch=None, out_ch=None, residual=True,
                 use_3x3=True, zero_last=False):
        super().__init__()
        out_ch = out_ch or in_ch
        hidden_ch = hidden_ch or round(in_ch * 0.25)
        self.in_channels = in_ch
        self.out_channels = out_ch
        self.residual = residual
        self.c1 = common.conv_k1s1(in_ch, hidden_ch)
        self.c2 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c3 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c4 = common.conv_k1s1(hidden_ch, out_ch, zero_weights=zero_last)

    def residual_scaling(self, N):
        # This residual scaling improves stability and performance with many layers
        # https://arxiv.org/pdf/2011.10650.pdf, Appendix Table 3
        self.c4.weight.data.mul_(math.sqrt(1 / N))

    def forward(self, x):
        xhat = self.c1(tnf.gelu(x))
        xhat = self.c2(tnf.gelu(xhat))
        xhat = self.c3(tnf.gelu(xhat))
        xhat = self.c4(tnf.gelu(xhat))
        out = (x + xhat) if self.residual else xhat
        return out

class VDBlockPatchDown(VDBlock):
    def __init__(self, in_ch, out_ch, down_rate=2):
        super().__init__(in_ch, residual=True)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


from timm.models.convnext import ConvNeXtBlock
class MyConvNeXtBlock(ConvNeXtBlock):
    def __init__(self, dim, mlp_ratio=2, **kwargs):
        super().__init__(dim, mlp_ratio=mlp_ratio, **kwargs)
        self.norm.affine = True # this variable is useless. just a workaround for flops computation

    def forward(self, x):
        shortcut = x
        x = self.conv_dw(x)
        if self.use_conv_mlp:
            x = self.norm(x)
            x = self.mlp(x)
        else:
            x = x.permute(0, 2, 3, 1).contiguous()
            x = self.norm(x)
            x = self.mlp(x)
            x = x.permute(0, 3, 1, 2).contiguous()
        if self.gamma is not None:
            x = x.mul(self.gamma.reshape(1, -1, 1, 1))
        x = self.drop_path(x) + shortcut
        return x

class MyConvNeXtPatchDown(MyConvNeXtBlock):
    def __init__(self, in_ch, out_ch, down_rate=2, mlp_ratio=2, kernel_size=7):
        super().__init__(in_ch, mlp_ratio=mlp_ratio, kernel_size=kernel_size)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


class BottomUpEncoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.enc_blocks = nn.ModuleList(blocks)

    def forward(self, x):
        feature = x
        enc_features = dict()
        for i, block in enumerate(self.enc_blocks):
            feature = block(feature)
            res = int(feature.shape[2])
            enc_features[res] = feature
        return enc_features


class QLatentBlockX(nn.Module):
    """ Latent block as described in the paper.
    """
    def __init__(self, width, zdim, enc_width=None, kernel_size=7, qam_order=256, snr_db=20.0, use_hierarchical_modulation=True):
        """
        Args:
            width       (int): number of feature channels
            zdim        (int): number of latent variable channels
            enc_width   (int, optional): number of encoder feature channels. \
                Defaults to `width` if not provided.
            kernel_size (int, optional): convolution kernel size. Defaults to 7.
            qam_order   (int, optional): QAM modulation order. Defaults to 256.
            snr_db      (float, optional): SNR in dB for AWGN channel. Defaults to 20.0.
            use_hierarchical_modulation (bool): 是否使用分层调制实现不等错误保护
        """
        super().__init__()
        self.in_channels  = width
        self.out_channels = width

        enc_width = enc_width or width
        hidden = int(max(width, enc_width) * 0.25)
        concat_ch = (width * 2) if enc_width is None else (width + enc_width)
        use_3x3 = (kernel_size >= 3)
        self.resnet_front = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.resnet_end   = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.posterior = VDBlock(concat_ch, hidden, zdim, residual=False, use_3x3=use_3x3)
        self.prior     = VDBlock(width, hidden, zdim * 2, residual=False, use_3x3=use_3x3,
                                 zero_last=True)
        self.z_proj = nn.Sequential(
            common.conv_k3s1(zdim, hidden//2) if use_3x3 else common.conv_k1s1(zdim, hidden//2),
            nn.GELU(),
            common.conv_k1s1(hidden//2, width),
        )
        self.discrete_gaussian = GaussianConditional(None)

        # 选择调制器类型
        self.use_hierarchical_modulation = use_hierarchical_modulation
        if use_hierarchical_modulation:
            # 使用增强型分层调制器实现不等错误保护
            self.bitstream_modulator = EnhancedHierarchicalModulator([4, 16, 64, 256])
        else:
            # 使用统一调制器
            self.bitstream_modulator = BitstreamModulator(qam_order)

        self.channel = SimpleAWGNChannel(snr_db)

        # 保留原有的符号调制器用于训练时的模拟
        self.symbol_modulator = SimpleModulator(qam_order)

        # 训练步数计数器（用于噪声调度）
        self.register_buffer('training_step', torch.tensor(0, dtype=torch.long))





    def residual_scaling(self, N):
        self.z_proj[2].weight.data.mul_(math.sqrt(1 / 3*N))

    def transform_prior(self, feature):
        """ prior p(z_i | z_<i)

        Args:
            feature (torch.Tensor): feature map
        """
        feature = self.resnet_front(feature)
        # prior p(z)
        pm, plogv = self.prior(feature).chunk(2, dim=1)
        plogv = tnf.softplus(plogv + 2.3) - 2.3 # make logscale > -2.3
        return feature, pm, plogv

    def forward_train(self, feature, enc_feature, get_latents=False):
        """ Training mode. Forward pass and compute KL.

        Args:
            feature     (torch.Tensor): feature map
            enc_feature (torch.Tensor): feature map
        """
        feature, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
        # posterior q(z|x)
        assert feature.shape[2:4] == enc_feature.shape[2:4]
        qm = self.posterior(torch.cat([feature, enc_feature], dim=1))

        # 端到端训练：训练时也模拟完整的传输过程
        if self.training:
            # 获取全局训练步数
            root_model = self
            while hasattr(root_model, '_modules') and len(root_model._modules) > 0:
                for name, module in root_model.named_children():
                    if hasattr(module, 'global_training_step'):
                        root_model = module
                        break
                else:
                    break

            # 训练策略：渐进式引入完整的传输过程
            if hasattr(root_model, 'global_training_step') and root_model.global_training_step > 1000:
                # 1000步后开始端到端训练
                training_progress = min(1.0, (root_model.global_training_step - 1000) / 5000)

                if torch.rand(1).item() < 0.5 + 0.4 * training_progress:  # 50%-90%概率使用端到端
                    try:
                        # 完整的压缩-传输-解压过程
                        indexes = self.discrete_gaussian.build_indexes(pv)
                        strings = self.discrete_gaussian.compress(qm, indexes, means=pm)

                        # 调制
                        modulated_signal, metadata = self.bitstream_modulator.modulate_bitstream(strings)

                        # 信道传输（包含噪声）
                        received_signal = self.channel(modulated_signal.unsqueeze(0)).squeeze(0)

                        # 解调和解码
                        processed_strings = self.bitstream_modulator.demodulate_bitstream(received_signal, metadata)
                        z_sample = self.discrete_gaussian.decompress(processed_strings, indexes, means=pm)

                        # 重新计算概率（用于KL损失）
                        _, probs = self.discrete_gaussian(qm, scales=pv, means=pm)
                        kl = -1.0 * torch.log(probs)

                    except Exception:
                        # 如果端到端失败，回退到标准方法
                        z_sample, probs = self.discrete_gaussian(qm, scales=pv, means=pm)
                        kl = -1.0 * torch.log(probs)
                else:
                    # 标准训练（无噪声）
                    z_sample, probs = self.discrete_gaussian(qm, scales=pv, means=pm)
                    kl = -1.0 * torch.log(probs)
            else:
                # 前1000步：标准训练，学习基本压缩
                z_sample, probs = self.discrete_gaussian(qm, scales=pv, means=pm)
                kl = -1.0 * torch.log(probs)

        else:
            # 推理时使用相同的离散高斯处理
            z_sample, probs = self.discrete_gaussian(qm, scales=pv, means=pm)
            kl = -1.0 * torch.log(probs)

        # add the new information to feature
        feature = feature + self.z_proj(z_sample)
        feature = self.resnet_end(feature)
        if get_latents:
            return feature, dict(z=z_sample.detach(), kl=kl)
        return feature, dict(kl=kl)

    def forward_uncond(self, feature, t=1.0, latent=None, paint_box=None):
        """ Sampling mode.

        Args:
            feature   (Tensor): feature map.
            t         (float):  tempreture. Defaults to 1.0.
            latent    (Tensor): latent variable z. Sample it from prior if not provided.
            paint_box (Tensor): masked box for inpainting. (x1, y1, x2, y2).
        """
        feature, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
        pv = pv * t # modulate the prior scale by the temperature t
        if latent is None: # normal case. Just sampling.
            z = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
        elif paint_box is not None: # partial sampling for inpainting
            nB, zC, zH, zW = latent.shape
            if min(zH, zW) == 1:
                z = latent
            else:
                x1, y1, x2, y2 = paint_box
                h_slice = slice(round(y1*zH), round(y2*zH))
                w_slice = slice(round(x1*zW), round(x2*zW))
                z_sample = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
                z_patch = z_sample[:, :, h_slice, w_slice]
                z = torch.clone(latent)
                z[:, :, h_slice, w_slice] = z_patch
        else: # if `latent` is provided and `paint_box` is not provided, directly use it.
            assert pm.shape == latent.shape
            z = latent
        feature = feature + self.z_proj(z)
        feature = self.resnet_end(feature)
        return feature

    def update(self):
        """ Prepare for entropy coding. Musted be called before compression.
        """
        min_scale = 0.1
        max_scale = 20
        log_scales = torch.linspace(math.log(min_scale), math.log(max_scale), steps=64)
        scale_table = torch.exp(log_scales)
        updated = self.discrete_gaussian.update_scale_table(scale_table)
        self.discrete_gaussian.update()

    def compress(self, feature, enc_feature, fast_mode=False):
        """ Forward pass, compression (encoding) mode.

        Args:
            feature     (torch.Tensor): feature map
            enc_feature (torch.Tensor): feature map
            fast_mode   (bool): 如果为True，跳过调制/解调过程以加速压缩
        """
        feature, pm, plogv = self.transform_prior(feature)
        # posterior q(z|x)
        qm = self.posterior(torch.cat([feature, enc_feature], dim=1))
        # compress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        strings = self.discrete_gaussian.compress(qm, indexes, means=pm)

        if fast_mode:
            # 快速模式：跳过调制/解调，直接使用原始strings
            processed_strings = strings
            zhat = self.discrete_gaussian.decompress(processed_strings, indexes, means=pm)
        else:
            # 完整模式：包含调制和信道处理（用于实际传输）
            modulated_signal, metadata = self.bitstream_modulator.modulate_bitstream(strings)

            # 通过信道（添加AWGN噪声）
            # 注意：训练时的噪声已经在forward_train中处理了
            received_signal = self.channel(modulated_signal.unsqueeze(0)).squeeze(0)
            processed_strings = self.bitstream_modulator.demodulate_bitstream(received_signal, metadata)
            zhat = self.discrete_gaussian.decompress(processed_strings, indexes, means=pm)

        # add the new information to feature
        feature = feature + self.z_proj(zhat)
        feature = self.resnet_end(feature)
        return feature, processed_strings

    def decompress(self, feature, strings):
        """ Forward pass, decompression (decoding) mode.

        Args:
            feature (torch.Tensor): feature map
            strings (list[str]):    encoded bits
        """
        feature, pm, plogv = self.transform_prior(feature)
        # decompress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        zhat = self.discrete_gaussian.decompress(strings, indexes, means=pm)

        # add the new information to feature
        feature = feature + self.z_proj(zhat)
        feature = self.resnet_end(feature)
        return feature


class TopDownDecoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.dec_blocks = nn.ModuleList(blocks)

        width = self.dec_blocks[0].in_channels
        self.bias = nn.Parameter(torch.zeros(1, width, 1, 1))

        self._init_weights()

    def _init_weights(self):
        total_blocks = len([1 for b in self.dec_blocks if hasattr(b, 'residual_scaling')])
        for block in self.dec_blocks:
            if hasattr(block, 'residual_scaling'):
                block.residual_scaling(total_blocks)

    def forward(self, enc_features, get_latents=False):
        stats = []
        min_res = min(enc_features.keys())
        feature = self.bias.expand(enc_features[min_res].shape)
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_train'):
                res = int(feature.shape[2])
                f_enc = enc_features[res]
                feature, block_stats = block.forward_train(feature, f_enc, get_latents=get_latents)
                stats.append(block_stats)
            else:
                feature = block(feature)
        return feature, stats

    def forward_uncond(self, nhw_repeat=(1, 1, 1), t=1.0):
        nB, nH, nW = nhw_repeat
        feature = self.bias.expand(nB, -1, nH, nW)
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'):
                feature = block.forward_uncond(feature, t)
            else:
                feature = block(feature)
        return feature

    def forward_with_latents(self, latents, nhw_repeat=None, t=1.0, paint_box=None):
        if nhw_repeat is None:
            nB, _, nH, nW = latents[0].shape
            feature = self.bias.expand(nB, -1, nH, nW)
        else: # use defined
            nB, nH, nW = nhw_repeat
            feature = self.bias.expand(nB, -1, nH, nW)
        idx = 0
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'):
                feature = block.forward_uncond(feature, t, latent=latents[idx], paint_box=paint_box)
                idx += 1
            else:
                feature = block(feature)
        return feature

    def update(self):
        for block in self.dec_blocks:
            if hasattr(block, 'update'):
                block.update()

    def compress(self, enc_features, fast_mode=False):
        # assert len(self.bias_xs) == 1
        min_res = min(enc_features.keys())
        feature = self.bias.expand(enc_features[min_res].shape)
        strings_all = []
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'compress'):
                # res = block.up_rate * feature.shape[2]
                res = feature.shape[2]
                f_enc = enc_features[res]
                feature, strs_batch = block.compress(feature, f_enc, fast_mode)
                strings_all.append(strs_batch)
            else:
                feature = block(feature)
        return strings_all, feature

    def decompress(self, compressed_object: list):
        # assert len(self.bias_xs) == 1
        smallest_shape = compressed_object[-1]
        feature = self.bias.expand(smallest_shape)
        # assert len(compressed_object) == len(self.dec_blocks)
        str_i = 0
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'decompress'):
                strs_batch = compressed_object[str_i]
                str_i += 1
                feature = block.decompress(feature, strs_batch)
            else:
                feature = block(feature)
        assert str_i == len(compressed_object) - 1, f'decoded={str_i}, len={len(compressed_object)}'
        return feature


class HierarchicalVAE(nn.Module):
    """ Class of general hierarchical VAEs
    """
    log2_e = math.log2(math.e)

    def __init__(self, config: dict):
        """ Initialize model

        Args:
            config (dict): model config dict
        """
        super().__init__()
        self.encoder = BottomUpEncoder(blocks=config.pop('enc_blocks'))
        self.decoder = TopDownDecoder(blocks=config.pop('dec_blocks'))
        self.out_net = config.pop('out_net')

        self.im_shift = float(config['im_shift'])
        self.im_scale = float(config['im_scale'])
        self.max_stride = config['max_stride']

        # 训练步数跟踪（用于渐进式噪声训练）
        self.register_buffer('global_training_step', torch.tensor(0, dtype=torch.long))

        # 信道参数配置
        self.qam_order = config.get('qam_order', 256)
        self.snr_db = config.get('snr_db', 20.0)

        self.register_buffer('_dummy', torch.zeros(1), persistent=False)
        self._dummy: torch.Tensor

        self._stats_log = dict()
        self._flops_mode = False
        self.compressing = False

    def preprocess_input(self, im: torch.Tensor):
        """ Shift and scale the input image

        Args:
            im (torch.Tensor): a batch of images, (N, C, H, W), values between (0, 1)
        """
        assert (im.shape[2] % self.max_stride == 0) and (im.shape[3] % self.max_stride == 0)
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im + self.im_shift) * self.im_scale
        return x

    def process_output(self, x: torch.Tensor):
        """ scale the decoder output from range (-1, 1) to (0, 1)

        Args:
            x (torch.Tensor): network decoder output, (N, C, H, W), values between (-1, 1)
        """
        assert not x.requires_grad
        im_hat = x.clone().clamp_(min=-1.0, max=1.0).mul_(0.5).add_(0.5)
        return im_hat

    def preprocess_target(self, im: torch.Tensor):
        """ Shift and scale the image to make it reconstruction target

        Args:
            im (torch.Tensor): a batch of images, (N, C, H, W), values between (0, 1)
        """
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im - 0.5) * 2.0
        return x

    def forward(self, im, return_rec=False):
        """ Forward pass for training

        Args:
            im (tensor): image, (B, 3, H, W)
            return_rec (bool, optional): if True, return the reconstructed image \
                in addition to losses. Defaults to False.

        Returns:
            dict: str -> loss
        """
        # 更新全局训练步数（仅在训练模式下）
        if self.training:
            self.global_training_step += 1

        im = im.to(self._dummy.device)
        x = self.preprocess_input(im)
        x_target = self.preprocess_target(im)

        enc_features = self.encoder(x)
        feature, stats_all = self.decoder(enc_features)
        out_loss, x_hat = self.out_net.forward_loss(feature, x_target)

        if self._flops_mode: # testing flops
            return x_hat

        # ================ Training ================
        nB, imC, imH, imW = im.shape # batch, channel, height, width
        kl_divergences = [stat['kl'].sum(dim=(1, 2, 3)) for stat in stats_all]
        ndims = imC * imH * imW
        kl = sum(kl_divergences) / ndims
        loss = (kl + out_loss).mean(0) # rate + distortion

        # ================ Logging ================
        with torch.no_grad():
            nats_per_dim = kl.detach().cpu().mean(0).item()
            im_hat = self.process_output(x_hat.detach())
            im_mse = tnf.mse_loss(im_hat, im, reduction='mean')
            psnr = -10 * math.log10(im_mse.item())
            # logging
            kls = torch.stack([kl.mean(0) / ndims for kl in kl_divergences], dim=0)
            bpdim = kls * self.log2_e
            mode = 'train' if self.training else 'eval'
            self._stats_log[f'{mode}_bpdim'] = bpdim.tolist()
            self._stats_log[f'{mode}_bppix'] = (bpdim * imC).tolist()
            channel_bpps = [stat['kl'].sum(dim=(2,3)).mean(0).cpu() / (imH * imW) for stat in stats_all]
            self._stats_log[f'{mode}_channels'] = [(bpps*self.log2_e).tolist() for bpps in channel_bpps]

        stats = OrderedDict()
        stats['loss']  = loss
        stats['kl']    = nats_per_dim
        stats[self.out_net.loss_name] = out_loss.detach().cpu().mean(0).item()
        stats['bppix'] = nats_per_dim * self.log2_e * imC
        stats['psnr']  = psnr
        if return_rec:
            stats['im_hat'] = im_hat
        return stats

    @torch.no_grad()
    def forward_eval(self, *args, **kwargs):
        """ a dummy function for evaluation
        """
        return self.forward(*args, **kwargs)

    @torch.no_grad()
    def uncond_sample(self, nhw_repeat, temprature=1.0):
        """ unconditionally sample, ie, generate new images

        Args:
            nhw_repeat (tuple): repeat the initial constant feature n,h,w times
            temprature (float): temprature
        """
        feature = self.decoder.forward_uncond(nhw_repeat, t=temprature)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    @torch.no_grad()
    def cond_sample(self, latents, nhw_repeat=None, temprature=1.0, paint_box=None):
        """ conditional sampling with latents

        Args:
            latents (torch.Tensor): latent variables
            nhw_repeat (tuple): repeat the constant n,h,w times
            temprature (float): temprature
            paint_box (tuple of floats): (x1,y1,x2,y2), in 0-1 range
        """
        feature = self.decoder.forward_with_latents(latents, nhw_repeat, t=temprature, paint_box=paint_box)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    def forward_get_latents(self, im):
        """ forward pass and return all the latent variables
        """
        x = self.preprocess_input(im)
        activations = self.encoder.forward(x)
        _, stats = self.decoder.forward(activations, get_latents=True)
        return stats

    @torch.no_grad()
    def inpaint(self, im, paint_box, steps=1, temprature=1.0):
        """ Inpainting

        Args:
            im (tensor): image (with paint_box mased out)
            paint_box (tuple): (x1, y1, x2, y2)
            steps (int, optional): A larger `step` gives a slightly better result.
            temprature (float, optional): tempreture. Defaults to 1.0.

        Returns:
            tensor: inpainted image
        """
        nB, imC, imH, imW = im.shape
        x1, y1, x2, y2 = paint_box
        h_slice = slice(round(y1*imH), round(y2*imH))
        w_slice = slice(round(x1*imW), round(x2*imW))
        im_input = im.clone()
        for i in range(steps):
            stats_all = self.forward_get_latents(im_input)
            latents = [st['z'] for st in stats_all]
            im_sample = self.cond_sample(latents, temprature=temprature, paint_box=paint_box)
            torch.clamp_(im_sample, min=0, max=1)
            im_input = im.clone()
            im_input[:, :, h_slice, w_slice] = im_sample[:, :, h_slice, w_slice]
        return im_sample

    def compress_mode(self, mode=True):
        """ Prepare for entropy coding. Musted be called before compression.
        """
        if mode:
            self.decoder.update()
            if hasattr(self.out_net, 'compress'):
                self.out_net.update()
        self.compressing = mode

    @torch.no_grad()
    def compress(self, im, fast_mode=False):
        """ compress a batch of images

        Args:
            im (torch.Tensor): a batch of images, (N, C, H, W), values between (0, 1)
            fast_mode (bool): 如果为True，跳过调制/解调过程以加速压缩

        Returns:
            list: [string1, string2, string2, ..., string_N, feature_shape]
        """
        x = self.preprocess_input(im)
        enc_features = self.encoder(x)
        compressed_obj, feature = self.decoder.compress(enc_features, fast_mode)
        min_res = min(enc_features.keys())
        compressed_obj.append(tuple(enc_features[min_res].shape))
        if hasattr(self.out_net, 'compress'): # lossless compression
            x_tgt = self.preprocess_target(im)
            final_str = self.out_net.compress(feature, x_tgt)
            compressed_obj.append(final_str)
        return compressed_obj

    @torch.no_grad()
    def decompress(self, compressed_object):
        """ decompress a compressed_object

        Args:
            compressed_object (list): same as the output of self.compress()

        Returns:
            torch.Tensor: a batch of reconstructed images, (N, C, H, W), values between (0, 1)
        """
        if hasattr(self.out_net, 'compress'): # lossless compression
            feature = self.decoder.decompress(compressed_object[:-1])
            x_hat = self.out_net.decompress(feature, compressed_object[-1])
        else: # lossy compression
            feature = self.decoder.decompress(compressed_object)
            x_hat = self.out_net.mean(feature)
        im_hat = self.process_output(x_hat)
        return im_hat

    @torch.no_grad()
    def compress_file(self, img_path, output_path):
        """ Compress an image file specified by `img_path` and save to `output_path`

        Args:
            img_path    (str): input image path
            output_path (str): output bits path
        """
        # read image
        img = Image.open(img_path)
        img_padded = pad_divisible_by(img, div=self.max_stride)
        device = next(self.parameters()).device
        im = tvf.to_tensor(img_padded).unsqueeze_(0).to(device=device)
        # compress by model
        compressed_obj = self.compress(im)
        compressed_obj.append((img.height, img.width))
        # save bits to file
        with open(output_path, 'wb') as f:
            pickle.dump(compressed_obj, file=f)

    @torch.no_grad()
    def decompress_file(self, bits_path):
        """ Decompress a bits file specified by `bits_path`

        Args:
            bits_path (str): input bits path

        Returns:
            torch.Tensor: reconstructed image
        """
        # read from file
        with open(bits_path, 'rb') as f:
            compressed_obj = pickle.load(file=f)
        img_h, img_w = compressed_obj.pop()
        # decompress by model
        im_hat = self.decompress(compressed_obj)
        return im_hat[:, :, :img_h, :img_w]

    def set_channel_params(self, qam_order=None, snr_db=None):
        """设置信道参数

        Args:
            qam_order (int, optional): QAM调制阶数
            snr_db (float, optional): 信噪比(dB)
        """
        if qam_order is not None:
            self.qam_order = qam_order
            # 更新所有QLatentBlockX的调制器
            for block in self.decoder.dec_blocks:
                if hasattr(block, 'modulator'):
                    block.modulator = SimpleModulator(qam_order)

        if snr_db is not None:
            self.snr_db = snr_db
            # 更新所有QLatentBlockX的信道
            for block in self.decoder.dec_blocks:
                if hasattr(block, 'channel'):
                    block.channel.set_snr(snr_db)

    def get_channel_params(self):
        """获取当前信道参数"""
        return {
            'qam_order': self.qam_order,
            'snr_db': self.snr_db
        }


def pad_divisible_by(img, div=64):
    """ Pad an PIL.Image at right and bottom border \
         such that both sides are divisible by `div`.

    Args:
        img (PIL.Image): image
        div (int, optional): `div`. Defaults to 64.

    Returns:
        PIL.Image: padded image
    """
    h_old, w_old = img.height, img.width
    if (h_old % div == 0) and (w_old % div == 0):
        return img
    h_tgt = round(div * math.ceil(h_old / div))
    w_tgt = round(div * math.ceil(w_old / div))
    # left, top, right, bottom
    padding = (0, 0, (w_tgt - w_old), (h_tgt - h_old))
    padded = tvf.pad(img, padding=padding, padding_mode='edge')
    return padded