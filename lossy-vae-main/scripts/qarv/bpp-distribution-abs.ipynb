{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from PIL import Image\n", "import math\n", "import torch\n", "import torchvision.transforms.functional as tvf\n", "torch.set_grad_enabled(False)\n", "\n", "from lvae import get_model, known_datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["img_dir = known_datasets['kodak']\n", "print(img_dir)\n", "fig_save_path = 'bpp-distribution-abs.pdf'\n", "\n", "def get_bpp_distribution(model: torch.nn.Module):\n", "    device = next(model.parameters()).device\n", "    img_paths = list(img_dir.rglob('*.*'))\n", "    bpps_all = None\n", "    log2_e = math.log2(math.e)\n", "    for impath in img_paths:\n", "        im = tvf.to_tensor(Image.open(impath)).unsqueeze_(0).to(device=device)\n", "        _, stats_all = model.forward_end2end(im, lmb=model.default_lmb)\n", "        nB, imC, imH, imW = im.shape\n", "        npix = float(imH * imW)\n", "        bpps = [stat['kl'].sum() * log2_e / npix for stat in stats_all]\n", "        bpps = torch.stack(bpps)\n", "        bpps_all = bpps if (bpps_all is None) else (bpps_all + bpps)\n", "    bpps_all = bpps_all / len(img_paths)\n", "    return bpps_all\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model('qarv_base', pretrained=True)\n", "\n", "model = model.cuda()\n", "model.eval()\n", "\n", "# lambdas = [16, 32, 64, 128, 256, 512, 1024, 2048]\n", "steps = 15\n", "_loglow, _loghigh = math.log(model.lmb_range[0]), math.log(model.lmb_range[1])\n", "lambdas = torch.linspace(_loglow, _loghigh, steps=steps).exp()\n", "\n", "stats_all = []\n", "for lmb in tqdm(lambdas):\n", "    model.default_lmb = lmb\n", "    bpps = get_bpp_distribution(model)\n", "    stats_all.append(bpps)\n", "stats_all = torch.stack(stats_all, dim=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "data = stats_all.cpu().numpy()\n", "# data = np.flip(data, axis=1) # change the order to Z_N -> Z_1\n", "data_cum = data.cumsum(axis=1)\n", "# category_colors = plt.get_cmap('Set3')\n", "category_colors = plt.get_cmap('tab20')\n", "category_colors = category_colors(np.linspace(0, 1, data.shape[1]))\n", "\n", "fig, ax = plt.subplots(figsize=(13.4, 4.8))\n", "\n", "num_latents = data.shape[1]\n", "labels         = [f'$\\lambda = {lmb:.0f}$' for lmb in lambdas]\n", "category_names = [f'$Z_{{ {i} }}$'         for i   in range(1, num_latents+1)]\n", "for i, (colname, color) in enumerate(zip(category_names, category_colors)):\n", "    widths = data[:, i]\n", "    starts = data_cum[:, i] - widths\n", "    rects = ax.barh(labels, widths, left=starts, height=0.8,\n", "                    label=colname, color=color)\n", "\n", "legend_handles, legend_labels = ax.get_legend_handles_labels()\n", "ax.legend(\n", "    legend_handles[::-1], legend_labels[::-1], ncol=len(category_names), loc='lower left',\n", "    fontsize=12.2, bbox_to_anchor=(0.2, 1.0), handletextpad=0.24\n", ")\n", "# ax.set_title('Distribution of bit rates over latent variables')\n", "ax.tick_params(axis='both', which='major', labelsize=12)\n", "ax.set_ylim(-0.6, len(labels)-0.4)\n", "ax.invert_yaxis()\n", "# ax.yaxis.set_label_position(\"right\")\n", "ax.yaxis.tick_right()\n", "# ax.xaxis.set_visible(False)\n", "ax.set_xticks(np.arange(0, 2.31, 0.1))\n", "ax.set_xlim(0, np.sum(data, axis=1).max()+0.02)\n", "ax.invert_xaxis()\n", "ax.set_xlabel('Bits per pixel (bpp)', fontdict={'size':14})\n", "fig.tight_layout()\n", "# plt.subplots_adjust(left=0.106, right=0.97, bottom=0.1, top=0.94)\n", "plt.subplots_adjust(bottom=0.11, top=0.91)\n", "fig.savefig(fig_save_path)"]}], "metadata": {"interpreter": {"hash": "dd99ce241fc9a98adb16dacebaa58469dd0c84ca3cfa9b25e7e9cb4caa7bb934"}, "kernelspec": {"display_name": "Python 3.9.7 ('pt110env')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}