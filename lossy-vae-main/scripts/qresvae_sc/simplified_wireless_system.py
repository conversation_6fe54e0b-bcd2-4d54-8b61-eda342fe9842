#!/usr/bin/env python3
"""
简化的无线传输系统 - 去掉熵编码
直接对VAE特征进行QAM调制和AWGN传输
"""

import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import sys
import os

# 添加项目路径
sys.path.append('/home/<USER>/LOSSY_VAE/lossy-vae-main')

from lvae.models.qrvesvae_sc.model import HierarchicalVAE
from lvae.models.qrvesvae_sc.zoo import qres34m_sc

class SimplifiedWirelessVAE:
    """简化的无线VAE系统 - 无熵编码版本"""
    
    def __init__(self, snr_db=20):
        self.snr_db = snr_db
        self.snr_linear = 10 ** (snr_db / 10.0)
        
        # 加载预训练的VAE模型
        self.vae_model = qres34m_sc(snr_db=snr_db, verbose=False)
        self.vae_model.eval()
        
        print(f"🚀 简化无线VAE系统初始化完成")
        print(f"📡 SNR设置: {snr_db} dB")
        print(f"🔧 去掉熵编码，直接传输qm特征")
    
    def vae_encode(self, image):
        """VAE编码：图像 → qm特征"""
        with torch.no_grad():
            # 预处理
            x = self.vae_model.preprocess_input(image)
            
            # 编码得到多层特征
            enc_features = self.vae_model.encoder(x)
            
            # 提取qm特征（这里简化为最高分辨率层）
            # 在实际系统中，你可能需要处理多层特征
            max_res = max(enc_features.keys())
            qm_features = enc_features[max_res]
            
            print(f"📊 VAE编码完成: qm形状={qm_features.shape}")
            print(f"📈 qm数值范围: [{qm_features.min():.6f}, {qm_features.max():.6f}]")
            
            return qm_features, enc_features
    
    def qam_modulate(self, qm_features, qam_order=16):
        """QAM调制：qm特征 → 调制符号"""
        # 1. 归一化到[0,1]
        qm_min, qm_max = qm_features.min(), qm_features.max()
        qm_normalized = (qm_features - qm_min) / (qm_max - qm_min + 1e-8)
        
        # 2. 映射到QAM符号索引[0, qam_order-1]
        qam_indices = torch.round(qm_normalized * (qam_order - 1))
        qam_indices = torch.clamp(qam_indices, 0, qam_order - 1)
        
        # 3. 映射到星座点（简化为实数星座）
        constellation_points = torch.linspace(-1, 1, qam_order)
        qam_symbols = constellation_points[qam_indices.long()]
        
        # 4. 功率归一化
        signal_power = torch.mean(qam_symbols ** 2)
        qam_symbols = qam_symbols / torch.sqrt(signal_power)
        
        print(f"📡 QAM调制完成: {qam_order}-QAM")
        print(f"🎯 符号范围: [{qam_symbols.min():.4f}, {qam_symbols.max():.4f}]")
        
        # 返回调制信息，用于解调
        modulation_info = {
            'qm_min': qm_min,
            'qm_max': qm_max,
            'qam_order': qam_order,
            'constellation_points': constellation_points,
            'signal_power': signal_power
        }
        
        return qam_symbols, modulation_info
    
    def awgn_channel(self, qam_symbols):
        """AWGN信道：添加高斯白噪声"""
        # 计算噪声功率
        signal_power = torch.mean(qam_symbols ** 2)
        noise_power = signal_power / self.snr_linear
        noise_std = torch.sqrt(noise_power)
        
        # 添加噪声
        noise = torch.randn_like(qam_symbols) * noise_std
        received_symbols = qam_symbols + noise
        
        print(f"🌊 AWGN信道传输完成")
        print(f"📊 信号功率: {signal_power:.6f}")
        print(f"📉 噪声功率: {noise_power:.6f}")
        print(f"📡 实际SNR: {10*torch.log10(signal_power/noise_power):.2f} dB")
        
        return received_symbols
    
    def qam_demodulate(self, received_symbols, modulation_info):
        """QAM解调：接收符号 → qm特征"""
        # 1. 功率反归一化
        signal_power = modulation_info['signal_power']
        received_symbols = received_symbols * torch.sqrt(signal_power)
        
        # 2. 硬判决：找到最近的星座点
        constellation_points = modulation_info['constellation_points']
        qam_order = modulation_info['qam_order']
        
        # 计算到每个星座点的距离
        distances = torch.abs(received_symbols.unsqueeze(-1) - constellation_points.unsqueeze(0).unsqueeze(0).unsqueeze(0))
        qam_indices = torch.argmin(distances, dim=-1)
        
        # 3. 反归一化到原始qm范围
        qm_normalized = qam_indices.float() / (qam_order - 1)
        qm_min, qm_max = modulation_info['qm_min'], modulation_info['qm_max']
        qm_recovered = qm_normalized * (qm_max - qm_min) + qm_min
        
        print(f"🔄 QAM解调完成")
        print(f"📈 恢复qm范围: [{qm_recovered.min():.6f}, {qm_recovered.max():.6f}]")
        
        return qm_recovered
    
    def vae_decode(self, qm_recovered, enc_features):
        """VAE解码：qm特征 → 重建图像"""
        with torch.no_grad():
            # 替换编码特征中的最高分辨率层
            max_res = max(enc_features.keys())
            enc_features[max_res] = qm_recovered
            
            # VAE解码
            feature = self.vae_model.decoder.decompress_features(enc_features)
            reconstructed = self.vae_model.out_net.mean(feature)
            
            # 后处理
            reconstructed = self.vae_model.process_output(reconstructed)
            
            print(f"🖼️  VAE解码完成: 图像形状={reconstructed.shape}")
            
            return reconstructed
    
    def end_to_end_transmission(self, image, qam_order=16):
        """端到端无线传输"""
        print(f"\n🚀 开始端到端无线传输 (SNR={self.snr_db}dB, {qam_order}-QAM)")
        print("=" * 60)
        
        # 1. VAE编码
        qm_features, enc_features = self.vae_encode(image)
        
        # 2. QAM调制
        qam_symbols, modulation_info = self.qam_modulate(qm_features, qam_order)
        
        # 3. AWGN信道传输
        received_symbols = self.awgn_channel(qam_symbols)
        
        # 4. QAM解调
        qm_recovered = self.qam_demodulate(received_symbols, modulation_info)
        
        # 5. VAE解码
        reconstructed = self.vae_decode(qm_recovered, enc_features)
        
        # 6. 计算PSNR
        mse = torch.mean((image - reconstructed) ** 2)
        psnr = 10 * torch.log10(1.0 / mse) if mse > 0 else float('inf')
        
        print(f"\n📊 传输结果:")
        print(f"MSE: {mse:.6f}")
        print(f"PSNR: {psnr:.2f} dB")
        print("=" * 60)
        
        return reconstructed, psnr.item()

def test_simplified_system():
    """测试简化的无线传输系统"""
    # 创建测试图像
    test_img_array = np.zeros((256, 256, 3), dtype=np.uint8)
    for i in range(256):
        test_img_array[:, i, 0] = i  # 红色渐变
        test_img_array[i, :, 1] = i  # 绿色渐变
    test_img_array[64:192, 64:192, 2] = 255  # 蓝色方块
    
    original_img = Image.fromarray(test_img_array)
    img_tensor = torch.from_numpy(np.array(original_img)).float().permute(2, 0, 1).unsqueeze(0) / 255.0
    
    # 测试不同SNR
    snr_values = [20, 10, 0, -10]
    results = []
    
    for snr in snr_values:
        system = SimplifiedWirelessVAE(snr_db=snr)
        reconstructed, psnr = system.end_to_end_transmission(img_tensor)
        results.append({'snr': snr, 'psnr': psnr})
    
    print(f"\n🎯 不同SNR下的PSNR对比:")
    print("-" * 30)
    for result in results:
        print(f"SNR: {result['snr']:3d} dB → PSNR: {result['psnr']:6.2f} dB")

if __name__ == "__main__":
    test_simplified_system()
