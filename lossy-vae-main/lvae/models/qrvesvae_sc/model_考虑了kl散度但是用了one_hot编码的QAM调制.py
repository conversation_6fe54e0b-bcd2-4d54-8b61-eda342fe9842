import pickle
from collections import OrderedDict
from PIL import Image
import math
import torch
import torch.nn as nn
import torch.nn.functional as tnf
import torch.distributions as td
import torchvision.transforms.functional as tvf
from compressai.entropy_models import GaussianConditional

import lvae.models.common as common
from lvae.models.entropy_coding import gaussian_log_prob_mass


class GaussianNLLOutputNet(nn.Module):
    def __init__(self, conv_mean, conv_scale, bin_size=1/127.5):
        super().__init__()
        self.conv_mean  = conv_mean
        self.conv_scale = conv_scale
        self.bin_size = bin_size
        self.loss_name = 'nll'

    def forward_loss(self, feature, x_tgt):
        """ compute negative log-likelihood loss

        Args:
            feature (torch.Tensor): feature given by the top-down decoder
            x_tgt (torch.Tensor): original image
        """
        feature = feature.float()
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_logscale = tnf.softplus(p_logscale + 16) - 16 # logscale lowerbound
        log_prob = gaussian_log_prob_mass(p_mean, torch.exp(p_logscale), x_tgt, bin_size=self.bin_size)
        assert log_prob.shape == x_tgt.shape
        nll = -log_prob.mean(dim=(1,2,3)) # BCHW -> (B,)
        return nll, p_mean

    def mean(self, feature):
        p_mean = self.conv_mean(feature)
        return p_mean

    def sample(self, feature, mode='continuous', temprature=None):
        p_mean = self.conv_mean(feature)
        p_logscale = self.conv_scale(feature)
        p_scale = torch.exp(p_logscale)
        if temprature is not None:
            p_scale = p_scale * temprature

        if mode == 'continuous':
            samples = p_mean + p_scale * torch.randn_like(p_mean)
        elif mode == 'discrete':
            raise NotImplementedError()
        else:
            raise ValueError()
        return samples

    def update(self):
        self.discrete_gaussian = GaussianConditional(None, scale_bound=0.11)
        device = next(self.parameters()).device
        self.discrete_gaussian = self.discrete_gaussian.to(device=device)
        lower = self.discrete_gaussian.lower_bound_scale.bound.item()
        max_scale = 20
        scale_table = torch.exp(torch.linspace(math.log(lower), math.log(max_scale), steps=128))
        updated = self.discrete_gaussian.update_scale_table(scale_table)
        self.discrete_gaussian.update()

    def _preapre_codec(self, feature, x=None):
        assert not feature.requires_grad
        pm = self.conv_mean(feature)
        pm = torch.round(pm * 127.5 + 127.5) / 127.5 - 1 # workaround to make sure lossless
        plogv = self.conv_scale(feature)
        # scale (-1,1) range to (-127.5, 127.5) range
        pm = pm / self.bin_size
        plogv = plogv - math.log(self.bin_size)
        if x is not None:
            x = x / self.bin_size
        return pm, plogv, x

    def compress(self, feature, x):
        pm, plogv, x = self._preapre_codec(feature, x)
        # compress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        strings = self.discrete_gaussian.compress(x, indexes, means=pm)
        return strings

    def decompress(self, feature, strings):
        pm, plogv, _ = self._preapre_codec(feature)
        # decompress
        indexes = self.discrete_gaussian.build_indexes(torch.exp(plogv))
        x_hat = self.discrete_gaussian.decompress(strings, indexes, means=pm)
        x_hat = x_hat * self.bin_size
        return x_hat


class MSEOutputNet(nn.Module):
    def __init__(self, mse_lmb):
        super().__init__()
        self.mse_lmb = float(mse_lmb)
        self.loss_name = 'mse'

    def forward_loss(self, x_hat, x_tgt):
        """ compute MSE loss

        Args:
            x_hat (torch.Tensor): reconstructed image
            x_tgt (torch.Tensor): original image
        """
        assert x_hat.shape == x_tgt.shape
        mse = tnf.mse_loss(x_hat, x_tgt, reduction='none').mean(dim=(1,2,3)) # (B,3,H,W) -> (B,)
        loss = mse * self.mse_lmb
        return loss, x_hat

    def mean(self, x_hat, temprature=None):
        return x_hat
    sample = mean


class VDBlock(nn.Module):
    """ Adapted from VDVAE (https://github.com/openai/vdvae)
    - Paper: Very Deep VAEs Generalize Autoregressive Models and Can Outperform Them on Images
    - arxiv: https://arxiv.org/abs/2011.10650
    """
    def __init__(self, in_ch, hidden_ch=None, out_ch=None, residual=True,
                 use_3x3=True, zero_last=False):
        super().__init__()
        out_ch = out_ch or in_ch
        hidden_ch = hidden_ch or round(in_ch * 0.25)
        self.in_channels = in_ch
        self.out_channels = out_ch
        self.residual = residual
        self.c1 = common.conv_k1s1(in_ch, hidden_ch)
        self.c2 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c3 = common.conv_k3s1(hidden_ch, hidden_ch) if use_3x3 else common.conv_k1s1(hidden_ch, hidden_ch)
        self.c4 = common.conv_k1s1(hidden_ch, out_ch, zero_weights=zero_last)

    def residual_scaling(self, N):
        # This residual scaling improves stability and performance with many layers
        # https://arxiv.org/pdf/2011.10650.pdf, Appendix Table 3
        self.c4.weight.data.mul_(math.sqrt(1 / N))

    def forward(self, x):
        xhat = self.c1(tnf.gelu(x))
        xhat = self.c2(tnf.gelu(xhat))
        xhat = self.c3(tnf.gelu(xhat))
        xhat = self.c4(tnf.gelu(xhat))
        out = (x + xhat) if self.residual else xhat
        return out

class VDBlockPatchDown(VDBlock):
    def __init__(self, in_ch, out_ch, down_rate=2):
        super().__init__(in_ch, residual=True)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


from timm.models.convnext import ConvNeXtBlock
class MyConvNeXtBlock(ConvNeXtBlock):
    def __init__(self, dim, mlp_ratio=2, **kwargs):
        super().__init__(dim, mlp_ratio=mlp_ratio, **kwargs)
        self.norm.affine = True # this variable is useless. just a workaround for flops computation

    def forward(self, x):
        shortcut = x
        x = self.conv_dw(x)
        if self.use_conv_mlp:
            x = self.norm(x)
            x = self.mlp(x)
        else:
            x = x.permute(0, 2, 3, 1).contiguous()
            x = self.norm(x)
            x = self.mlp(x)
            x = x.permute(0, 3, 1, 2).contiguous()
        if self.gamma is not None:
            x = x.mul(self.gamma.reshape(1, -1, 1, 1))
        x = self.drop_path(x) + shortcut
        return x

class MyConvNeXtPatchDown(MyConvNeXtBlock):
    def __init__(self, in_ch, out_ch, down_rate=2, mlp_ratio=2, kernel_size=7):
        super().__init__(in_ch, mlp_ratio=mlp_ratio, kernel_size=kernel_size)
        self.downsapmle = common.patch_downsample(in_ch, out_ch, rate=down_rate)

    def forward(self, x):
        x = super().forward(x)
        out = self.downsapmle(x)
        return out


class STEFunction(torch.autograd.Function):
    @staticmethod
    def forward(ctx, input_for_grad, discrete_output):
        # input_for_grad: e.g., noisy_symbols (N,), complex
        # discrete_output: e.g., recovered_one_hot_iq (N, 2, 4), real
        ctx.input_shape = input_for_grad.shape
        ctx.input_dtype = input_for_grad.dtype # Save dtype
        return discrete_output

    @staticmethod
    def backward(ctx, grad_output):
        # grad_output: gradient w.r.t. discrete_output, shape (N, 2, 4), real
        # Expected gradient: for input_for_grad, shape (N,), complex

        # Heuristic to match shape and type:
        # Sum gradients from the one-hot representation.
        reduced_grad_real_part = torch.sum(grad_output, dim=(1, 2)) # Shape (N,), real

        # Construct gradient compatible with input_for_grad
        if ctx.input_dtype.is_complex:
            # Create a complex gradient with the summed real part and zero imaginary part.
            # This is a strong simplification.
            final_grad = torch.complex(reduced_grad_real_part, torch.zeros_like(reduced_grad_real_part))
        elif ctx.input_dtype == reduced_grad_real_part.dtype: # If input was real and compatible
             final_grad = reduced_grad_real_part
        else: # Fallback if types are mismatched in an unexpected way (e.g. input real float64, reduced_grad float32)
            final_grad = reduced_grad_real_part.to(ctx.input_dtype)


        # Ensure the shape is absolutely correct.
        if final_grad.shape != ctx.input_shape:
            # This case should ideally not be hit if input_shape is (N,) and reduced_grad is (N,)
            # but as a safeguard:
            print(f"Warning: STEFunction gradient shape mismatch. Got {final_grad.shape}, expected {ctx.input_shape}. Returning zeros.")
            return torch.zeros(ctx.input_shape, dtype=ctx.input_dtype, device=grad_output.device), None
            
        return final_grad, None


class QAMChannelSimulation(nn.Module):
    def __init__(self, z_dim_of_layer: int, snr_db: float = 20.0, qam_bits: int = 4, gumbel_tau: float = 1.0):
        super().__init__()
        self.snr_db = snr_db
        assert qam_bits == 4, "Currently only 16-QAM (qam_bits=4) is supported."
        self.qam_bits = qam_bits 
        self.num_pam_levels = 2**(self.qam_bits // 2) 
        self.gumbel_tau = gumbel_tau
        self.z_dim_of_layer = z_dim_of_layer

        self.internal_qam_features = 2 * self.num_pam_levels
        self.input_adapter = nn.Conv2d(self.z_dim_of_layer, self.internal_qam_features, kernel_size=1)
        self.output_adapter = nn.Conv2d(self.internal_qam_features, self.z_dim_of_layer, kernel_size=1)

        self._qam_map_vals = torch.tensor([-3, -1, 3, 1], dtype=torch.float32)
        self._qam_normalization = torch.sqrt(torch.tensor(10.0, dtype=torch.float32)) 

        constellation = []
        bit_patterns = [] 
        pam_idx_to_bits_map = {0: (0,0), 1: (0,1), 2: (1,1), 3: (1,0)}

        for i_idx in range(self.num_pam_levels): 
            for q_idx in range(self.num_pam_levels): 
                real_part = self._qam_map_vals[i_idx]
                imag_part = self._qam_map_vals[q_idx]
                constellation.append(torch.complex(real_part, imag_part) / self._qam_normalization)
                bits_i_hi, bits_i_lo = pam_idx_to_bits_map[i_idx]
                bits_q_hi, bits_q_lo = pam_idx_to_bits_map[q_idx]
                bit_patterns.append(torch.tensor([bits_i_hi, bits_i_lo, bits_q_hi, bits_q_lo], dtype=torch.uint8))

        self.register_buffer("_constellation_points", torch.stack(constellation)) 
        self.register_buffer("_constellation_bits", torch.stack(bit_patterns)) 

    def _pam_idx_to_bits_tensor(self, pam_indices):
        assert self.num_pam_levels == 4, "This specific Gray unmapping is for num_pam_levels=4"
        bits_high = torch.zeros_like(pam_indices, dtype=torch.uint8, device=pam_indices.device)
        bits_low  = torch.zeros_like(pam_indices, dtype=torch.uint8, device=pam_indices.device)
        bits_high[pam_indices == 2] = 1
        bits_high[pam_indices == 3] = 1
        bits_low[pam_indices == 1] = 1
        bits_low[pam_indices == 2] = 1
        return torch.stack([bits_high, bits_low], dim=-1)

    def _bits_to_pam_idx_tensor(self, bits_pairs):
        assert self.num_pam_levels == 4, "This specific Gray mapping is for num_pam_levels=4"
        pam_idx = torch.zeros(bits_pairs.shape[0], dtype=torch.long, device=bits_pairs.device)
        pam_idx[(bits_pairs[:,0]==0) & (bits_pairs[:,1]==0)] = 0
        pam_idx[(bits_pairs[:,0]==0) & (bits_pairs[:,1]==1)] = 1
        pam_idx[(bits_pairs[:,0]==1) & (bits_pairs[:,1]==1)] = 2
        pam_idx[(bits_pairs[:,0]==1) & (bits_pairs[:,1]==0)] = 3
        return pam_idx

    def _one_hot_to_bits(self, one_hot_iq):
        pam_indices_i = torch.argmax(one_hot_iq[:, 0, :], dim=1) 
        pam_indices_q = torch.argmax(one_hot_iq[:, 1, :], dim=1) 
        bits_i = self._pam_idx_to_bits_tensor(pam_indices_i) 
        bits_q = self._pam_idx_to_bits_tensor(pam_indices_q) 
        return torch.cat([bits_i, bits_q], dim=1) 

    def _bits_to_one_hot(self, bits_grouped):
        bits_i = bits_grouped[:, 0:2] 
        bits_q = bits_grouped[:, 2:4] 
        pam_indices_i = self._bits_to_pam_idx_tensor(bits_i) 
        pam_indices_q = self._bits_to_pam_idx_tensor(bits_q) 
        one_hot_i = tnf.one_hot(pam_indices_i, num_classes=self.num_pam_levels).float() 
        one_hot_q = tnf.one_hot(pam_indices_q, num_classes=self.num_pam_levels).float() 
        return torch.stack([one_hot_i, one_hot_q], dim=1) 

    def _generate_one_hot_symbols(self, z_input_for_qam):
        logits_feature_map = self.input_adapter(z_input_for_qam)
        B, _, H, W = logits_feature_map.shape
        logits_iq_flat = logits_feature_map.permute(0, 2, 3, 1).reshape(-1, 2, self.num_pam_levels)
        one_hot_symbols_iq = tnf.gumbel_softmax(logits_iq_flat, tau=self.gumbel_tau, hard=True, dim=-1)
        padding_info = {"original_z_shape": z_input_for_qam.shape, "batch_size": B}
        return one_hot_symbols_iq, padding_info

    def _one_hot_to_qam_symbols(self, one_hot_symbols_iq):
        one_hot_i = one_hot_symbols_iq[:, 0, :] 
        one_hot_q = one_hot_symbols_iq[:, 1, :] 
        pam_levels_tensor = self._qam_map_vals.to(one_hot_i.device) 
        real_part = torch.sum(one_hot_i * pam_levels_tensor.unsqueeze(0), dim=1)
        imag_part = torch.sum(one_hot_q * pam_levels_tensor.unsqueeze(0), dim=1)
        qam_symbols = torch.complex(real_part, imag_part) / self._qam_normalization
        return qam_symbols

    def _add_awgn(self, qam_symbols):
        signal_power = torch.mean(torch.abs(qam_symbols)**2)
        snr_linear = 10**(self.snr_db / 10.0)
        noise_power_total = signal_power / snr_linear if snr_linear > 0 else signal_power
        noise_power_total = torch.max(noise_power_total, torch.tensor(0.0, device=qam_symbols.device))
        noise_std_per_dim = torch.sqrt(noise_power_total / 2.0)
        
        if noise_std_per_dim < 1e-10: 
            effective_signal_power = torch.mean(torch.abs(self._constellation_points)**2) 
            fallback_noise_power_total = effective_signal_power / snr_linear if snr_linear > 0 else effective_signal_power
            fallback_noise_power_total = torch.max(fallback_noise_power_total, torch.tensor(0.0, device=qam_symbols.device))
            noise_std_per_dim = torch.sqrt(fallback_noise_power_total / 2.0)
            if noise_std_per_dim < 1e-10: 
                 noise_std_per_dim = torch.tensor(1e-5, device=qam_symbols.device)

        noise_real = torch.randn_like(qam_symbols.real) * noise_std_per_dim
        noise_imag = torch.randn_like(qam_symbols.imag) * noise_std_per_dim
        noise = torch.complex(noise_real, noise_imag)
        return qam_symbols + noise

    def _demodulate_qam_to_one_hot(self, noisy_symbols):
        noisy_symbols_expanded = noisy_symbols.unsqueeze(1) 
        constellation_expanded = self._constellation_points.unsqueeze(0) 
        distances_sq = torch.abs(noisy_symbols_expanded - constellation_expanded)**2 
        indices_of_nearest_symbol = torch.argmin(distances_sq, dim=1) 
        recovered_bits_grouped = self._constellation_bits[indices_of_nearest_symbol] 
        recovered_one_hot_iq = self._bits_to_one_hot(recovered_bits_grouped) 
        return STEFunction.apply(noisy_symbols, recovered_one_hot_iq)

    def _one_hot_to_latent_output(self, one_hot_iq, original_z_shape_for_dims_and_target_C):
        B, target_C, H, W = original_z_shape_for_dims_and_target_C
        feature_map_internal_channels_flat = one_hot_iq.reshape(B, H, W, self.internal_qam_features)
        feature_map_internal_channels = feature_map_internal_channels_flat.permute(0, 3, 1, 2).contiguous()
        z_channel_output = self.output_adapter(feature_map_internal_channels) 
        return z_channel_output.float()

    def forward(self, z_input):
        original_z_shape = z_input.shape 
        original_one_hot_iq, padding_info = self._generate_one_hot_symbols(z_input)
        qam_symbols = self._one_hot_to_qam_symbols(original_one_hot_iq)
        noisy_symbols = self._add_awgn(qam_symbols)
        recovered_one_hot_iq = self._demodulate_qam_to_one_hot(noisy_symbols)
        z_channel_output = self._one_hot_to_latent_output(recovered_one_hot_iq, original_z_shape)
        target_bits_grouped = self._one_hot_to_bits(original_one_hot_iq.detach()) 
        input_bits_for_loss = self._one_hot_to_bits(recovered_one_hot_iq) 
        comm_bce_loss = tnf.binary_cross_entropy(
            input=input_bits_for_loss.float(), 
            target=target_bits_grouped.float(),
            reduction='mean'
        )
        return z_channel_output, comm_bce_loss, original_one_hot_iq, padding_info

    def demodulate_dequantize_for_decompress(self, received_clean_one_hot_iq, target_z_shape_with_z_dim, padding_info):
        qam_symbols = self._one_hot_to_qam_symbols(received_clean_one_hot_iq)
        noisy_symbols = self._add_awgn(qam_symbols)
        demodulated_one_hot_iq = self._demodulate_qam_to_one_hot(noisy_symbols)
        z_hat = self._one_hot_to_latent_output(demodulated_one_hot_iq, target_z_shape_with_z_dim)
        return z_hat


class BottomUpEncoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.enc_blocks = nn.ModuleList(blocks)

    def forward(self, x):
        feature = x
        enc_features = dict()
        for i, block in enumerate(self.enc_blocks):
            feature = block(feature)
            res = int(feature.shape[2])
            enc_features[res] = feature
        return enc_features


class QLatentBlockX(nn.Module):
    """ Latent block as described in the paper.
    """
    def __init__(self, width, zdim, enc_width=None, kernel_size=7, snr_db=20.0):
        """
        Args:
            width       (int): number of feature channels
            zdim        (int): number of latent variable channels for this specific layer.
            enc_width   (int, optional): number of encoder feature channels. \\
                Defaults to `width` if not provided.
            kernel_size (int, optional): convolution kernel size. Defaults to 7.
            snr_db      (float, optional): SNR in dB for QAM channel simulation. Defaults to 20.0.
        """
        super().__init__()
        self.in_channels  = width
        self.out_channels = width

        enc_width = enc_width or width
        hidden = int(max(width, enc_width) * 0.25)
        concat_ch = (width * 2) if enc_width is None else (width + enc_width)
        use_3x3 = (kernel_size >= 3)
        self.resnet_front = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.resnet_end   = MyConvNeXtBlock(width, kernel_size=kernel_size)
        self.posterior = VDBlock(concat_ch, hidden, zdim, residual=False, use_3x3=use_3x3) 
        self.prior     = VDBlock(width, hidden, zdim * 2, residual=False, use_3x3=use_3x3,
                                 zero_last=True)
        self.z_proj = nn.Sequential(
            common.conv_k3s1(zdim, hidden//2) if use_3x3 else common.conv_k1s1(zdim, hidden//2),
            nn.GELU(),
            common.conv_k1s1(hidden//2, width),
        )
        self.discrete_gaussian = GaussianConditional(None) 
        self.qam_channel = QAMChannelSimulation(z_dim_of_layer=zdim, snr_db=snr_db, qam_bits=4, gumbel_tau=1.0)

    def residual_scaling(self, N):
        self.z_proj[2].weight.data.mul_(math.sqrt(1 / 3*N))

    def transform_prior(self, feature):
        """ prior p(z_i | z_<i) """
        feature = self.resnet_front(feature)
        pm, plogv = self.prior(feature).chunk(2, dim=1)
        plogv = tnf.softplus(plogv + 2.3) - 2.3 
        return feature, pm, plogv

    def forward_train(self, feature, enc_feature, get_latents=False):
        """ Training mode. Forward pass and compute KL. """
        feature_transformed_prior, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
        qm = self.posterior(torch.cat([feature_transformed_prior, enc_feature], dim=1)) 
        
        kl_dict = {}
        z_sample_continuous_for_kl = qm + torch.empty_like(qm).uniform_(-0.5, 0.5) 
        
        log_prob_vae = gaussian_log_prob_mass(pm, pv, x=z_sample_continuous_for_kl, bin_size=1.0, prob_clamp=1e-6)
        kl_dict['kl_vae'] = -1.0 * log_prob_vae
            
        z_channel_output, comm_bce_loss, original_quantized_repr, _ = self.qam_channel(z_sample_continuous_for_kl)
        kl_dict['comm_bce'] = comm_bce_loss
            
        final_z_for_decoder = z_channel_output 

        feature_out = feature_transformed_prior + self.z_proj(final_z_for_decoder)
        feature_out = self.resnet_end(feature_out)
        
        if get_latents:
            return feature_out, dict(z=final_z_for_decoder.detach(), kl_dict=kl_dict, original_quantized_repr=original_quantized_repr.detach())
        return feature_out, dict(kl_dict=kl_dict)


    def forward_uncond(self, feature, t=1.0, latent=None, paint_box=None):
        """ Sampling mode. """
        feature, pm, plogv = self.transform_prior(feature)
        pv = torch.exp(plogv)
        pv = pv * t 
        if latent is None: 
            z_prior_sample = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
        elif paint_box is not None: 
            nB, zC, zH, zW = latent.shape
            if min(zH, zW) == 1: 
                z_prior_sample = latent
            else:
                x1, y1, x2, y2 = paint_box
                h_slice = slice(round(y1*zH), round(y2*zH))
                w_slice = slice(round(x1*zW), round(x2*zW))
                z_sample_patch_from_prior = pm + pv * torch.randn_like(pm) + torch.empty_like(pm).uniform_(-0.5, 0.5) * t
                z_patch = z_sample_patch_from_prior[:, :, h_slice, w_slice]
                z_prior_sample = torch.clone(latent)
                z_prior_sample[:, :, h_slice, w_slice] = z_patch
        else: 
            assert pm.shape == latent.shape
            z_prior_sample = latent
        
        # For unconditional sampling, if channel effects are desired on generated samples:
        # Option 1: Pass through QAM channel simulation
        # final_z, _, _, _ = self.qam_channel(z_prior_sample)
        # Option 2: Use prior sample directly (no channel effect for pure generation)
        final_z = z_prior_sample

        feature = feature + self.z_proj(final_z)
        feature = self.resnet_end(feature)
        return feature

    def update(self):
        """ Prepare for entropy coding. """
        min_scale = 0.1
        max_scale = 20
        log_scales = torch.linspace(math.log(min_scale), math.log(max_scale), steps=64, device=self.prior.c1.weight.device)
        scale_table = torch.exp(log_scales)
        self.discrete_gaussian.update_scale_table(scale_table) 
        self.discrete_gaussian.update()

    def compress(self, feature, enc_feature):
        """ Forward pass, compression (encoding) mode. """
        feature_transformed, pm, plogv = self.transform_prior(feature) 
        qm = self.posterior(torch.cat([feature_transformed, enc_feature], dim=1)) 
        
        original_one_hot_iq, padding_info = self.qam_channel._generate_one_hot_symbols(qm)
        bits_grouped_for_storage = self.qam_channel._one_hot_to_bits(original_one_hot_iq)
        
        # Use original qm.shape for target shape, as this is what z_proj expects eventually
        z_hat_quantized_only = self.qam_channel._one_hot_to_latent_output(
            original_one_hot_iq, 
            qm.shape 
        )

        feature_out = feature_transformed + self.z_proj(z_hat_quantized_only)
        feature_out = self.resnet_end(feature_out)
        
        compressed_data = {
            "bits_grouped": bits_grouped_for_storage.cpu(), 
            "padding_info": padding_info, 
            "original_z_shape": qm.shape 
        }
        return feature_out, compressed_data

    def decompress(self, feature, strings):
        """ Forward pass, decompression (decoding) mode. """
        feature_transformed, pm, plogv = self.transform_prior(feature)
        
        received_bits_grouped = strings["bits_grouped"].to(feature.device) 
        padding_info = strings["padding_info"] 
        original_qm_shape = strings["original_z_shape"] # This has the correct z_dim_of_layer for this block

        received_clean_one_hot_iq = self.qam_channel._bits_to_one_hot(received_bits_grouped)
        
        z_hat_from_channel = self.qam_channel.demodulate_dequantize_for_decompress(
            received_clean_one_hot_iq, 
            original_qm_shape, # Pass original qm_shape which contains the target z_dim for this layer
            padding_info 
        )
        
        feature_out = feature_transformed + self.z_proj(z_hat_from_channel)
        feature_out = self.resnet_end(feature_out)
        return feature_out


class TopDownDecoder(nn.Module):
    def __init__(self, blocks):
        super().__init__()
        self.dec_blocks = nn.ModuleList(blocks)

        width = self.dec_blocks[0].in_channels
        self.bias = nn.Parameter(torch.zeros(1, width, 1, 1))

        self._init_weights()

    def _init_weights(self):
        total_blocks = len([1 for b in self.dec_blocks if hasattr(b, 'residual_scaling')])
        for block in self.dec_blocks:
            if hasattr(block, 'residual_scaling'):
                block.residual_scaling(total_blocks)

    def forward(self, enc_features, get_latents=False):
        stats = []
        min_res = min(enc_features.keys())
        enc_min_res_feature = enc_features[min_res]
        B, _, H_min, W_min = enc_min_res_feature.shape
        feature = self.bias.expand(B, -1, H_min, W_min) 

        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_train'):
                res = int(feature.shape[2]) 
                if res not in enc_features:
                     raise KeyError(f"Encoder feature for resolution {res} not found. Available: {list(enc_features.keys())}")
                f_enc = enc_features[res]
                feature, block_stats = block.forward_train(feature, f_enc, get_latents=get_latents)
                stats.append(block_stats)
            else: 
                feature = block(feature)
        return feature, stats

    def forward_uncond(self, nhw_repeat=(1, 1, 1), t=1.0):
        nB, nH, nW = nhw_repeat
        feature = self.bias.expand(nB, -1, nH, nW)
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'):
                feature = block.forward_uncond(feature, t)
            else:
                feature = block(feature)
        return feature

    def forward_with_latents(self, latents, nhw_repeat=None, t=1.0, paint_box=None):
        if nhw_repeat is None:
            if latents and latents[0] is not None:
                 nB, _, nH, nW = latents[0].shape 
            else: 
                nB, nH, nW = (1,1,1) if nhw_repeat is None else nhw_repeat
            feature = self.bias.expand(nB, -1, nH, nW)
        else: 
            nB, nH, nW = nhw_repeat
            feature = self.bias.expand(nB, -1, nH, nW)

        idx = 0
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'forward_uncond'): 
                current_latent = latents[idx] if latents and idx < len(latents) else None
                feature = block.forward_uncond(feature, t, latent=current_latent, paint_box=paint_box)
                idx += 1
            else: 
                feature = block(feature)
        return feature

    def update(self):
        for block in self.dec_blocks:
            if hasattr(block, 'update'):
                block.update()

    def compress(self, enc_features):
        min_res = min(enc_features.keys())
        enc_min_res_feature = enc_features[min_res]
        B, _, H_min, W_min = enc_min_res_feature.shape
        feature = self.bias.expand(B, -1, H_min, W_min) 
        
        compressed_data_all_layers = [] 
        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'compress'): 
                res = feature.shape[2]
                f_enc = enc_features[res]
                feature, compressed_data_dict_from_block = block.compress(feature, f_enc)
                compressed_data_all_layers.append(compressed_data_dict_from_block) 
            else: 
                feature = block(feature)
        return compressed_data_all_layers, feature

    def decompress(self, compressed_object: list):
        smallest_shape_info = compressed_object[-1] 
        B_comp, _, H_comp, W_comp = smallest_shape_info 
        feature = self.bias.expand(B_comp, -1, H_comp, W_comp)
        
        num_latent_layers_compressed_data = len(compressed_object) - 1 
        data_idx = 0 

        for i, block in enumerate(self.dec_blocks):
            if hasattr(block, 'decompress'): 
                if data_idx < num_latent_layers_compressed_data:
                    compressed_data_dict_for_block = compressed_object[data_idx]
                    data_idx += 1
                    feature = block.decompress(feature, compressed_data_dict_for_block)
                else:
                    raise ValueError("Mismatch: More decompressible blocks than available compressed data items.")
            else: 
                feature = block(feature)
        
        if data_idx != num_latent_layers_compressed_data:
            raise ValueError(f"Mismatch in consumed compressed data for decoder: consumed items = {data_idx}, available items = {num_latent_layers_compressed_data}")
            
        return feature


class HierarchicalVAE(nn.Module):
    """ Class of general hierarchical VAEs
    """
    log2_e = math.log2(math.e)

    def __init__(self, config: dict):
        """ Initialize model """
        super().__init__()
        self.encoder = BottomUpEncoder(blocks=config.pop('enc_blocks'))
        self.decoder = TopDownDecoder(blocks=config.pop('dec_blocks'))
        self.out_net = config.pop('out_net')

        self.im_shift = float(config['im_shift'])
        self.im_scale = float(config['im_scale'])
        self.max_stride = config['max_stride']

        self.register_buffer('_dummy', torch.zeros(1), persistent=False)
        self._dummy: torch.Tensor

        self._stats_log = dict()
        self._flops_mode = False
        self.compressing = False

    def preprocess_input(self, im: torch.Tensor):
        """ Shift and scale the input image """
        assert (im.shape[2] % self.max_stride == 0) and (im.shape[3] % self.max_stride == 0)
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im + self.im_shift) * self.im_scale
        return x

    def process_output(self, x: torch.Tensor):
        """ scale the decoder output from range (-1, 1) to (0, 1) """
        assert not x.requires_grad
        im_hat = x.clone().clamp_(min=-1.0, max=1.0).mul_(0.5).add_(0.5)
        return im_hat

    def preprocess_target(self, im: torch.Tensor):
        """ Shift and scale the image to make it reconstruction target """
        if not self._flops_mode:
            assert (im.dim() == 4) and (0 <= im.min() <= im.max() <= 1) and not im.requires_grad
        x = (im - 0.5) * 2.0
        return x

    def forward(self, im, return_rec=False):
        """ Forward pass for training """
        im = im.to(self._dummy.device)
        x = self.preprocess_input(im)
        x_target = self.preprocess_target(im)

        enc_features = self.encoder(x)
        feature, stats_all = self.decoder(enc_features, get_latents=self._flops_mode) 
        out_loss, x_hat = self.out_net.forward_loss(feature, x_target)

        if self._flops_mode: 
            return x_hat

        nB, imC, imH, imW = im.shape 
        ndims = float(imC * imH * imW) 

        kl_vae_all_layers = []
        comm_bce_all_layers = []
        for stat_block_output in stats_all: 
            kl_d = stat_block_output['kl_dict'] 
            kl_vae_all_layers.append(kl_d['kl_vae'].sum(dim=(1, 2, 3))) 
            comm_bce_all_layers.append(kl_d['comm_bce']) 
            
        kl_vae_total_nats = sum(kl_vae_all_layers) 
        kl_vae_nats_per_dim = kl_vae_total_nats / ndims 

        num_latent_layers = len(comm_bce_all_layers)
        comm_bce_total = sum(comm_bce_all_layers) if num_latent_layers > 0 else torch.tensor(0.0, device=im.device, dtype=torch.float) 
        comm_bce_total = comm_bce_total.float()
        comm_bce_avg_per_layer = comm_bce_total / num_latent_layers if num_latent_layers > 0 else torch.tensor(0.0, device=im.device, dtype=torch.float)
        
        loss = (kl_vae_nats_per_dim + comm_bce_total + out_loss).mean(0) 

        with torch.no_grad():
            im_hat = self.process_output(x_hat.detach())
            im_mse = tnf.mse_loss(im_hat, im, reduction='mean')
            psnr = -10 * math.log10(im_mse.item() if im_mse.item() > 0 else 1e-10) 
            
            mode = 'train' if self.training else 'eval'

            kl_vae_per_layer_bpdim = [(kl_sum_spatial.mean(0) / ndims * self.log2_e).item() for kl_sum_spatial in kl_vae_all_layers]
            self._stats_log[f'{mode}_kl_vae_bpdim_layers'] = kl_vae_per_layer_bpdim

            comm_bce_per_layer_logged = [cbl.mean().item() for cbl in comm_bce_all_layers if hasattr(cbl, 'mean')] 
            self._stats_log[f'{mode}_comm_bce_layers'] = comm_bce_per_layer_logged
            
            total_kl_vae_bpdim = kl_vae_nats_per_dim.mean(0).item() * self.log2_e
            self._stats_log[f'{mode}_kl_vae_bpdim_total'] = total_kl_vae_bpdim
            self._stats_log[f'{mode}_comm_bce_total'] = comm_bce_total.mean(0).item() if hasattr(comm_bce_total, 'mean') else comm_bce_total.item()
            self._stats_log[f'{mode}_comm_bce_avg_layer'] = comm_bce_avg_per_layer.mean(0).item() if hasattr(comm_bce_avg_per_layer, 'mean') else comm_bce_avg_per_layer.item()

            self._stats_log[f'{mode}_bppix'] = total_kl_vae_bpdim 
            
            try:
                channel_bpps_vae = []
                for stat_block_output in stats_all:
                    # Ensure 'kl_vae' exists and is a tensor before trying to access its ndim
                    if 'kl_dict' in stat_block_output and 'kl_vae' in stat_block_output['kl_dict']:
                        kl_vae_unsummed = stat_block_output['kl_dict']['kl_vae'] 
                        if isinstance(kl_vae_unsummed, torch.Tensor) and kl_vae_unsummed.ndim == 4: 
                            bpps_c = kl_vae_unsummed.sum(dim=(2,3)).mean(0).cpu() / (imH * imW) 
                            channel_bpps_vae.append((bpps_c * self.log2_e).tolist())
                if channel_bpps_vae:
                     self._stats_log[f'{mode}_channels_kl_vae'] = channel_bpps_vae
            except Exception:
                pass 

        stats = OrderedDict()
        stats['loss']  = loss 
        stats['kl_vae']    = kl_vae_nats_per_dim.mean(0).item() 
        stats['comm_bce'] = comm_bce_total.mean(0).item() if hasattr(comm_bce_total, 'mean') else comm_bce_total.item()
        stats[self.out_net.loss_name] = out_loss.detach().cpu().mean(0).item()
        stats['bppix_vae'] = total_kl_vae_bpdim 
        stats['psnr']  = psnr
        if return_rec:
            stats['im_hat'] = im_hat
        return stats

    @torch.no_grad()
    def forward_eval(self, *args, **kwargs):
        """ a dummy function for evaluation """
        return self.forward(*args, **kwargs)

    @torch.no_grad()
    def uncond_sample(self, nhw_repeat, temprature=1.0):
        """ unconditionally sample, ie, generate new images """
        feature = self.decoder.forward_uncond(nhw_repeat, t=temprature)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    @torch.no_grad()
    def cond_sample(self, latents, nhw_repeat=None, temprature=1.0, paint_box=None):
        """ conditional sampling with latents """
        feature = self.decoder.forward_with_latents(latents, nhw_repeat, t=temprature, paint_box=paint_box)
        x_samples = self.out_net.sample(feature, temprature=temprature)
        im_samples = self.process_output(x_samples)
        return im_samples

    def forward_get_latents(self, im):
        """ forward pass and return all the latent variables """
        self.eval() 
        x = self.preprocess_input(im.to(self._dummy.device))
        enc_features = self.encoder(x)
        _, stats_all = self.decoder(enc_features, get_latents=True) 
        return stats_all 

    @torch.no_grad()
    def inpaint(self, im, paint_box, steps=1, temprature=1.0):
        """ Inpainting """
        nB, imC, imH, imW = im.shape
        x1, y1, x2, y2 = paint_box
        h_slice = slice(round(y1*imH), round(y2*imH))
        w_slice = slice(round(x1*imW), round(x2*imW))
        im_input = im.clone()
        for i in range(steps):
            stats_from_current_image = self.forward_get_latents(im_input)
            latents_for_resampling = [st['z'] for st in stats_from_current_image] 

            im_sample = self.cond_sample(latents_for_resampling, temprature=temprature, paint_box=paint_box)
            torch.clamp_(im_sample, min=0, max=1)
            im_input = im.clone() 
            im_input[:, :, h_slice, w_slice] = im_sample[:, :, h_slice, w_slice] 
        return im_sample


    def compress_mode(self, mode=True):
        """ Prepare for entropy coding. """
        if mode:
            self.decoder.update() 
            if hasattr(self.out_net, 'compress'):
                self.out_net.update()
        self.compressing = mode

    @torch.no_grad()
    def compress(self, im):
        """ compress a batch of images """
        x = self.preprocess_input(im.to(self._dummy.device))
        enc_features = self.encoder(x)
        compressed_obj_from_decoder, feature = self.decoder.compress(enc_features)
        
        min_res = min(enc_features.keys())
        compressed_obj_from_decoder.append(tuple(enc_features[min_res].shape)) 
        
        if hasattr(self.out_net, 'compress'): 
            x_tgt = self.preprocess_target(im)
            final_out_net_strings = self.out_net.compress(feature, x_tgt)
            compressed_obj_from_decoder.append(final_out_net_strings)
        return compressed_obj_from_decoder

    @torch.no_grad()
    def decompress(self, compressed_object_list: list):
        """ decompress a compressed_object_list """
        if hasattr(self.out_net, 'compress'): 
            decoder_input_list = compressed_object_list[:-1] 
            out_net_strings = compressed_object_list[-1]
            
            feature = self.decoder.decompress(decoder_input_list)
            x_hat = self.out_net.decompress(feature, out_net_strings)
        else: 
            feature = self.decoder.decompress(compressed_object_list)
            x_hat = self.out_net.mean(feature) 
        im_hat = self.process_output(x_hat)
        return im_hat

    @torch.no_grad()
    def compress_file(self, img_path, output_path):
        """ Compress an image file and save to `output_path` """
        self.eval()
        img = Image.open(img_path)
        original_size = (img.height, img.width)
        img_padded = pad_divisible_by(img, div=self.max_stride)
        im_tensor = tvf.to_tensor(img_padded).unsqueeze_(0)
        
        compressed_obj_list = self.compress(im_tensor) 
        
        data_to_save = {"compressed_payload": compressed_obj_list, "original_size": original_size}
        
        with open(output_path, 'wb') as f:
            pickle.dump(data_to_save, file=f)

    @torch.no_grad()
    def decompress_file(self, bits_path):
        """ Decompress a bits file """
        self.eval()
        with open(bits_path, 'rb') as f:
            saved_data = pickle.load(file=f)
        
        compressed_obj_list = saved_data["compressed_payload"]
        img_h, img_w = saved_data["original_size"]
        
        im_hat_tensor = self.decompress(compressed_obj_list) 
        
        im_hat_cropped = im_hat_tensor[:, :, :img_h, :img_w]
        im_pil = tvf.to_pil_image(im_hat_cropped.squeeze(0).cpu())
        return im_pil


def pad_divisible_by(img, div=64):
    """ Pad an PIL.Image at right and bottom border """
    h_old, w_old = img.height, img.width
    
    h_target = math.ceil(h_old / div) * div
    w_target = math.ceil(w_old / div) * div

    if h_old == h_target and w_old == w_target:
        return img

    padding = (0, 0, (w_target - w_old), (h_target - h_old))
    padded_img = tvf.pad(img, padding=padding, padding_mode='edge')
    return padded_img