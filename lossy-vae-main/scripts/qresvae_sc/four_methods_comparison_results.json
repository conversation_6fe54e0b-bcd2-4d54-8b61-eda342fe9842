{"WebP+LDPC+16QAM": {"method": "WebP-80+LDPC+16QAM", "psnr": 37.7340647859075, "compress_time": 0.09796953201293945, "decompress_time": 0.02391362190246582, "ldpc_qam_time": 13.377504348754883, "total_time": 13.376558303833008, "compressed_size": 32992, "transmission_size": 66000.0, "quality": 80, "snr_db": 20.0, "compression_ratio": 17.873454545454546, "compression_rate": 94.40511067708334, "bpp": 1.3427734375, "original_size_bytes": 1179648, "compressed_size_bytes": 66000.0, "space_saved_bytes": 1113648.0, "original_compressed_size": 32992, "ldpc_encoded_bits": 528000, "ldpc_overhead": 2.0004849660523765, "code_rate": 0.5, "original_bits": 263936, "encoded_bits": 528000, "symbols": 132000, "bit_errors": 0, "ber": 0.0, "encode_time": 2.441601276397705, "modulate_time": 1.4331810474395752, "channel_time": 0.021070003509521484, "demodulate_time": 4.82202672958374, "decode_time": 4.658679246902466}, "JPEG-50+LDPC+16QAM": {"method": "JPEG-50+LDPC+16QAM", "psnr": 34.52806563301249, "compress_time": 0.012991905212402344, "decompress_time": 0.00740361213684082, "ldpc_qam_time": 1.670363426208496, "total_time": 1.668915033340454, "jpeg_size": 28920, "transmission_size": 58000.0, "jpeg_quality": 50, "snr_db": 20.0, "compression_ratio": 20.338758620689656, "compression_rate": 95.08327907986111, "bpp": 1.1800130208333333, "original_size_bytes": 1179648, "compressed_size_bytes": 58000.0, "space_saved_bytes": 1121648.0, "original_compressed_size": 28920, "ldpc_encoded_bits": 464000, "ldpc_overhead": 2.0055325034578146, "code_rate": 0.5, "original_bits": 231360, "encoded_bits": 464000, "symbols": 116000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.02902960777282715, "modulate_time": 0.01822209358215332, "channel_time": 0.02861785888671875, "demodulate_time": 0.018498659133911133, "decode_time": 1.5745468139648438}, "JPEG-75+LDPC+16QAM": {"method": "JPEG-75+LDPC+16QAM", "psnr": 36.70149534784471, "compress_time": 0.015661001205444336, "decompress_time": 0.00900578498840332, "ldpc_qam_time": 1.6578011512756348, "total_time": 1.656097650527954, "jpeg_size": 45639, "transmission_size": 91500.0, "jpeg_quality": 75, "snr_db": 20.0, "compression_ratio": 12.892327868852458, "compression_rate": 92.24344889322916, "bpp": 1.861572265625, "original_size_bytes": 1179648, "compressed_size_bytes": 91500.0, "space_saved_bytes": 1088148.0, "original_compressed_size": 45639, "ldpc_encoded_bits": 732000, "ldpc_overhead": 2.0048642608295535, "code_rate": 0.5, "original_bits": 365112, "encoded_bits": 732000, "symbols": 183000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.036603689193725586, "modulate_time": 0.018072128295898438, "channel_time": 0.04559755325317383, "demodulate_time": 0.020621776580810547, "decode_time": 1.5352025032043457}, "JPEG-85+LDPC+16QAM": {"method": "JPEG-85+LDPC+16QAM", "psnr": 38.38487378462513, "compress_time": 0.013678789138793945, "decompress_time": 0.01202082633972168, "ldpc_qam_time": 1.693389654159546, "total_time": 1.6861441135406494, "jpeg_size": 65004, "transmission_size": 130250.0, "jpeg_quality": 85, "snr_db": 20.0, "compression_ratio": 9.056798464491363, "compression_rate": 88.95857069227431, "bpp": 2.6499430338541665, "original_size_bytes": 1179648, "compressed_size_bytes": 130250.0, "space_saved_bytes": 1049398.0, "original_compressed_size": 65004, "ldpc_encoded_bits": 1042000, "ldpc_overhead": 2.0037228478247493, "code_rate": 0.5, "original_bits": 520032, "encoded_bits": 1042000, "symbols": 260500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.027977466583251953, "modulate_time": 0.01324772834777832, "channel_time": 0.045639991760253906, "demodulate_time": 0.02121448516845703, "decode_time": 1.5780644416809082}, "BPG-20+LDPC+16QAM": {"method": "BPG-20+LDPC+16QAM", "psnr": 41.711893113334924, "compress_time": 1.943265676498413, "decompress_time": 0.85280442237854, "ldpc_qam_time": 1.7157626152038574, "total_time": 1.7134573459625244, "compressed_size": 64346, "transmission_size": 128750.0, "quality": 20, "snr_db": 20.0, "compression_ratio": 9.162314563106795, "compression_rate": 89.08572726779514, "bpp": 2.6194254557291665, "original_size_bytes": 1179648, "compressed_size_bytes": 128750.0, "space_saved_bytes": 1050898.0, "original_compressed_size": 64346, "ldpc_encoded_bits": 1030000, "ldpc_overhead": 2.000901376930967, "code_rate": 0.5, "original_bits": 514768, "encoded_bits": 1030000, "symbols": 257500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.043624162673950195, "modulate_time": 0.024794816970825195, "channel_time": 0.07563447952270508, "demodulate_time": 0.024667978286743164, "decode_time": 1.5447359085083008}, "BPG-30+LDPC+16QAM": {"method": "BPG-30+LDPC+16QAM", "psnr": 37.049052677577876, "compress_time": 1.182753324508667, "decompress_time": 0.4152259826660156, "ldpc_qam_time": 1.7848923206329346, "total_time": 1.783994436264038, "compressed_size": 20097, "transmission_size": 40250.0, "quality": 30, "snr_db": 20.0, "compression_ratio": 29.308024844720496, "compression_rate": 96.58796522352431, "bpp": 0.8188883463541666, "original_size_bytes": 1179648, "compressed_size_bytes": 40250.0, "space_saved_bytes": 1139398.0, "original_compressed_size": 20097, "ldpc_encoded_bits": 322000, "ldpc_overhead": 2.0027864855451063, "code_rate": 0.5, "original_bits": 160776, "encoded_bits": 322000, "symbols": 80500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.025195837020874023, "modulate_time": 0.017421722412109375, "channel_time": 0.022365093231201172, "demodulate_time": 0.01699352264404297, "decode_time": 1.7020182609558105}, "BPG-40+LDPC+16QAM": {"method": "BPG-40+LDPC+16QAM", "psnr": 31.746750261977894, "compress_time": 0.8113338947296143, "decompress_time": 0.4734499454498291, "ldpc_qam_time": 1.5734138488769531, "total_time": 1.5730035305023193, "compressed_size": 6088, "transmission_size": 12250.0, "quality": 40, "snr_db": 20.0, "compression_ratio": 96.29779591836734, "compression_rate": 98.96155463324654, "bpp": 0.24922688802083334, "original_size_bytes": 1179648, "compressed_size_bytes": 12250.0, "space_saved_bytes": 1167398.0, "original_compressed_size": 6088, "ldpc_encoded_bits": 98000, "ldpc_overhead": 2.01215505913272, "code_rate": 0.5, "original_bits": 48704, "encoded_bits": 98000, "symbols": 24500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.018219709396362305, "modulate_time": 0.009912490844726562, "channel_time": 0.004736185073852539, "demodulate_time": 0.007263660430908203, "decode_time": 1.5328714847564697}, "AVIF-20+LDPC+16QAM": {"method": "AVIF-20+LDPC+16QAM", "psnr": 30.439659978024135, "compress_time": 0.5077764987945557, "decompress_time": 0.03287172317504883, "ldpc_qam_time": 1.6822021007537842, "total_time": 1.6818110942840576, "compressed_size": 5126, "transmission_size": 10500.0, "quality": 20, "format_used": "AVIF", "snr_db": 20.0, "compression_ratio": 112.34742857142857, "compression_rate": 99.10990397135416, "bpp": 0.213623046875, "original_size_bytes": 1179648, "compressed_size_bytes": 10500.0, "space_saved_bytes": 1169148.0, "original_compressed_size": 5126, "ldpc_encoded_bits": 84000, "ldpc_overhead": 2.048380803745611, "code_rate": 0.5, "original_bits": 41008, "encoded_bits": 84000, "symbols": 21000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.023360729217529297, "modulate_time": 0.012132644653320312, "channel_time": 0.006110668182373047, "demodulate_time": 0.010616302490234375, "decode_time": 1.6295907497406006}, "AVIF-30+LDPC+16QAM": {"method": "AVIF-30+LDPC+16QAM", "psnr": 32.24851789799001, "compress_time": 0.6099462509155273, "decompress_time": 0.023079872131347656, "ldpc_qam_time": 1.7071828842163086, "total_time": 1.706697940826416, "compressed_size": 7852, "transmission_size": 15750.0, "quality": 30, "format_used": "AVIF", "snr_db": 20.0, "compression_ratio": 74.89828571428572, "compression_rate": 98.66485595703125, "bpp": 0.3204345703125, "original_size_bytes": 1179648, "compressed_size_bytes": 15750.0, "space_saved_bytes": 1163898.0, "original_compressed_size": 7852, "ldpc_encoded_bits": 126000, "ldpc_overhead": 2.0058583800305656, "code_rate": 0.5, "original_bits": 62816, "encoded_bits": 126000, "symbols": 31500, "bit_errors": 0, "ber": 0.0, "encode_time": 0.020606279373168945, "modulate_time": 0.012269020080566406, "channel_time": 0.007630109786987305, "demodulate_time": 0.010682344436645508, "decode_time": 1.6555101871490479}, "AVIF-40+LDPC+16QAM": {"method": "AVIF-40+LDPC+16QAM", "psnr": 34.11285744767588, "compress_time": 0.6523687839508057, "decompress_time": 0.02529287338256836, "ldpc_qam_time": 1.7551984786987305, "total_time": 1.7545769214630127, "compressed_size": 11729, "transmission_size": 23500.0, "quality": 40, "format_used": "AVIF", "snr_db": 20.0, "compression_ratio": 50.19778723404255, "compression_rate": 98.00788031684029, "bpp": 0.4781087239583333, "original_size_bytes": 1179648, "compressed_size_bytes": 23500.0, "space_saved_bytes": 1156148.0, "original_compressed_size": 11729, "ldpc_encoded_bits": 188000, "ldpc_overhead": 2.0035808679341804, "code_rate": 0.5, "original_bits": 93832, "encoded_bits": 188000, "symbols": 47000, "bit_errors": 0, "ber": 0.0, "encode_time": 0.02398967742919922, "modulate_time": 0.015160083770751953, "channel_time": 0.01178431510925293, "demodulate_time": 0.012194633483886719, "decode_time": 1.6914482116699219}, "VAE+QAM": {"method": "VAE+QAM", "psnr": 22.74944171976476, "compress_time": 3.2845077514648438, "decompress_time": 1.2413809299468994, "total_time": 4.525888681411743, "snr_db": 20.0, "compression_ratio": 9.04860088365243, "bpp": 2.65234375, "compression_rate": 88.94856770833334, "total_symbols": 226560, "total_bits": 1042944.0, "transmission_size": 130368.0}}